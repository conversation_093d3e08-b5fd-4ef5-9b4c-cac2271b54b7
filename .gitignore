# SpecStory explanation file
.specstory/.what-is-this.md

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.yarn-integrity

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Build and dist
dist/
build/
out/
.next/
.nuxt/
.output/

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Logs
logs/
*.log

# Testing
coverage/
.nyc_output/

# Cache
.cache/
.temp/
.tmp/
.eslintcache
.stylelintcache

# Misc
*.pid
*.seed
*.pid.lock
