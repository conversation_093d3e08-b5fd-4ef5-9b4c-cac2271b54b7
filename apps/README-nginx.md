# RealMaster 开发环境 Nginx 配置

## 🎯 目的

基于你的实际goupload配置，提供静态文件服务让前端访问视频和图片。API请求继续直接访问Go后端。

## 📁 文件说明

- `nginx.conf` - 基于config.toml的nginx配置
- `start-nginx.sh` - 自动创建目录的启动脚本  
- `README-nginx.md` - 本说明文件

## 🚀 快速启动

直接运行启动脚本即可：

```bash
./start-nginx.sh
```

脚本会自动：
- 检查nginx安装
- 创建所需的存储目录
- 设置正确的权限
- 启动nginx服务

## 🗂️ 存储目录结构

基于你的 `config.toml` 配置：

### 草稿阶段 (用户上传的原始文件)
```
/var/www/draft/rm_video_drafts/     # 视频草稿
/var/www/draft/rm_thumbnail_drafts/ # 缩略图草稿
```

### 最终阶段 (Worker处理后的文件)
```
/var/www/media/rm_videos/      # 最终视频文件 (包含HLS流)
/var/www/media/rm_thumbnails/  # 最终缩略图
/var/www/media/rm_avatars/     # 客户头像
```

## 🌐 URL路径映射

### 草稿文件访问
```
http://localhost:3000/draft/videos/USER/2025-28/abc123/video.mp4
http://localhost:3000/draft/thumbnails/USER/2025-28/abc123/thumb.jpg
```

### 最终文件访问  
```
http://localhost:3000/media/videos/USER/2025-28/abc123/video.m3u8
http://localhost:3000/media/videos/USER/2025-28/abc123/segment001.ts
http://localhost:3000/media/thumbnails/USER/2025-28/abc123/thumb.jpg
http://localhost:3000/media/avatars/USER/2025-28/abc123/avatar.jpg
```

## 🔧 配置详解

### 端口配置
- **nginx静态文件服务**: 端口 `3000`
- **Go后端API**: 端口 `8080` (config.toml中配置)

### 特殊处理
- **HLS视频流**: 自动处理 `.m3u8` 和 `.ts` 文件，添加无缓存头
- **Range请求**: 支持视频拖拽播放
- **跨域访问**: 完整的CORS支持

### goupload集成
配置完全基于你的 `config.toml`：
- site: "TEST"
- entryName: video_draft, thumbnail_draft, video_final, thumbnail_final, client_avatar
- 对应的prefix和storage路径

## 🛠️ 前端配置建议

### M1播放器 (frontend_m1_player)
可以添加媒体文件基础URL：
```javascript
// src/config.js
export const API_BASE_URL = 'http://*************:3000/video/v1';
export const MEDIA_BASE_URL = 'http://localhost:3000/media/';  // 新增
```

### M2管理后台 (frontend_m2_uploader)  
API配置保持不变：
```javascript
export const API_BASE_URL = 'http://localhost:8080';
```

## 🐛 故障排除

### 1. 权限问题
```bash
# 如果遇到权限错误，运行：
sudo chmod -R 755 /var/www/draft/
sudo chmod -R 755 /var/www/media/
```

### 2. 端口被占用
```bash
sudo lsof -i :3000
sudo kill -9 <PID>
```

### 3. 目录不存在
启动脚本会自动创建，如果手动创建：
```bash
sudo mkdir -p /var/www/draft/rm_video_drafts
sudo mkdir -p /var/www/draft/rm_thumbnail_drafts  
sudo mkdir -p /var/www/media/rm_videos
sudo mkdir -p /var/www/media/rm_thumbnails
sudo mkdir -p /var/www/media/rm_avatars
```

## 📝 重要说明

1. **完全基于你的配置**: nginx配置直接对应config.toml中的路径
2. **开发环境专用**: 生产环境需要更严格的安全配置
3. **API保持不变**: Go后端继续在8080端口，前端API调用无需修改
4. **自动目录管理**: 启动脚本会处理所有目录创建和权限设置

## 🔄 停止服务

按 `Ctrl+C` 停止，或：
```bash
sudo pkill -f "nginx.*nginx.conf"
```
