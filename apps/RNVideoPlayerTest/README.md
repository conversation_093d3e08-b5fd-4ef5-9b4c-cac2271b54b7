# React Native Video Player Test

这是一个用于测试 m1 v2 视频播放器在 React Native 环境中表现的测试项目。

## 功能特性

- 📱 视频列表页面：展示视频卡片列表
- 🎥 视频播放页面：使用 WebView 集成 m1 v2 播放器
- 🌐 多语言支持：中英文切换
- 📱 全屏模式：支持横屏全屏播放
- 🔄 双向通信：React Native 与 WebView 之间的消息传递

## 项目结构

```
src/
├── data/
│   └── mockData.js          # 模拟数据（从 m1 v2 迁移）
├── screens/
│   ├── VideoListScreen.js   # 视频列表页面
│   └── VideoPlayerScreen.js # 视频播放页面
```

## 安装和运行

### 1. 配置网络访问

**重要：** 为了在真机上测试，需要配置局域网访问。

1. 找到你的电脑 IP 地址：
   - **Windows**: 打开命令提示符，输入 `ipconfig`，查找 "IPv4 地址"
   - **Mac**: 打开终端，输入 `ifconfig | grep "inet " | grep -v 127.0.0.1`
   - **Linux**: 打开终端，输入 `hostname -I`

2. 更新配置文件 `src/config/config.js`：
   ```javascript
   const LOCAL_IP = '*************'; // 替换为你的实际 IP 地址
   ```

### 2. 启动 m1 v2 项目

确保 m1 v2 项目在局域网中可访问：
```bash
cd ../frontend_m1_player_v2
npm run dev -- --host 0.0.0.0
```

### 3. 安装依赖并运行

```bash
npm install
npm start
```

选择运行平台：
- iOS: `i`
- Android: `a`
- Web: `w`

## 使用说明

### 视频列表页面
- 点击右上角语言按钮切换中英文
- 点击任意视频卡片进入播放页面

### 视频播放页面
- 左上角返回按钮返回列表页面
- 支持 m1 v2 的所有手势操作
- 全屏模式下自动隐藏返回按钮并横屏显示

### 消息通信

#### React Native → WebView
```javascript
// 语言切换
{
  type: 'LANGUAGE_CHANGE',
  language: 'zh' // 或 'en'
}
```

#### WebView → React Native
```javascript
// 全屏状态变化
{
  type: 'FULLSCREEN_CHANGE',
  isFullscreen: true,
  orientation: 'landscape'
}

// 视频播放状态
{
  type: 'VIDEO_PLAY' | 'VIDEO_PAUSE' | 'VIDEO_ENDED'
}

// 视频切换
{
  type: 'VIDEO_CHANGED',
  index: 1
}
```

## 注意事项

1. **m1 v2 URL**: 确保 VideoPlayerScreen.js 中的 playerUrl 指向正确的 m1 v2 开发服务器
2. **网络权限**: 在真机测试时，确保允许 HTTP 请求（开发环境）
3. **屏幕旋转**: 需要在 app.json 中配置屏幕旋转权限

## 开发说明

- 所有 mock 数据已从 m1 v2 迁移到 `src/data/mockData.js`
- m1 v2 现在支持多语言标题和描述
- 添加了语言切换的消息监听机制
- 支持全屏模式的自动屏幕旋转

## 故障排除

1. **WebView 无法加载**: 检查 m1 v2 是否正在运行，URL 是否正确
2. **全屏不工作**: 确保安装了 expo-screen-orientation
3. **语言切换无效**: 检查 m1 v2 的消息监听是否正常工作
