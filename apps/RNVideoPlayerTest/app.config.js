const path = require('path');
const dotenv = require('dotenv');

// 加载根目录下的多种环境文件（按优先级覆盖）
const rootDir = path.resolve(__dirname, '../../');
['.env', '.env.development', '.env.local', '.env.development.local'].forEach((name) => {
  dotenv.config({ path: path.join(rootDir, name) });
});

module.exports = ({ config }) => {
  const nodeEnv = process.env.NODE_ENV || 'development';

  // 开发下保留回退，生产必须提供
  const devFallbackApi = 'http://192.168.2.162:8080';
  const devFallbackMedia = 'http://192.168.2.162:3000';

  const apiBaseUrl = process.env.API_BASE_URL || process.env.EXPO_PUBLIC_API_BASE_URL || (nodeEnv === 'production' ? '' : devFallbackApi);
  const mediaBaseUrl = process.env.MEDIA_BASE_URL || process.env.EXPO_PUBLIC_MEDIA_BASE_URL || (nodeEnv === 'production' ? '' : devFallbackMedia);

  if (nodeEnv === 'production') {
    if (!apiBaseUrl || !mediaBaseUrl) {
      throw new Error('Missing API_BASE_URL or MEDIA_BASE_URL for production build. Set them in root .env.production or CI env.');
    }
  }

  const debugMode = String(process.env.DEBUG_MODE ?? (nodeEnv !== 'production'));

  return {
    ...config,
    extra: {
      ...(config.extra || {}),
      apiBaseUrl,
      mediaBaseUrl,
      nodeEnv,
      debugMode,
    },
  };
}; 