#!/usr/bin/env node

/**
 * Script to check if m1 v2 server is accessible
 */

const http = require('http');
const { M1_V2_CONFIG } = require('../src/config/config');

function checkUrl(url) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      resolve({
        url,
        status: res.statusCode,
        accessible: res.statusCode === 200
      });
    });

    req.on('error', (err) => {
      resolve({
        url,
        status: 'ERROR',
        accessible: false,
        error: err.message
      });
    });

    req.on('timeout', () => {
      resolve({
        url,
        status: 'TIMEOUT',
        accessible: false,
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function main() {
  console.log('🔍 Checking m1 v2 server accessibility...\n');

  const urls = [
    M1_V2_CONFIG.DEV_LOCALHOST,
    M1_V2_CONFIG.DEV_LAN,
  ];

  for (const url of urls) {
    console.log(`Checking: ${url}`);
    const result = await checkUrl(url);
    
    if (result.accessible) {
      console.log(`✅ Accessible (Status: ${result.status})`);
    } else {
      console.log(`❌ Not accessible (${result.status})`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    }
    console.log('');
  }

  console.log('📱 Current configuration:');
  console.log(`   Development URL: ${M1_V2_CONFIG.URL}`);
  console.log(`   Production URL: ${M1_V2_CONFIG.PRODUCTION}`);
  console.log('');

  console.log('💡 Tips:');
  console.log('   - Make sure m1 v2 is running with: npm run dev -- --host 0.0.0.0');
  console.log('   - Update LOCAL_IP in src/config/config.js with your actual IP address');
  console.log('   - Check firewall settings if LAN access fails');
}

main().catch(console.error);
