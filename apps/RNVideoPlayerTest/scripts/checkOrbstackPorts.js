#!/usr/bin/env node

/**
 * Check if OrbStack port forwarding is working correctly
 */

const http = require('http');
const { execSync } = require('child_process');

function checkPort(host, port, name) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: host,
      port: port,
      path: '/',
      method: 'GET',
      timeout: 3000
    }, (res) => {
      resolve({
        name,
        host,
        port,
        status: res.statusCode,
        accessible: true
      });
    });

    req.on('error', (err) => {
      resolve({
        name,
        host,
        port,
        status: 'ERROR',
        accessible: false,
        error: err.message
      });
    });

    req.on('timeout', () => {
      resolve({
        name,
        host,
        port,
        status: 'TIMEOUT',
        accessible: false,
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function main() {
  console.log('🐳 OrbStack 端口转发检查\n');

  // Get VM IP
  let vmIP;
  try {
    vmIP = execSync('hostname -I', { encoding: 'utf8' }).trim().split(' ')[0];
    console.log(`🖥️  虚拟机 IP: ${vmIP}`);
  } catch {
    console.log('❌ 无法获取虚拟机 IP');
    return;
  }

  console.log('\n🔍 检查端口访问性...\n');

  const checks = [
    { host: 'localhost', port: 5174, name: 'M1 V2 (localhost)' },
    { host: vmIP, port: 5174, name: 'M1 V2 (VM IP)' },
    { host: 'localhost', port: 8081, name: 'Expo (localhost)' },
    { host: vmIP, port: 8081, name: 'Expo (VM IP)' },
  ];

  for (const check of checks) {
    const result = await checkPort(check.host, check.port, check.name);
    
    if (result.accessible) {
      console.log(`✅ ${result.name}: http://${result.host}:${result.port}`);
    } else {
      console.log(`❌ ${result.name}: ${result.error || result.status}`);
    }
  }

  console.log('\n📱 iPhone 测试说明:');
  console.log('1. 获取 Mac 的 WiFi IP 地址');
  console.log('2. 在 iPhone Safari 中测试: http://[Mac-IP]:5174');
  console.log('3. 如果可以访问，说明 OrbStack 端口转发正常');
  console.log('4. 然后在 Expo Go 中使用: exp://[Mac-IP]:8081');

  console.log('\n💡 提示:');
  console.log('- 确保服务使用 --host 0.0.0.0 启动');
  console.log('- 检查 Mac 防火墙设置');
  console.log('- 确认 iPhone 和 Mac 在同一 WiFi 网络');
}

main().catch(console.error);
