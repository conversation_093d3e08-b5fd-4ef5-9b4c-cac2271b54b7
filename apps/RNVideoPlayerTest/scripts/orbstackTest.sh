#!/usr/bin/env bash

set -euo pipefail

echo "🐳 OrbStack 测试指南"

cat <<EOF
1. 启动 M1 V2（Vite）开发服务器：
   cd apps/frontend_m1_player_v2
   npm run dev -- --host 0.0.0.0 --port 5174

2. OrbStack 会将虚拟机 5174 端口转发到 Mac。
   在 Mac 浏览器中测试:  http://localhost:5174
   在 iPhone Safari 中测试: http://[Mac-IP]:5174

3. 启动 Expo（LAN 模式）：
   cd apps/RNVideoPlayerTest
   npm run start
   或确保执行：EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 npx expo start --host lan

4. 如果手机扫码后无法连接：
   - 确保手机与 Mac 在同一 WiFi
   - 临时使用隧道模式：npx expo start --tunnel

5. 常见端口：
   - Vite（M1V2）：5174
   - Expo：8081
EOF
