#!/usr/bin/env node

/**
 * Start Expo with LAN host derived from root .env (API_BASE_URL)
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

function getHostFromEnv() {
  // Try reading repository root .env for API_BASE_URL
  const rootDir = path.resolve(__dirname, '../../');
  const envPaths = [
    path.join(rootDir, '.env'),
    path.join(rootDir, '.env.development'),
    path.join(rootDir, '.env.local'),
    path.join(rootDir, '.env.development.local'),
  ];

  for (const p of envPaths) {
    try {
      if (fs.existsSync(p)) {
        const content = fs.readFileSync(p, 'utf8');
        const m = content.match(/API_BASE_URL\s*=\s*(.+)/);
        if (m && m[1]) {
          try {
            const url = new URL(m[1].trim());
            return url.hostname;
          } catch {}
        }
      }
    } catch {}
  }
  return null;
}

function main() {
  const host = getHostFromEnv();
  if (!host) {
    console.warn('⚠️ 未能从根 .env 解析 API_BASE_URL 主机名，使用 LAN 模式默认设置');
  }

  const env = {
    ...process.env,
    EXPO_DEVTOOLS_LISTEN_ADDRESS: '0.0.0.0',
    ...(host ? { REACT_NATIVE_PACKAGER_HOSTNAME: host } : {}),
  };

  const args = ['start', '--host', 'lan', '--port', '8081'];

  console.log(`Running: expo ${args.join(' ')}`);
  if (host) {
    console.log(`Environment: REACT_NATIVE_PACKAGER_HOSTNAME=${host}`);
  }

  const expoProcess = spawn('npx', ['expo', ...args], {
    env,
    stdio: 'inherit',
    cwd: process.cwd(),
  });

  expoProcess.on('error', (error) => {
    console.error('Failed to start Expo:', error.message);
    process.exit(1);
  });

  expoProcess.on('close', (code) => {
    console.log(`Expo process exited with code ${code}`);
    process.exit(code);
  });

  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping Expo...');
    expoProcess.kill('SIGINT');
  });
}

main();
