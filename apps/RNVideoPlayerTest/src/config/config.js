import Constants from 'expo-constants';

/**
 * Configuration file for the React Native Video Player Test app
 */

const nodeEnv = Constants?.expoConfig?.extra?.nodeEnv || 'development';

// 优先从 Expo extra 注入的配置读取；开发下保留回退，生产必须提供
const DEV_FALLBACK_API = 'http://*************:8080';
const DEV_FALLBACK_MEDIA = 'http://*************:3000';

const API_BASE_URL = (Constants?.expoConfig?.extra?.apiBaseUrl) || (nodeEnv === 'production' ? '' : DEV_FALLBACK_API);
const MEDIA_BASE_URL = (Constants?.expoConfig?.extra?.mediaBaseUrl) || (nodeEnv === 'production' ? '' : DEV_FALLBACK_MEDIA);

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  MEDIA_BASE_URL: MEDIA_BASE_URL,
  TIMEOUT: 10000,
};

function resolveHostFromUrl(url) {
  try {
    return new URL(url).hostname;
  } catch {
    return 'localhost';
  }
}
const LAN_HOST = resolveHostFromUrl(API_BASE_URL || 'http://localhost');

export const M1_V2_CONFIG = {
  DEV_LOCALHOST: 'http://localhost:5174',
  DEV_LAN: `http://${LAN_HOST}:5174`,
  PRODUCTION: Constants?.expoConfig?.extra?.m1V2PublicUrl || 'https://your-production-url.com',
  get URL() {
    if (__DEV__) {
      return this.DEV_LAN;
    }
    return this.PRODUCTION;
  },
};

export const NETWORK_CONFIG = {
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

export const APP_CONFIG = {
  DEFAULT_LANGUAGE: 'en',
  SUPPORTED_LANGUAGES: ['en', 'zh'],
  CONTROLS_AUTO_HIDE_TIMEOUT: 4000,
  VIDEO_PLAYER: {
    HARDWARE_ACCELERATION: true,
    ALLOW_INLINE_PLAYBACK: true,
    REQUIRE_USER_ACTION: false,
  },
};

export const DEBUG_CONFIG = {
  ENABLE_LOGGING: __DEV__,
  ENABLE_WEBVIEW_DEBUG: __DEV__,
  SHOW_NETWORK_LOGS: __DEV__,
};

export const getIPInstructions = () => {
  return `\nUpdate API_BASE_URL and MEDIA_BASE_URL in the repository root .env to change endpoints for both RN and M1V2.\n`;
};

export default {
  M1_V2_CONFIG,
  NETWORK_CONFIG,
  APP_CONFIG,
  DEBUG_CONFIG,
  getIPInstructions,
};
