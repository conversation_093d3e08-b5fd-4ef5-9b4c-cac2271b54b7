/**
 * Configuration file for the React Native Video Player Test app
 */

// Network configuration for different environments
// For OrbStack Linux VM: Use Mac host IP address
const MAC_HOST_IP = '*************';  // Mac's main network IP
// const MAC_HOST_IP = '**************';
const ORBSTACK_VM_IP = '**************';  // OrbStack VM IP

// Determine which IP to use based on environment
// Both RN and M1 V2 are running in OrbStack, use OrbStack VM IP
const LOCAL_IP = MAC_HOST_IP; // Use Mac host IP for backend services

// API and Media configuration
export const API_CONFIG = {
  BASE_URL: `http://${LOCAL_IP}:8080`,
  MEDIA_BASE_URL: `http://${LOCAL_IP}:3000`,
  TIMEOUT: 10000,
};

// M1 V2 Player configuration
export const M1_V2_CONFIG = {
  // Development URLs
  DEV_LOCALHOST: 'http://localhost:5174',
  DEV_LAN: `http://${LOCAL_IP}:5174`,
  
  // Production URL (update when deploying)
  PRODUCTION: 'https://your-production-url.com',
  
  // Current URL based on environment
  get URL() {
    if (__DEV__) {
      // In development, use LAN URL for device testing
      return this.DEV_LAN;
    } else {
      return this.PRODUCTION;
    }
  }
};

// Network configuration
export const NETWORK_CONFIG = {
  // Timeout for network requests (in milliseconds)
  TIMEOUT: 10000,
  
  // Retry attempts for failed requests
  RETRY_ATTEMPTS: 3,
  
  // Delay between retry attempts (in milliseconds)
  RETRY_DELAY: 1000,
};

// App configuration
export const APP_CONFIG = {
  // Default language
  DEFAULT_LANGUAGE: 'en',
  
  // Supported languages
  SUPPORTED_LANGUAGES: ['en', 'zh'],
  
  // Auto-hide controls timeout (in milliseconds)
  CONTROLS_AUTO_HIDE_TIMEOUT: 4000,
  
  // Video player settings
  VIDEO_PLAYER: {
    // Enable hardware acceleration
    HARDWARE_ACCELERATION: true,
    
    // Allow inline media playback
    ALLOW_INLINE_PLAYBACK: true,
    
    // Require user action for media playback
    REQUIRE_USER_ACTION: false,
  }
};

// Debug configuration
export const DEBUG_CONFIG = {
  // Enable console logging
  ENABLE_LOGGING: __DEV__,
  
  // Enable WebView debugging
  ENABLE_WEBVIEW_DEBUG: __DEV__,
  
  // Show network request logs
  SHOW_NETWORK_LOGS: __DEV__,
};

// Helper function to get current IP address instructions
export const getIPInstructions = () => {
  return `
To find your computer's IP address:

Windows:
1. Open Command Prompt
2. Type: ipconfig
3. Look for "IPv4 Address" under your network adapter

Mac:
1. Open Terminal
2. Type: ifconfig | grep "inet " | grep -v 127.0.0.1
3. Use the IP address shown (usually starts with 192.168 or 10.0)

Linux:
1. Open Terminal
2. Type: hostname -I
3. Use the first IP address shown

Then update LOCAL_IP in src/config/config.js with your IP address.
`;
};

export default {
  M1_V2_CONFIG,
  NETWORK_CONFIG,
  APP_CONFIG,
  DEBUG_CONFIG,
  getIPInstructions,
};
