// Mock video data migrated from m1 v2
export const mockVideos = [
  {
    id: 1,
    title: {
      zh: '多伦多市中心豪华公寓',
      en: 'Luxury Condo Tour in Downtown Toronto'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    description: {
      zh: '探索位于市中心核心地带的这套令人惊叹的豪华公寓，现代设计与城市便利完美结合。',
      en: 'A giant rabbit with a heart bigger than himself. A story about friendship, courage, and a whole lot of carrots. This is a longer description to test the expand and collapse functionality.'
    },
    coverImage: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=600',
    stats: { likes: '10.2k', collections: '1.8k' },
    userState: { isLiked: false, isCollected: true },
    propertyIds: ['prop-123', 'prop-456', 'prop-789', 'prop-abc', 'prop-def'],
    width: 1920,
    height: 1080
  },
  {
    id: 2,
    title: {
      zh: '温哥华海景别墅',
      en: 'Vancouver Oceanview Villa'
    },
    videoUrl: 'https://videos.pexels.com/video-files/4678261/4678261-hd_1080_1920_25fps.mp4',
    description: {
      zh: '体验这座位于温哥华的精美海景别墅，融合现代设计与自然美景。',
      en: 'The first-ever open-source animated short film showcasing beautiful oceanview villa.'
    },
    coverImage: 'https://images.unsplash.com/photo-1580587771525-78b9dba3b914?w=600',
    stats: { likes: '25.6k', collections: '3.2k' },
    userState: { isLiked: true, isCollected: false },
    width: 720,
    height: 1280
  },
  {
    id: 3,
    title: {
      zh: '新手买家看房全攻略',
      en: 'First-Time Home Buyer Guide'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    description: {
      zh: '本视频为新手买家提供看房的实用技巧和注意事项，助您做出明智的购房决策。',
      en: 'A short film by the Blender Institute, a must-watch for all 3D enthusiasts.'
    },
    coverImage: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=600',
    stats: { likes: '5,123', collections: '456' },
    userState: { isLiked: false, isCollected: false },
    propertyIds: ['prop-789'],
    width: 1280,
    height: 720
  },
  {
    id: 4,
    title: {
      zh: '列治文山学区房解析',
      en: 'Richmond Hill School District'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    description: {
      zh: '全面分析列治文山顶级学区及其周边房产特点与投资潜力。',
      en: 'Another beautiful short from the Blender team.'
    },
    coverImage: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600',
    stats: { likes: '9,876', collections: '1,012' },
    userState: { isLiked: true, isCollected: true },
    width: 1080,
    height: 1920
  },
  {
    id: 5,
    title: {
      zh: '房屋贷款政策解读',
      en: 'Mortgage Policy Explained'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    description: {
      zh: '专家为您解读当前最新的房屋贷款政策及其对市场的影响。',
      en: 'A tale of a girl and her dragon.'
    },
    coverImage: 'https://images.unsplash.com/photo-1524503033411-c9566986fc8f?w=600',
    stats: { likes: '15k', collections: '2.5k' },
    userState: { isLiked: false, isCollected: false },
    width: 1920,
    height: 1080
  },
  {
    id: 6,
    title: {
      zh: '房产投资短视频分析',
      en: 'Real Estate Investment Tips'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
    description: {
      zh: '快速了解房产投资的核心要点和注意事项。',
      en: 'Quick tips for real estate investment in vertical format.'
    },
    coverImage: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=600',
    stats: { likes: '8.5k', collections: '1.2k' },
    userState: { isLiked: false, isCollected: false },
    width: 540,
    height: 960
  },
  {
    id: 7,
    title: {
      zh: '社交媒体房产营销',
      en: 'Social Media Property Marketing'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/VolkswagenGTIReview.mp4',
    description: {
      zh: '学习如何在社交媒体上有效推广房产项目。',
      en: 'Learn effective property marketing strategies for social media platforms.'
    },
    coverImage: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=600',
    stats: { likes: '12.3k', collections: '890' },
    userState: { isLiked: true, isCollected: false },
    width: 608,
    height: 1080
  },
  {
    id: 8,
    title: {
      zh: '加拿大房产市场趋势',
      en: 'Canadian Real Estate Market Trends'
    },
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/WhatCarCanYouGetForAGrand.mp4',
    description: {
      zh: '深度分析加拿大房产市场的最新趋势和未来走向。',
      en: 'In-depth analysis of Canadian real estate market trends and future outlook.'
    },
    coverImage: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600',
    stats: { likes: '18.7k', collections: '2.8k' },
    userState: { isLiked: false, isCollected: true },
    width: 1920,
    height: 1080
  }
];

// Mock property data
export const mockProperties = {
  'prop-123': {
    id: 'prop-123',
    searchAddr: '123 Bay Street, Toronto, ON',
    price: '$1,200,000',
    thumbUrl: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300',
    saleOrRent: 'Sale',
    city: 'Toronto',
    prov: 'Ontario',
    cmty: 'Downtown',
    webUrl: '#',
    bedroom: '2',
    bathroom: '2',
    parking: '1'
  },
  'prop-456': {
    id: 'prop-456',
    searchAddr: '456 Queen Street, Toronto, ON',
    price: '$950,000',
    thumbUrl: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=300',
    saleOrRent: 'Sale',
    city: 'Toronto',
    prov: 'Ontario',
    cmty: 'Queen West',
    webUrl: '#',
    bedroom: '1',
    bathroom: '1',
    parking: '0'
  },
  'prop-789': {
    id: 'prop-789',
    searchAddr: '789 King Street, Toronto, ON',
    price: '$2,500,000',
    thumbUrl: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=300',
    saleOrRent: 'Sale',
    city: 'Toronto',
    prov: 'Ontario',
    cmty: 'King West',
    webUrl: '#',
    bedroom: '3',
    bathroom: '2',
    parking: '2'
  }
};

// Simulate API call for fetching more videos
export const fetchMoreVideos = (page = 1) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (page === 1) {
        resolve(mockVideos);
      } else {
        resolve([]); // No more data
      }
    }, 500);
  });
};
