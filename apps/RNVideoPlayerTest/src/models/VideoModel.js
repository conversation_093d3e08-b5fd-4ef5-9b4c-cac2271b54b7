import { NumberFormatter } from '../utils/NumberFormatter';

/**
 * VideoModel - 视频数据模型
 * 负责视频数据的封装、格式化和业务逻辑
 */
export class VideoModel {
  constructor(backendData, locale = 'en') {
    // 基础信息
    this.id = backendData.id;
    this.title = backendData.title || {};
    this.description = backendData.description || {};
    this.status = backendData.status;
    this.duration = backendData.duration || 0;
    this.publishedAt = backendData.publishedAt ? new Date(backendData.publishedAt) : null;

    // 媒体URL - 直接使用后端返回的完整URL，与M2项目保持一致
    this.videoUrl = backendData.previewVideoUrl || '';
    this.coverImage = backendData.previewThumbUrl || '';

    // 保持向后兼容
    this.previewVideoUrl = backendData.previewVideoUrl || '';
    this.previewThumbUrl = backendData.previewThumbUrl || '';

    // 视频尺寸
    this.width = backendData.width || 1920;
    this.height = backendData.height || 1080;

    // 关联数据
    this.uploaderId = backendData.uploaderId;
    this.categoryId = backendData.categoryId;
    this.tags = backendData.tags || [];
    this.propertyIds = backendData.propertyIds || [];
    this.externalUrl = backendData.externalUrl;

    // 统计数据（原始数字）
    this.stats = {
      views: backendData.stats?.views || 0,
      likes: backendData.stats?.likes || 0,
      collections: backendData.stats?.collections || 0,
      completions: backendData.stats?.completions || 0,
      completionRate: backendData.stats?.completionRate || '0%',
    };

    // 用户状态（需要单独获取和更新）
    this.userState = {
      isLiked: false,
      isCollected: false,
      isBlocked: false,
      progressSeconds: 0,
    };

    // 本地化设置
    this.locale = locale;

    // 缓存格式化结果
    this._formattedStatsCache = null;
    this._lastFormattedLocale = null;
  }

  /**
   * 获取本地化文本
   * @param {string} field - 字段名 ('title' 或 'description')
   * @returns {string}
   */
  getLocalizedText(field) {
    const text = this[field];
    
    if (typeof text === 'object' && text !== null) {
      // 优先使用当前语言，然后是英文，最后是中文
      return text[this.locale] || text.en || text.zh || '';
    }
    
    return text || '';
  }

  /**
   * 获取格式化的统计数据
   * @param {string} style - 格式化样式 ('compact', 'full')
   * @returns {Object}
   */
  getFormattedStats(style = 'compact') {
    // 检查缓存
    if (this._formattedStatsCache && 
        this._lastFormattedLocale === this.locale &&
        this._lastFormattedStyle === style) {
      return this._formattedStatsCache;
    }

    const formatted = {
      views: NumberFormatter.format(this.stats.views, { 
        locale: this.locale, 
        style,
        context: 'stats'
      }),
      likes: NumberFormatter.format(this.stats.likes, { 
        locale: this.locale, 
        style,
        context: 'stats'
      }),
      collections: NumberFormatter.format(this.stats.collections, { 
        locale: this.locale, 
        style,
        context: 'stats'
      }),
      completions: NumberFormatter.format(this.stats.completions, { 
        locale: this.locale, 
        style,
        context: 'stats'
      }),
      completionRate: this.stats.completionRate,
    };

    // 缓存结果
    this._formattedStatsCache = formatted;
    this._lastFormattedLocale = this.locale;
    this._lastFormattedStyle = style;

    return formatted;
  }

  /**
   * 获取格式化的时长
   * @param {Object} options - 格式化选项
   * @returns {string}
   */
  getFormattedDuration(options = {}) {
    return NumberFormatter.formatDuration(this.duration, options);
  }

  /**
   * 获取视频方向
   * @returns {string} 'portrait' | 'landscape' | 'square'
   */
  getOrientation() {
    if (this.height > this.width) {
      return 'portrait';
    } else if (this.width > this.height) {
      return 'landscape';
    } else {
      return 'square';
    }
  }

  /**
   * 获取宽高比
   * @returns {number}
   */
  getAspectRatio() {
    return this.height > 0 ? this.width / this.height : 16 / 9;
  }

  /**
   * 判断是否为短视频
   * @param {number} threshold - 时长阈值（秒），默认60秒
   * @returns {boolean}
   */
  isShortVideo(threshold = 60) {
    return this.duration <= threshold;
  }

  /**
   * 判断是否为竖屏视频
   * @returns {boolean}
   */
  isPortrait() {
    return this.getOrientation() === 'portrait';
  }

  /**
   * 判断是否为HLS视频
   * @returns {boolean}
   */
  isHLS() {
    return this.videoUrl.includes('.m3u8');
  }

  /**
   * 更新用户状态
   * @param {Object} stateData - 状态数据
   */
  updateUserState(stateData) {
    this.userState = {
      ...this.userState,
      ...stateData,
    };
  }

  /**
   * 更新统计数据
   * @param {Object} statsData - 统计数据
   */
  updateStats(statsData) {
    this.stats = {
      ...this.stats,
      ...statsData,
    };
    
    // 清除格式化缓存
    this._formattedStatsCache = null;
  }

  /**
   * 切换点赞状态
   * @returns {boolean} 新的点赞状态
   */
  toggleLike() {
    const newLikedState = !this.userState.isLiked;
    this.userState.isLiked = newLikedState;
    
    // 更新统计数据
    if (newLikedState) {
      this.stats.likes += 1;
    } else {
      this.stats.likes = Math.max(0, this.stats.likes - 1);
    }
    
    // 清除格式化缓存
    this._formattedStatsCache = null;
    
    return newLikedState;
  }

  /**
   * 切换收藏状态
   * @returns {boolean} 新的收藏状态
   */
  toggleCollection() {
    const newCollectedState = !this.userState.isCollected;
    this.userState.isCollected = newCollectedState;
    
    // 更新统计数据
    if (newCollectedState) {
      this.stats.collections += 1;
    } else {
      this.stats.collections = Math.max(0, this.stats.collections - 1);
    }
    
    // 清除格式化缓存
    this._formattedStatsCache = null;
    
    return newCollectedState;
  }

  /**
   * 更新观看进度
   * @param {number} progressSeconds - 观看进度（秒）
   */
  updateProgress(progressSeconds) {
    this.userState.progressSeconds = Math.max(0, progressSeconds);
  }

  /**
   * 获取观看进度百分比
   * @returns {number} 0-100的百分比
   */
  getProgressPercentage() {
    if (this.duration <= 0) return 0;
    return Math.min(100, (this.userState.progressSeconds / this.duration) * 100);
  }

  /**
   * 判断是否已观看完成
   * @param {number} threshold - 完成阈值百分比，默认90%
   * @returns {boolean}
   */
  isCompleted(threshold = 90) {
    return this.getProgressPercentage() >= threshold;
  }

  /**
   * 设置语言环境
   * @param {string} locale - 语言代码
   */
  setLocale(locale) {
    if (this.locale !== locale) {
      this.locale = locale;
      // 清除格式化缓存
      this._formattedStatsCache = null;
    }
  }

  /**
   * 转换为前端兼容格式（为了兼容现有代码）
   * @returns {Object}
   */
  toLegacyFormat() {
    const formattedStats = this.getFormattedStats();
    
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      videoUrl: this.videoUrl,
      coverImage: this.coverImage,
      width: this.width,
      height: this.height,
      duration: this.duration,
      stats: {
        likes: formattedStats.likes,
        collections: formattedStats.collections,
        views: formattedStats.views,
      },
      userState: {
        isLiked: this.userState.isLiked,
        isCollected: this.userState.isCollected,
      },
      propertyIds: this.propertyIds,
      publishedAt: this.publishedAt,
    };
  }

  /**
   * 转换为JSON（用于存储或传输）
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      status: this.status,
      duration: this.duration,
      videoUrl: this.videoUrl,
      coverImage: this.coverImage,
      width: this.width,
      height: this.height,
      uploaderId: this.uploaderId,
      categoryId: this.categoryId,
      tags: this.tags,
      propertyIds: this.propertyIds,
      externalUrl: this.externalUrl,
      stats: this.stats,
      userState: this.userState,
      publishedAt: this.publishedAt?.toISOString(),
      locale: this.locale,
    };
  }

  /**
   * 从JSON创建VideoModel实例
   * @param {Object} json - JSON数据
   * @returns {VideoModel}
   */
  static fromJSON(json) {
    const model = new VideoModel(json, json.locale);
    model.userState = json.userState || model.userState;
    return model;
  }

  /**
   * 克隆VideoModel实例
   * @returns {VideoModel}
   */
  clone() {
    return VideoModel.fromJSON(this.toJSON());
  }
}
