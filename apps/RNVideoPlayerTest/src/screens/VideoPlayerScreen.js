import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Text,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { M1_V2_CONFIG } from '../config/config';
import { OrientationManager } from '../utils/orientationUtils';
import { authService } from '../services/AuthService';

const VideoPlayerScreen = ({ route, navigation }) => {
  const { videoIndex, allVideos, currentLanguage } = route.params;
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showBackButton, setShowBackButton] = useState(true);
  const webViewRef = useRef(null);

  // M1 V2 Player URL from configuration
  const playerUrl = M1_V2_CONFIG.URL;

  // This useEffect is firing too early, before the WebView is ready.
  // We will send the language message after the WEBVIEW_READY handshake.
  /*
  useEffect(() => {
    // Send initial language to WebView
    if (webViewRef.current) {
      const message = JSON.stringify({
        type: 'LANGUAGE_CHANGE',
        language: currentLanguage
      });
      webViewRef.current.postMessage(message);
    }
  }, [currentLanguage]);
  */

  // Cleanup orientation when component unmounts
  useEffect(() => {
    return () => {
      // Reset orientation when leaving the screen
      OrientationManager.resetOrientation().catch(error => {
        console.warn('Failed to reset orientation on cleanup:', error.message);
      });
    };
  }, []);

  const handleWebViewMessage = async (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      // console.log('Received message from WebView:', data); // Verbose logging

      switch (data.type) {
        case 'WEBVIEW_READY':
          // The WebView is loaded and ready to receive data
          if (webViewRef.current) {
            console.log('RN: WebView is ready. Sending JWT token, video data and language...');

            // 1. Send JWT Token first
            const currentUser = authService.getCurrentUser();
            if (currentUser?.token) {
              webViewRef.current.postMessage(JSON.stringify({
                type: 'SET_JWT_TOKEN',
                token: currentUser.token
              }));
            } else {
              console.warn('RN: No JWT token available to send to WebView');
            }

            // 2. Send initial video data
            webViewRef.current.postMessage(JSON.stringify({
              type: 'INITIALIZE_PLAYER',
              videoData: allVideos,
              startIndex: videoIndex
            }));

            // 3. Send initial language right after
            webViewRef.current.postMessage(JSON.stringify({
              type: 'LANGUAGE_CHANGE',
              language: currentLanguage
            }));
          }
          break;
        
        case 'FETCH_NEXT_PAGE':
          // The M1 player is requesting more videos for infinite scroll
          console.log(`RN: WebView requesting next page (request id: ${data.id})`);
          // In a real app, you would fetch data from an API here.
          // For this example, we'll return an empty array to signify the end of the list.
          if (webViewRef.current) {
            webViewRef.current.postMessage(JSON.stringify({
              type: 'FETCH_NEXT_PAGE_RESULT',
              id: data.id,
              videos: [] 
            }));
          }
          break;

        case 'FETCH_PROPERTIES':
          // The M1 player should call property API directly, not through RN
          console.log(`RN: WebView requesting properties for ids: ${data.propertyIds.join(', ')}`);
          console.log('RN: M1 V2 should call property API directly, not through RN');

          // Send response telling M1 V2 to call API directly
          if (webViewRef.current) {
            webViewRef.current.postMessage(JSON.stringify({
              type: 'FETCH_PROPERTIES_RESULT',
              id: data.id,
              callApiDirectly: true,
              message: 'M1 V2 should call property API directly'
            }));
          }
          break;

        case 'CONSOLE_LOG':
          // Handle console logs from WebView for debugging
          const logLevel = data.level || 'log';
          const logMessage = data.message || '';
          const logArgs = data.args || [];
          console.log(`[WebView ${logLevel.toUpperCase()}]`, logMessage, ...logArgs);
          break;

        case 'FULLSCREEN_CHANGE':
          if (data.isFullscreen) {
            // Enter fullscreen
            setIsFullscreen(true);
            setShowBackButton(false);
            await OrientationManager.enterFullscreen();
          } else {
            // Exit fullscreen
            setIsFullscreen(false);
            setShowBackButton(true);
            await OrientationManager.exitFullscreen();
          }
          break;

        case 'VIDEO_PLAY':
          console.log('Video started playing');
          break;

        case 'VIDEO_PAUSE':
          console.log('Video paused');
          break;

        case 'VIDEO_ENDED':
          console.log('Video ended');
          break;

        case 'VIDEO_CHANGED':
          console.log('Video changed to index:', data.index);
          break;

        default:
          console.log('Unknown message type:', data.type);
      }
    } catch (error) {
      console.error('Failed to parse WebView message:', error);
    }
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const injectedJavaScript = `
    (function() {
      // --- Console override for debugging ---
      // This allows WebView's console logs to be visible in the RN console
      const originalConsole = { log: console.log, warn: console.warn, error: console.error, info: console.info, debug: console.debug };
      function sendLogToNative(level, args) {
        try {
          // Post message to React Native, converting all args to strings
          const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
          window.ReactNativeWebView?.postMessage(JSON.stringify({ type: 'CONSOLE_LOG', level: level, message: message, args: args }));
        } catch (e) {
          // Fallback if postMessage fails for any reason
        }
        // Also call the original console method
        originalConsole[level](...args);
      }
      console.log = (...args) => sendLogToNative('log', args);
      console.warn = (...args) => sendLogToNative('warn', args);
      console.error = (...args) => sendLogToNative('error', args);
      console.info = (...args) => sendLogToNative('info', args);
      console.debug = (...args) => sendLogToNative('debug', args);
      console.log('RN: WebView console override initialized.');
      // --- End Console override ---
      true; // Required for injectedJavaScript to work on iOS
    })();
  `;

  return (
    <View style={[styles.container, isFullscreen && styles.fullscreenContainer]}>
      <StatusBar hidden={isFullscreen} />
      
      {/* Back Button */}
      {showBackButton && (
        <SafeAreaView style={styles.backButtonContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
            activeOpacity={0.7}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
        </SafeAreaView>
      )}

      {/* WebView */}
      <WebView
        ref={webViewRef}
        source={{ uri: playerUrl }}
        style={styles.webview}
        onMessage={handleWebViewMessage}
        injectedJavaScript={injectedJavaScript}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        allowsProtectedMedia={true}
        onError={(error) => {
          console.error('WebView error:', error);
        }}
        onHttpError={(error) => {
          console.error('WebView HTTP error:', error);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  fullscreenContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backButtonContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1001,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  backButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'flex-start',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  webview: {
    flex: 1,
  },
});

export default VideoPlayerScreen;
