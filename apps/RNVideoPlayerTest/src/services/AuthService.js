import { apiClient, ApiError } from './ApiClient';

/**
 * AuthService - 认证服务
 * 负责用户认证、Token管理、用户状态维护
 */
class AuthService {
  constructor() {
    this.currentUser = null;
    this.tokenExpiryTimer = null;
    this.listeners = new Set();
  }

  /**
   * 添加认证状态监听器
   * @param {Function} listener - 监听器函数
   */
  addAuthListener(listener) {
    this.listeners.add(listener);
  }

  /**
   * 移除认证状态监听器
   * @param {Function} listener - 监听器函数
   */
  removeAuthListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器认证状态变化
   * @param {Object} user - 用户信息
   */
  notifyAuth<PERSON>hange(user) {
    this.listeners.forEach(listener => {
      try {
        listener(user);
      } catch (error) {
        console.error('Auth listener error:', error);
      }
    });
  }

  /**
   * 开发环境认证
   * @param {string} userType - 用户类型 ('admin', 'user', 'realtor')
   * @returns {Promise<Object>} 用户信息
   */
  async authenticate(userType = 'admin') {
    try {
      console.log(`AuthService: Authenticating as ${userType}...`);
      
      const authData = await apiClient.get('/dev/jwt', { user: userType });
      
      // 验证响应数据
      if (!authData.jwt || !authData.user_id) {
        throw new Error('Invalid authentication response');
      }

      // 设置token到API客户端
      apiClient.setToken(authData.jwt);
      
      // 存储用户信息
      this.currentUser = {
        id: authData.user_id,
        roles: authData.roles || ['user'],
        token: authData.jwt,
        expiresIn: authData.expires_in || 86400,
        authenticatedAt: Date.now(),
        userType: userType,
      };

      // 设置Token过期定时器
      this.setupTokenExpiryTimer();

      // 通知监听器
      this.notifyAuthChange(this.currentUser);

      console.log('AuthService: Authentication successful', {
        userId: this.currentUser.id,
        roles: this.currentUser.roles,
        userType: userType,
      });

      return this.currentUser;

    } catch (error) {
      console.error('AuthService: Authentication failed:', error);
      
      if (error instanceof ApiError) {
        throw new Error(`认证失败: ${error.getUserMessage()}`);
      }
      
      throw new Error(`认证失败: ${error.message}`);
    }
  }

  /**
   * 设置Token过期定时器
   */
  setupTokenExpiryTimer() {
    // 清除现有定时器
    if (this.tokenExpiryTimer) {
      clearTimeout(this.tokenExpiryTimer);
    }

    if (!this.currentUser?.expiresIn) {
      return;
    }

    // 在Token过期前5分钟触发刷新
    const refreshTime = (this.currentUser.expiresIn - 300) * 1000;
    
    if (refreshTime > 0) {
      this.tokenExpiryTimer = setTimeout(() => {
        console.log('AuthService: Token expiring soon, attempting refresh...');
        this.refreshToken().catch(error => {
          console.error('AuthService: Token refresh failed:', error);
          this.logout();
        });
      }, refreshTime);
    }
  }

  /**
   * 刷新Token (开发环境重新认证)
   * @returns {Promise<Object>} 新的用户信息
   */
  async refreshToken() {
    if (!this.currentUser) {
      throw new Error('No user to refresh token for');
    }

    try {
      // 开发环境下重新认证
      return await this.authenticate(this.currentUser.userType);
    } catch (error) {
      console.error('AuthService: Token refresh failed:', error);
      this.logout();
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * 检查是否已认证
   * @returns {boolean}
   */
  isAuthenticated() {
    if (!this.currentUser?.token) {
      return false;
    }

    // 检查Token是否过期
    const now = Date.now();
    const authenticatedAt = this.currentUser.authenticatedAt || 0;
    const expiresIn = (this.currentUser.expiresIn || 0) * 1000;
    
    return (now - authenticatedAt) < expiresIn;
  }

  /**
   * 检查用户是否有指定角色
   * @param {string} role - 角色名称
   * @returns {boolean}
   */
  hasRole(role) {
    return this.currentUser?.roles?.includes(role) || false;
  }

  /**
   * 检查用户是否为管理员
   * @returns {boolean}
   */
  isAdmin() {
    return this.hasRole('admin');
  }

  /**
   * 获取用户ID
   * @returns {string|null}
   */
  getUserId() {
    return this.currentUser?.id || null;
  }

  /**
   * 获取认证头信息
   * @returns {Object}
   */
  getAuthHeaders() {
    if (!this.isAuthenticated()) {
      throw new Error('User not authenticated');
    }

    return {
      'Authorization': `Bearer ${this.currentUser.token}`,
    };
  }

  /**
   * 登出
   */
  logout() {
    console.log('AuthService: Logging out...');
    
    // 清除定时器
    if (this.tokenExpiryTimer) {
      clearTimeout(this.tokenExpiryTimer);
      this.tokenExpiryTimer = null;
    }

    // 清除用户信息
    this.currentUser = null;
    
    // 清除API客户端Token
    apiClient.setToken(null);

    // 通知监听器
    this.notifyAuthChange(null);

    console.log('AuthService: Logged out successfully');
  }

  /**
   * 获取Token剩余有效时间（秒）
   * @returns {number}
   */
  getTokenRemainingTime() {
    if (!this.currentUser) {
      return 0;
    }

    const now = Date.now();
    const authenticatedAt = this.currentUser.authenticatedAt || 0;
    const expiresIn = (this.currentUser.expiresIn || 0) * 1000;
    const remaining = expiresIn - (now - authenticatedAt);
    
    return Math.max(0, Math.floor(remaining / 1000));
  }

  /**
   * 检查认证状态并在需要时重新认证
   * @returns {Promise<boolean>}
   */
  async ensureAuthenticated() {
    if (this.isAuthenticated()) {
      return true;
    }

    try {
      // 尝试使用之前的用户类型重新认证
      const userType = this.currentUser?.userType || 'admin';
      await this.authenticate(userType);
      return true;
    } catch (error) {
      console.error('AuthService: Re-authentication failed:', error);
      return false;
    }
  }

  /**
   * 销毁服务，清理资源
   */
  destroy() {
    this.logout();
    this.listeners.clear();
  }
}

// 创建单例实例
export const authService = new AuthService();

// 导出类以便测试
export { AuthService };
