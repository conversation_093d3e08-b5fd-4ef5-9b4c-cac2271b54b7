import { apiClient, ApiError } from './ApiClient';
import { authService } from './AuthService';

/**
 * VideoService - 视频相关API服务
 * 负责视频Feed、用户状态、交互事件等API调用
 */
class VideoService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 获取视频Feed
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} Feed数据
   */
  async getFeed(page = 1, limit = 20, useCache = true) {
    const cacheKey = `feed_${page}_${limit}`;
    
    // 检查缓存
    if (useCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        console.log('VideoService: Using cached feed data');
        return cached.data;
      }
    }

    try {
      console.log(`VideoService: Fetching feed page ${page}, limit ${limit}`);
      
      const feedData = await apiClient.get('/video/public/feed', { 
        page, 
        limit 
      });

      // 验证响应数据结构
      if (!feedData.videos || !Array.isArray(feedData.videos)) {
        throw new Error('Invalid feed response: missing videos array');
      }

      if (!feedData.pgn) {
        throw new Error('Invalid feed response: missing pagination info');
      }

      // 缓存数据
      if (useCache) {
        this.cache.set(cacheKey, {
          data: feedData,
          timestamp: Date.now(),
        });
      }

      // 处理视频数据，后端返回完整URL，直接使用（与M2项目一致）
      feedData.videos = feedData.videos.map(video => {
        // 处理日期字段，确保可序列化
        if (video.publishedAt && typeof video.publishedAt === 'string') {
          video.publishedAt = new Date(video.publishedAt);
        }

        // 后端返回完整URL，直接使用，无需拼接
        return video;
      });

      console.log(`VideoService: Feed loaded successfully, ${feedData.videos.length} videos`);
      return feedData;

    } catch (error) {
      console.error('VideoService: Get feed failed:', error);
      
      if (error instanceof ApiError) {
        if (error.isAuthError()) {
          throw new Error('认证失败，请重新登录');
        }
        throw new Error(`获取视频失败: ${error.getUserMessage()}`);
      }
      
      throw new Error(`获取视频失败: ${error.message}`);
    }
  }

  /**
   * 批量获取用户状态
   * @param {Array<string>} videoIds - 视频ID数组
   * @returns {Promise<Object>} 用户状态数据
   */
  async getUserStates(videoIds) {
    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      return { states: [] };
    }

    const userId = authService.getUserId();
    if (!userId) {
      throw new Error('用户未认证');
    }

    // 限制批量查询数量
    if (videoIds.length > 100) {
      console.warn('VideoService: Too many video IDs, limiting to 100');
      videoIds = videoIds.slice(0, 100);
    }

    try {
      console.log(`VideoService: Fetching user states for ${videoIds.length} videos`);

      const statesData = await apiClient.post('/video/public/states/batch', {
        userId,
        videoIds,
      });

      // 验证响应数据结构
      if (!statesData || typeof statesData !== 'object') {
        console.warn('VideoService: Invalid states response format, returning empty states');
        return { states: [] };
      }

      // 后端返回格式：{ states: [...], count: 2 }
      let states = [];
      if (Array.isArray(statesData)) {
        // 直接是数组
        states = statesData;
      } else if (statesData.states && Array.isArray(statesData.states)) {
        // 包装在对象中：{ states: [...] }
        states = statesData.states;
      } else {
        console.warn('VideoService: No states array found in response, returning empty states');
        console.log('VideoService: Actual response structure:', statesData);
        return { states: [] };
      }

      console.log(`VideoService: User states loaded, ${states.length} states`);
      return { states };

    } catch (error) {
      console.error('VideoService: Get user states failed:', error);
      
      if (error instanceof ApiError) {
        if (error.isAuthError()) {
          throw new Error('认证失败，请重新登录');
        }
        // 用户状态不是关键功能，可以降级处理
        console.warn('VideoService: Failed to load user states, continuing without states');
        return { states: [] };
      }
      
      // 非关键错误，返回空状态
      console.warn('VideoService: Failed to load user states, continuing without states');
      return { states: [] };
    }
  }

  /**
   * 创建交互事件
   * @param {string} videoId - 视频ID
   * @param {string} type - 交互类型 ('like', 'favorite', 'view_progress', 'share')
   * @param {Object} meta - 元数据
   * @returns {Promise<boolean>} 是否成功
   */
  async createInteraction(videoId, type, meta = {}) {
    if (!videoId || !type) {
      throw new Error('Video ID and interaction type are required');
    }

    // 验证交互类型
    const validTypes = ['like', 'favorite', 'view_progress', 'share', 'view_start', 'view_end'];
    if (!validTypes.includes(type)) {
      throw new Error(`Invalid interaction type: ${type}`);
    }

    try {
      console.log(`VideoService: Creating interaction ${type} for video ${videoId}`);
      
      await apiClient.post('/video/public/interactions', {
        videoId,
        type,
        meta,
      });

      console.log(`VideoService: Interaction ${type} created successfully`);
      return true;

    } catch (error) {
      console.error(`VideoService: Create interaction ${type} failed:`, error);
      
      if (error instanceof ApiError) {
        if (error.isAuthError()) {
          throw new Error('认证失败，请重新登录');
        }
        throw new Error(`操作失败: ${error.getUserMessage()}`);
      }
      
      throw new Error(`操作失败: ${error.message}`);
    }
  }

  /**
   * 获取用户收藏列表
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<Object>} 收藏数据
   */
  async getUserFavorites(page = 1, limit = 20) {
    try {
      console.log(`VideoService: Fetching user favorites page ${page}`);
      
      const favoritesData = await apiClient.get('/video/public/favorites', {
        page,
        limit,
      });

      console.log(`VideoService: User favorites loaded successfully`);
      return favoritesData;

    } catch (error) {
      console.error('VideoService: Get user favorites failed:', error);
      
      if (error instanceof ApiError) {
        if (error.isAuthError()) {
          throw new Error('认证失败，请重新登录');
        }
        throw new Error(`获取收藏失败: ${error.getUserMessage()}`);
      }
      
      throw new Error(`获取收藏失败: ${error.message}`);
    }
  }

  /**
   * 清除缓存
   * @param {string} pattern - 缓存键模式（可选）
   */
  clearCache(pattern = null) {
    if (pattern) {
      // 清除匹配模式的缓存
      for (const _key of this.cache.keys()) {
        if (_key.includes(pattern)) {
          this.cache.delete(_key);
        }
      }
      console.log(`VideoService: Cache cleared for pattern: ${pattern}`);
    } else {
      // 清除所有缓存
      this.cache.clear();
      console.log('VideoService: All cache cleared');
    }
  }

  /**
   * 预加载下一页数据
   * @param {number} currentPage - 当前页码
   * @param {number} limit - 每页数量
   */
  async preloadNextPage(currentPage, limit = 20) {
    try {
      const nextPage = currentPage + 1;
      console.log(`VideoService: Preloading page ${nextPage}`);

      // 静默预加载，不抛出错误
      await this.getFeed(nextPage, limit, true);

    } catch (error) {
      console.warn('VideoService: Preload failed:', error.message);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;

    for (const value of this.cache.values()) {
      if (now - value.timestamp < this.cacheTimeout) {
        validCount++;
      } else {
        expiredCount++;
      }
    }

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
    };
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [_key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(_key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`VideoService: Cleaned up ${cleanedCount} expired cache entries`);
    }
  }
}

// 创建单例实例
export const videoService = new VideoService();

// 定期清理过期缓存
setInterval(() => {
  videoService.cleanupExpiredCache();
}, 10 * 60 * 1000); // 每10分钟清理一次

// 导出类以便测试
export { VideoService };
