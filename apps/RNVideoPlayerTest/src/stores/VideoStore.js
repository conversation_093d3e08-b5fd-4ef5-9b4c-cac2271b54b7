import { videoService } from '../services/VideoService';
import { VideoModel } from '../models/VideoModel';

/**
 * VideoStore - 视频数据存储和状态管理
 * 负责视频数据的加载、缓存、状态管理和业务逻辑
 */
class VideoStore {
  constructor() {
    // 视频数据
    this.videos = [];
    this.loading = false;
    this.error = null;
    
    // 分页信息
    this.pagination = {
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      limit: 20,
    };

    // 设置
    this.locale = 'en';
    
    // 状态监听器
    this.listeners = new Set();
    
    // 预加载设置
    this.preloadThreshold = 3; // 剩余3个视频时开始预加载
    this.isPreloading = false;
  }

  /**
   * 添加状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
  }

  /**
   * 移除状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器状态变化
   * @param {string} event - 事件类型
   * @param {*} data - 事件数据
   */
  notifyListeners(event, data = null) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('VideoStore listener error:', error);
      }
    });
  }

  /**
   * 设置语言环境
   * @param {string} locale - 语言代码
   */
  setLocale(locale) {
    if (this.locale !== locale) {
      this.locale = locale;
      
      // 更新所有视频的语言设置
      this.videos.forEach(video => {
        video.setLocale(locale);
      });
      
      this.notifyListeners('localeChanged', locale);
    }
  }

  /**
   * 加载视频Feed
   * @param {number} page - 页码
   * @param {boolean} refresh - 是否刷新（清除现有数据）
   * @returns {Promise<void>}
   */
  async loadFeed(page = 1, refresh = false) {
    if (this.loading) {
      console.log('VideoStore: Already loading, skipping request');
      return;
    }

    try {
      this.loading = true;
      this.error = null;
      this.notifyListeners('loadingStarted', { page, refresh });

      console.log(`VideoStore: Loading feed page ${page}, refresh: ${refresh}`);

      // 如果是刷新操作，跳过缓存
      const useCache = !refresh;
      const feedData = await videoService.getFeed(page, this.pagination.limit, useCache);

      // 转换为VideoModel实例
      const videoModels = feedData.videos.map(video => 
        new VideoModel(video, this.locale)
      );

      if (refresh || page === 1) {
        this.videos = videoModels;
      } else {
        // 避免重复添加
        const existingIds = new Set(this.videos.map(v => v.id));
        const newVideos = videoModels.filter(v => !existingIds.has(v.id));
        this.videos.push(...newVideos);
      }

      // 更新分页信息
      this.pagination = {
        currentPage: feedData.pgn.currPg,
        totalPages: feedData.pgn.totPgs,
        totalItems: feedData.pgn.totItms,
        limit: feedData.pgn.lim,
      };

      console.log(`VideoStore: Feed loaded successfully, total videos: ${this.videos.length}`);

      // 加载用户状态
      await this.loadUserStates();

      this.notifyListeners('feedLoaded', {
        videos: this.videos,
        pagination: this.pagination,
      });

    } catch (error) {
      this.error = error.message;
      console.error('VideoStore: Load feed failed:', error);
      this.notifyListeners('loadingError', error);
    } finally {
      this.loading = false;
      this.notifyListeners('loadingFinished');
    }
  }

  /**
   * 加载用户状态
   * @returns {Promise<void>}
   */
  async loadUserStates() {
    if (this.videos.length === 0) {
      return;
    }

    try {
      console.log('VideoStore: Loading user states...');
      
      const videoIds = this.videos.map(video => video.id);
      const statesData = await videoService.getUserStates(videoIds);

      // 更新视频的用户状态
      const stateMap = new Map();
      statesData.states.forEach(state => {
        stateMap.set(state.videoId, state);
      });

      this.videos.forEach(video => {
        const state = stateMap.get(video.id);
        if (state) {
          video.updateUserState({
            isLiked: state.liked,
            isCollected: state.favorited,
            isBlocked: state.blocked,
            progressSeconds: state.progressSeconds,
          });
        }
      });

      console.log(`VideoStore: User states loaded for ${statesData.states.length} videos`);
      this.notifyListeners('userStatesLoaded', statesData.states);

    } catch (error) {
      console.warn('VideoStore: Load user states failed:', error);
      // 用户状态不是关键功能，不阻塞主流程
    }
  }

  /**
   * 加载下一页
   * @returns {Promise<boolean>} 是否成功加载
   */
  async loadNextPage() {
    if (!this.hasMorePages() || this.loading) {
      return false;
    }

    try {
      await this.loadFeed(this.pagination.currentPage + 1, false);
      return true;
    } catch (error) {
      console.error('VideoStore: Load next page failed:', error);
      return false;
    }
  }

  /**
   * 刷新数据
   * @returns {Promise<void>}
   */
  async refresh() {
    console.log('VideoStore: Refreshing feed');
    this.isRefreshing = true;
    try {
      // 刷新时强制跳过缓存，确保获取最新数据
      const feedData = await videoService.getFeed(1, 20, false); // useCache = false
      this.videos = feedData.videos.map(video => new VideoModel(video));
      this.currentPage = 1;
      this.hasMore = feedData.hasMore;
      this.totalCount = feedData.totalCount;
      console.log(`VideoStore: Refreshed with ${this.videos.length} videos`);
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 检查是否需要预加载
   * @param {number} currentIndex - 当前视频索引
   */
  checkPreload(currentIndex) {
    const remainingVideos = this.videos.length - currentIndex - 1;
    
    if (remainingVideos <= this.preloadThreshold && 
        this.hasMorePages() && 
        !this.loading && 
        !this.isPreloading) {
      
      this.preloadNextPage();
    }
  }

  /**
   * 预加载下一页
   * @returns {Promise<void>}
   */
  async preloadNextPage() {
    if (this.isPreloading || !this.hasMorePages()) {
      return;
    }

    try {
      this.isPreloading = true;
      console.log('VideoStore: Preloading next page...');
      
      await videoService.preloadNextPage(this.pagination.currentPage);
      
    } catch (error) {
      console.warn('VideoStore: Preload failed:', error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * 获取视频
   * @param {number} index - 视频索引
   * @returns {VideoModel|null}
   */
  getVideo(index) {
    return this.videos[index] || null;
  }

  /**
   * 根据ID获取视频
   * @param {string} videoId - 视频ID
   * @returns {VideoModel|null}
   */
  getVideoById(videoId) {
    return this.videos.find(video => video.id === videoId) || null;
  }

  /**
   * 获取视频总数
   * @returns {number}
   */
  getVideoCount() {
    return this.videos.length;
  }

  /**
   * 检查是否有更多页面
   * @returns {boolean}
   */
  hasMorePages() {
    return this.pagination.currentPage < this.pagination.totalPages;
  }

  /**
   * 获取兼容格式的视频列表
   * @returns {Array<Object>}
   */
  getVideosForLegacyFormat() {
    return this.videos.map(video => video.toLegacyFormat());
  }

  /**
   * 更新视频的用户状态
   * @param {string} videoId - 视频ID
   * @param {Object} stateUpdate - 状态更新
   */
  updateVideoUserState(videoId, stateUpdate) {
    const video = this.getVideoById(videoId);
    if (video) {
      video.updateUserState(stateUpdate);
      this.notifyListeners('videoStateUpdated', { videoId, stateUpdate });
    }
  }

  /**
   * 切换视频点赞状态
   * @param {string} videoId - 视频ID
   * @returns {boolean|null} 新的点赞状态，null表示视频不存在
   */
  toggleVideoLike(videoId) {
    const video = this.getVideoById(videoId);
    if (video) {
      const newState = video.toggleLike();
      this.notifyListeners('videoLikeToggled', { videoId, liked: newState });
      return newState;
    }
    return null;
  }

  /**
   * 切换视频收藏状态
   * @param {string} videoId - 视频ID
   * @returns {boolean|null} 新的收藏状态，null表示视频不存在
   */
  toggleVideoCollection(videoId) {
    const video = this.getVideoById(videoId);
    if (video) {
      const newState = video.toggleCollection();
      this.notifyListeners('videoCollectionToggled', { videoId, collected: newState });
      return newState;
    }
    return null;
  }

  /**
   * 更新视频观看进度
   * @param {string} videoId - 视频ID
   * @param {number} progressSeconds - 观看进度（秒）
   */
  updateVideoProgress(videoId, progressSeconds) {
    const video = this.getVideoById(videoId);
    if (video) {
      video.updateProgress(progressSeconds);
      this.notifyListeners('videoProgressUpdated', { videoId, progressSeconds });
    }
  }

  /**
   * 清除所有数据
   */
  clear() {
    this.videos = [];
    this.pagination = {
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      limit: 20,
    };
    this.error = null;
    this.notifyListeners('cleared');
  }

  /**
   * 获取存储统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      totalVideos: this.videos.length,
      currentPage: this.pagination.currentPage,
      totalPages: this.pagination.totalPages,
      hasMore: this.hasMorePages(),
      loading: this.loading,
      error: this.error,
      locale: this.locale,
    };
  }

  /**
   * 销毁存储，清理资源
   */
  destroy() {
    this.clear();
    this.listeners.clear();
  }
}

// 创建单例实例
export const videoStore = new VideoStore();

// 导出类以便测试
export { VideoStore };
