/**
 * TestComponents - 组件测试文件
 * 用于验证各个组件是否正常工作
 */

import { apiClient, ApiError } from '../services/ApiClient';
import { authService } from '../services/AuthService';
import { videoService } from '../services/VideoService';
import { NumberFormatter } from '../utils/NumberFormatter';
import { VideoModel } from '../models/VideoModel';
import { videoStore } from '../stores/VideoStore';

/**
 * 测试API客户端
 */
export async function testApiClient() {
  console.log('=== Testing ApiClient ===');
  
  try {
    // 测试健康检查
    const isHealthy = await apiClient.checkConnection();
    console.log('Health check:', isHealthy);
    
    // 测试错误处理
    try {
      await apiClient.get('/nonexistent');
    } catch (error) {
      if (error instanceof ApiError) {
        console.log('Error handling works:', error.code, error.getUserMessage());
      }
    }
    
    console.log('✅ ApiClient test passed');
  } catch (error) {
    console.error('❌ ApiClient test failed:', error);
  }
}

/**
 * 测试认证服务
 */
export async function testAuthService() {
  console.log('=== Testing AuthService ===');
  
  try {
    // 测试认证
    const user = await authService.authenticate('admin');
    console.log('Authentication successful:', user.id, user.roles);
    
    // 测试状态检查
    console.log('Is authenticated:', authService.isAuthenticated());
    console.log('Is admin:', authService.isAdmin());
    console.log('Token remaining time:', authService.getTokenRemainingTime(), 'seconds');
    
    console.log('✅ AuthService test passed');
  } catch (error) {
    console.error('❌ AuthService test failed:', error);
  }
}

/**
 * 测试视频服务
 */
export async function testVideoService() {
  console.log('=== Testing VideoService ===');
  
  try {
    // 确保已认证
    if (!authService.isAuthenticated()) {
      await authService.authenticate('admin');
    }
    
    // 测试获取Feed
    const feedData = await videoService.getFeed(1, 5);
    console.log('Feed loaded:', feedData.videos.length, 'videos');
    console.log('Pagination:', feedData.pgn);
    
    if (feedData.videos.length > 0) {
      // 测试获取用户状态
      const videoIds = feedData.videos.slice(0, 3).map(v => v.id);
      const statesData = await videoService.getUserStates(videoIds);
      console.log('User states loaded:', statesData.states.length, 'states');
      
      // 测试创建交互
      const firstVideoId = feedData.videos[0].id;
      const success = await videoService.createInteraction(firstVideoId, 'like', { liked: true });
      console.log('Interaction created:', success);
    }
    
    console.log('✅ VideoService test passed');
  } catch (error) {
    console.error('❌ VideoService test failed:', error);
  }
}

/**
 * 测试数字格式化
 */
export function testNumberFormatter() {
  console.log('=== Testing NumberFormatter ===');
  
  try {
    // 测试紧凑格式
    console.log('Compact format:');
    console.log('1234 ->', NumberFormatter.format(1234, { style: 'compact' }));
    console.log('12345 ->', NumberFormatter.format(12345, { style: 'compact' }));
    console.log('1234567 ->', NumberFormatter.format(1234567, { style: 'compact' }));
    
    // 测试中文格式
    console.log('Chinese format:');
    console.log('1234 ->', NumberFormatter.format(1234, { style: 'compact', locale: 'zh' }));
    console.log('12345 ->', NumberFormatter.format(12345, { style: 'compact', locale: 'zh' }));
    
    // 测试时长格式化
    console.log('Duration format:');
    console.log('65 seconds ->', NumberFormatter.formatDuration(65));
    console.log('3665 seconds ->', NumberFormatter.formatDuration(3665));
    
    // 测试文件大小格式化
    console.log('File size format:');
    console.log('1024 bytes ->', NumberFormatter.formatFileSize(1024));
    console.log('1048576 bytes ->', NumberFormatter.formatFileSize(1048576));
    
    console.log('✅ NumberFormatter test passed');
  } catch (error) {
    console.error('❌ NumberFormatter test failed:', error);
  }
}

/**
 * 测试视频模型
 */
export function testVideoModel() {
  console.log('=== Testing VideoModel ===');
  
  try {
    // 创建测试数据
    const mockBackendData = {
      id: 'test-video-123',
      title: { en: 'Test Video', zh: '测试视频' },
      description: { en: 'Test Description', zh: '测试描述' },
      previewVideoUrl: 'http://example.com/video.m3u8',
      previewThumbUrl: 'http://example.com/thumb.jpg',
      width: 1920,
      height: 1080,
      duration: 125.5,
      stats: {
        views: 12345,
        likes: 567,
        collections: 89,
      },
      propertyIds: ['prop-1', 'prop-2'],
    };
    
    // 创建VideoModel实例
    const video = new VideoModel(mockBackendData, 'en');
    
    // 测试基础属性
    console.log('Video ID:', video.id);
    console.log('Video URL:', video.videoUrl);
    console.log('Is HLS:', video.isHLS());
    console.log('Orientation:', video.getOrientation());
    console.log('Aspect ratio:', video.getAspectRatio());
    
    // 测试本地化文本
    console.log('English title:', video.getLocalizedText('title'));
    video.setLocale('zh');
    console.log('Chinese title:', video.getLocalizedText('title'));
    
    // 测试格式化统计
    const formattedStats = video.getFormattedStats();
    console.log('Formatted stats:', formattedStats);
    
    // 测试时长格式化
    console.log('Formatted duration:', video.getFormattedDuration());
    
    // 测试用户状态操作
    console.log('Toggle like:', video.toggleLike());
    console.log('Toggle collection:', video.toggleCollection());
    
    // 测试进度更新
    video.updateProgress(60);
    console.log('Progress percentage:', video.getProgressPercentage());
    
    // 测试转换为兼容格式
    const legacyFormat = video.toLegacyFormat();
    console.log('Legacy format keys:', Object.keys(legacyFormat));
    
    console.log('✅ VideoModel test passed');
  } catch (error) {
    console.error('❌ VideoModel test failed:', error);
  }
}

/**
 * 测试视频存储
 */
export async function testVideoStore() {
  console.log('=== Testing VideoStore ===');
  
  try {
    // 确保已认证
    if (!authService.isAuthenticated()) {
      await authService.authenticate('admin');
    }
    
    // 测试加载Feed
    await videoStore.loadFeed(1, true);
    console.log('Videos loaded:', videoStore.getVideoCount());
    console.log('Has more pages:', videoStore.hasMorePages());
    
    // 测试获取视频
    const firstVideo = videoStore.getVideo(0);
    if (firstVideo) {
      console.log('First video:', firstVideo.id, firstVideo.getLocalizedText('title'));
      
      // 测试状态操作
      const liked = videoStore.toggleVideoLike(firstVideo.id);
      console.log('Video liked:', liked);
      
      const collected = videoStore.toggleVideoCollection(firstVideo.id);
      console.log('Video collected:', collected);
    }
    
    // 测试语言切换
    videoStore.setLocale('zh');
    console.log('Locale changed to Chinese');
    
    // 测试统计信息
    const stats = videoStore.getStats();
    console.log('Store stats:', stats);
    
    console.log('✅ VideoStore test passed');
  } catch (error) {
    console.error('❌ VideoStore test failed:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 Starting component tests...\n');
  
  // 基础组件测试（不需要网络）
  testNumberFormatter();
  testVideoModel();
  
  // 网络相关测试
  await testApiClient();
  await testAuthService();
  await testVideoService();
  await testVideoStore();
  
  console.log('\n✅ All tests completed!');
}

/**
 * 快速验证所有组件是否可以正常导入
 */
export function quickImportTest() {
  console.log('=== Quick Import Test ===');
  
  try {
    console.log('ApiClient imported:', typeof apiClient);
    console.log('AuthService imported:', typeof authService);
    console.log('VideoService imported:', typeof videoService);
    console.log('NumberFormatter imported:', typeof NumberFormatter);
    console.log('VideoModel imported:', typeof VideoModel);
    console.log('VideoStore imported:', typeof videoStore);
    
    console.log('✅ All imports successful');
    return true;
  } catch (error) {
    console.error('❌ Import test failed:', error);
    return false;
  }
}
