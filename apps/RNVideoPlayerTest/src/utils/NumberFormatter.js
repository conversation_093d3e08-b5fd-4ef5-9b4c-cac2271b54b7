/**
 * NumberFormatter - 数字格式化工具类
 * 负责将数字格式化为用户友好的显示格式
 */
export class NumberFormatter {
  /**
   * 格式化数字
   * @param {number} num - 要格式化的数字
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的字符串
   */
  static format(num, options = {}) {
    const { 
      locale = 'en', 
      style = 'compact',
      maximumFractionDigits = 1,
      minimumFractionDigits = 0,
      useGrouping = true
    } = options;

    // 输入验证
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }

    // 处理负数
    if (num < 0) {
      return '-' + this.format(Math.abs(num), options);
    }

    switch (style) {
      case 'compact':
        return this.formatCompact(num, locale, maximumFractionDigits);
      case 'full':
        return this.formatFull(num, locale, useGrouping);
      case 'percentage':
        return this.formatPercentage(num, locale, maximumFractionDigits);
      case 'currency':
        return this.formatCurrency(num, locale, options.currency || 'USD');
      default:
        return num.toString();
    }
  }

  /**
   * 紧凑格式化（1.2k, 3.4M等）
   * @param {number} num - 数字
   * @param {string} locale - 语言环境
   * @param {number} maxDecimals - 最大小数位数
   * @returns {string}
   */
  static formatCompact(num, locale, maxDecimals) {
    const units = this.getUnits(locale);
    
    if (num >= 1000000000) {
      const value = (num / 1000000000).toFixed(maxDecimals);
      return `${this.removeTrailingZeros(value)}${units.billion}`;
    } else if (num >= 1000000) {
      const value = (num / 1000000).toFixed(maxDecimals);
      return `${this.removeTrailingZeros(value)}${units.million}`;
    } else if (num >= 1000) {
      const value = (num / 1000).toFixed(maxDecimals);
      return `${this.removeTrailingZeros(value)}${units.thousand}`;
    }
    
    return Math.floor(num).toString();
  }

  /**
   * 完整格式化（带千分位分隔符）
   * @param {number} num - 数字
   * @param {string} locale - 语言环境
   * @param {boolean} useGrouping - 是否使用分组
   * @returns {string}
   */
  static formatFull(num, locale, useGrouping) {
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    
    return new Intl.NumberFormat(localeCode, {
      useGrouping: useGrouping,
      maximumFractionDigits: 0,
    }).format(Math.floor(num));
  }

  /**
   * 百分比格式化
   * @param {number} num - 数字（0-1之间）
   * @param {string} locale - 语言环境
   * @param {number} maxDecimals - 最大小数位数
   * @returns {string}
   */
  static formatPercentage(num, locale, maxDecimals) {
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    
    return new Intl.NumberFormat(localeCode, {
      style: 'percent',
      maximumFractionDigits: maxDecimals,
    }).format(num);
  }

  /**
   * 货币格式化
   * @param {number} num - 数字
   * @param {string} locale - 语言环境
   * @param {string} currency - 货币代码
   * @returns {string}
   */
  static formatCurrency(num, locale, currency) {
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    
    return new Intl.NumberFormat(localeCode, {
      style: 'currency',
      currency: currency,
    }).format(num);
  }

  /**
   * 获取本地化单位
   * @param {string} locale - 语言环境
   * @returns {Object} 单位对象
   */
  static getUnits(locale) {
    if (locale === 'zh') {
      return {
        thousand: '千',
        million: '万',
        billion: '亿',
      };
    } else {
      return {
        thousand: 'k',
        million: 'M',
        billion: 'B',
      };
    }
  }

  /**
   * 移除尾随零
   * @param {string} value - 数值字符串
   * @returns {string}
   */
  static removeTrailingZeros(value) {
    return value.replace(/\.?0+$/, '');
  }

  /**
   * 格式化时长（秒转为时:分:秒）
   * @param {number} seconds - 秒数
   * @param {Object} options - 选项
   * @returns {string}
   */
  static formatDuration(seconds, options = {}) {
    const { showHours = true, showSeconds = true } = options;
    
    if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
      return '0:00';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0 && showHours) {
      if (showSeconds) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${hours}:${minutes.toString().padStart(2, '0')}`;
      }
    } else {
      if (showSeconds) {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes}`;
      }
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @param {Object} options - 选项
   * @returns {string}
   */
  static formatFileSize(bytes, options = {}) {
    const { locale = 'en', decimals = 1 } = options;
    
    if (bytes === 0) return '0 B';
    
    const units = locale === 'zh' 
      ? ['B', 'KB', 'MB', 'GB', 'TB']
      : ['B', 'KB', 'MB', 'GB', 'TB'];
    
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    const value = bytes / Math.pow(k, i);
    const formattedValue = i === 0 ? value.toString() : value.toFixed(decimals);
    
    return `${this.removeTrailingZeros(formattedValue)} ${units[i]}`;
  }

  /**
   * 智能格式化（根据数值大小自动选择格式）
   * @param {number} num - 数字
   * @param {Object} options - 选项
   * @returns {string}
   */
  static smartFormat(num, options = {}) {
    const { locale = 'en', context = 'general' } = options;
    
    // 根据上下文和数值大小选择合适的格式
    if (context === 'stats' || context === 'social') {
      // 社交媒体统计数据，使用紧凑格式
      return this.format(num, { locale, style: 'compact' });
    } else if (context === 'financial') {
      // 金融数据，使用完整格式
      return this.format(num, { locale, style: 'full' });
    } else if (num >= 10000) {
      // 大数字使用紧凑格式
      return this.format(num, { locale, style: 'compact' });
    } else {
      // 小数字使用完整格式
      return this.format(num, { locale, style: 'full' });
    }
  }

  /**
   * 批量格式化
   * @param {Array<number>} numbers - 数字数组
   * @param {Object} options - 格式化选项
   * @returns {Array<string>} 格式化后的字符串数组
   */
  static formatBatch(numbers, options = {}) {
    if (!Array.isArray(numbers)) {
      return [];
    }

    return numbers.map(num => this.format(num, options));
  }

  /**
   * 解析格式化的数字字符串回数字
   * @param {string} formattedNum - 格式化的数字字符串
   * @param {string} locale - 语言环境
   * @returns {number|null}
   */
  static parse(formattedNum, locale = 'en') {
    if (typeof formattedNum !== 'string') {
      return null;
    }

    const units = this.getUnits(locale);
    let multiplier = 1;
    let cleanNum = formattedNum.toLowerCase().trim();

    // 检查单位
    if (cleanNum.endsWith(units.billion.toLowerCase())) {
      multiplier = 1000000000;
      cleanNum = cleanNum.slice(0, -units.billion.length);
    } else if (cleanNum.endsWith(units.million.toLowerCase())) {
      multiplier = 1000000;
      cleanNum = cleanNum.slice(0, -units.million.length);
    } else if (cleanNum.endsWith(units.thousand.toLowerCase())) {
      multiplier = 1000;
      cleanNum = cleanNum.slice(0, -units.thousand.length);
    }

    // 解析数字
    const num = parseFloat(cleanNum);
    return isNaN(num) ? null : num * multiplier;
  }
}
