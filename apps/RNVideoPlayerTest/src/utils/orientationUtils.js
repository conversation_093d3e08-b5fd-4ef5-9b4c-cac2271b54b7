import * as ScreenOrientation from 'expo-screen-orientation';

/**
 * Utility functions for handling screen orientation changes
 * with proper error handling for different device capabilities
 */

export const OrientationManager = {
  /**
   * Enter fullscreen mode (landscape orientation)
   */
  async enterFullscreen() {
    try {
      // First unlock to allow orientation change
      await ScreenOrientation.unlockAsync();
      
      // Small delay to ensure unlock takes effect
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Try to lock to landscape
      await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
      
      console.log('Successfully entered fullscreen (landscape)');
      return true;
    } catch (error) {
      console.warn('Failed to enter fullscreen, trying fallback:', error.message);
      
      try {
        // Fallback: just unlock and let user rotate manually
        await ScreenOrientation.unlockAsync();
        console.log('Fallback: orientation unlocked for manual rotation');
        return true;
      } catch (fallbackError) {
        console.error('Failed to handle orientation for fullscreen:', fallbackError.message);
        return false;
      }
    }
  },

  /**
   * Exit fullscreen mode (portrait orientation)
   */
  async exitFullscreen() {
    try {
      // First unlock to allow orientation change
      await ScreenOrientation.unlockAsync();
      
      // Small delay to ensure unlock takes effect
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Try different portrait orientations in order of preference
      const portraitOptions = [
        ScreenOrientation.OrientationLock.PORTRAIT_UP,
        ScreenOrientation.OrientationLock.PORTRAIT,
        ScreenOrientation.OrientationLock.DEFAULT
      ];
      
      for (const orientation of portraitOptions) {
        try {
          await ScreenOrientation.lockAsync(orientation);
          console.log(`Successfully locked to ${orientation}`);
          return true;
        } catch (lockError) {
          console.warn(`Failed to lock to ${orientation}:`, lockError.message);
          continue;
        }
      }
      
      // If all portrait locks fail, just keep it unlocked
      console.log('Could not lock to portrait, keeping orientation unlocked');
      return true;
      
    } catch (error) {
      console.error('Failed to exit fullscreen orientation:', error.message);
      return false;
    }
  },

  /**
   * Reset orientation to default (usually portrait)
   */
  async resetOrientation() {
    try {
      await ScreenOrientation.unlockAsync();
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Try to set to default orientation
      try {
        await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
      } catch (lockError) {
        // If lock fails, just keep unlocked
        console.warn('Could not lock to portrait on reset, keeping unlocked');
      }
      
      return true;
    } catch (error) {
      console.error('Failed to reset orientation:', error.message);
      return false;
    }
  },

  /**
   * Get current orientation info
   */
  async getCurrentOrientation() {
    try {
      const orientation = await ScreenOrientation.getOrientationAsync();
      return orientation;
    } catch (error) {
      console.error('Failed to get current orientation:', error.message);
      return null;
    }
  }
};
