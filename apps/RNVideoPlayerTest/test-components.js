/**
 * 组件测试入口文件
 * 在Node.js环境中测试组件的基本功能
 */

// 模拟React Native环境
global.fetch = require('node-fetch');

// 导入测试函数
const { 
  quickImportTest, 
  testNumberFormatter,
  runAllTests 
} = require('./src/test/TestComponents.js');

async function main() {
  console.log('🧪 Component Testing Suite\n');
  
  try {
    // 快速导入测试
    const importSuccess = quickImportTest();
    
    if (importSuccess) {
      console.log('\n📊 Running basic tests...');
      
      // 运行不需要网络的基础测试
      testNumberFormatter();
      
      console.log('\n🌐 Network tests require a running backend server.');
      console.log('To run full tests, ensure the backend is running at http://192.168.2.162:8080');
      console.log('Then call runAllTests() in your React Native app.');
      
    } else {
      console.log('❌ Import test failed, please check your imports');
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// 运行测试
main().catch(console.error);
