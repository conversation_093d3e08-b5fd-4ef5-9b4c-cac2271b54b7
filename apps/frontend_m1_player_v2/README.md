# RealMaster 视频播放器 V2

这是一个现代化的 Web 视频播放器，专为移动端设计，支持垂直滑动切换视频、手势控制、全屏播放等功能。该播放器采用模块化架构，支持与 React Native WebView 集成，提供流畅的视频浏览体验。

## ✨ 主要特性

- 🎬 **流畅视频播放**: 支持多种视频格式，优化的播放性能
- 📱 **移动端优化**: 专为移动设备设计的触摸交互体验
- 🔄 **垂直滑动切换**: 类似短视频应用的上下滑动切换视频
- 👆 **丰富手势支持**: 单击播放/暂停、双击快进/快退、长按菜单
- 🖥️ **全屏播放**: 支持横屏全屏模式，优化的全屏控制界面
- 🏠 **房源信息集成**: 支持显示相关房产信息的抽屉组件
- 📊 **互动功能**: 点赞、收藏、分享等社交功能
- 🎛️ **播放控制**: 进度条拖拽、倍速播放、静音控制
- 🔧 **模块化架构**: 清晰的代码结构，易于维护和扩展
- 📲 **React Native 集成**: 支持与 React Native WebView 通信

---

## 🚀 快速开始

### 环境要求

- Node.js 18+ (推荐使用 LTS 版本)
- npm 8+ 或 yarn 1.22+
- Vite 7.0+
- 现代浏览器支持 (Chrome 80+, Firefox 75+, Safari 13+)
- 视频播放支持 (H.264/HEVC 编码)
- 网络连接 (用于加载视频流)
- 开发环境需要支持 ES Modules

### 安装与运行

1.  **克隆项目**
    ```bash
    git clone <your-repository-url>
    cd apps/frontend_m1_player_v2
    ```

2.  **安装依赖**
    ```bash
    npm install
    ```

3.  **启动开发服务器**
    ```bash
    npm run dev
    ```

4.  **构建生产版本**
    ```bash
    npm run build
    ```

5.  **预览生产版本**
    ```bash
    npm run preview
    ```

---

## 🧑‍💻 使用示例

### 基础用法

```javascript
import { Player } from './player/Player.js';

// 初始视频数据
const initialVideos = [
  {
    id: 1,
    title: '示例视频',
    videoUrl: 'https://example.com/video.mp4',
    description: '这是一个示例视频',
    stats: { likes: '1.2k', collections: '456' },
    userState: { isLiked: false, isCollected: false }
  }
];

// 获取更多数据的函数
function fetchNextPage() {
  return new Promise((resolve) => {
    // 模拟 API 调用
    setTimeout(() => {
      resolve([/* 更多视频数据 */]);
    }, 500);
  });
}

// 创建播放器实例
const container = document.querySelector('#app');
const player = new Player(container, initialVideos, 0, fetchNextPage);
```

### 手势控制

播放器支持以下手势操作：

- **单击屏幕**: 播放/暂停视频
- **双击左侧**: 快退 10 秒
- **双击右侧**: 快进 10 秒
- **上下滑动**: 切换视频
- **长按屏幕**: 显示菜单选项
- **拖拽进度条**: 调整播放进度

### React Native 集成

```javascript
// 在 React Native WebView 中使用
const player = new Player(container, videos, startIndex, fetchMore);

// 播放器会自动向 React Native 发送消息
// 例如：全屏状态变化、视频播放事件等
```

---

## ⚙️ 配置说明

配置文件位于 `src/config/constants.js`。

### 播放器配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `NUM_VIDEO_SLOTS` | number | `5` | DOM 中维护的视频元素数量 |
| `PRELOAD_THRESHOLD` | number | `3` | 预加载下一页的触发阈值 |
| `DOUBLE_TAP_SEEK_SECONDS` | number | `10` | 双击快进/快退的秒数 |

### 手势配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `SWIPE_THRESHOLD_RATIO` | number | `0.2` | 滑动切换视频的阈值比例 |
| `LONG_PRESS_TIME_MS` | number | `300` | 长按触发时间(毫秒) |

---

## 📁 项目结构

```text
src/
├── main.js                 # 应用入口文件
├── style.css              # 全局样式
├── config/                # 配置文件
│   └── constants.js       # 常量配置
├── player/                # 播放器核心
│   ├── Player.js          # 主播放器类
│   └── FullscreenController.js # 全屏控制器
├── data/                  # 数据管理
│   └── DataManager.js     # 数据管理器
├── gestures/              # 手势控制
│   └── GestureManager.js  # 手势管理器
├── ui/                    # 用户界面组件
│   ├── PlayerUI.js        # 播放器 UI
│   ├── BottomMenu.js      # 底部菜单
│   ├── HouseDrawer.js     # 房源信息抽屉
│   ├── css/               # 样式文件
│   └── icons/             # 图标资源
└── services/              # 服务层
    └── ApiService.js      # API 服务
```

---

## 🎮 核心功能

### 视频播放控制

- **自动播放**: 视频切换时自动播放
- **播放状态管理**: 统一的播放/暂停状态控制
- **进度记忆**: 记住每个视频的播放进度
- **预加载**: 智能预加载下一个视频

### 手势交互

- **垂直滑动**: 流畅的视频切换动画
- **双击快进**: 左右双击实现快进/快退
- **长按菜单**: 显示更多操作选项
- **进度条拖拽**: 精确的进度控制

### 全屏模式

- **横屏全屏**: 自动切换到横屏模式
- **控制界面**: 专门的全屏控制界面
- **手势保持**: 全屏模式下保持所有手势功能
- **React Native 通信**: 通知原生应用屏幕方向变化

---

## ✅ 测试

运行测试（如果有的话）：

```bash
npm test
```

---

## 📄 License

本项目采用 MIT 开源许可证。

---

## 🙋 常见问题

**Q: 视频无法播放？**  
A: 请检查视频 URL 是否有效，确保视频格式被浏览器支持（推荐 H.264 编码的 MP4 格式）。

**Q: 手势不响应？**  
A: 确保容器元素有足够的高度和宽度，检查是否有其他元素阻挡了触摸事件。

**Q: 全屏模式异常？**  
A: 在 React Native WebView 中使用时，确保原生端正确处理了屏幕方向变化的消息。

**Q: 如何自定义样式？**  
A: 修改 `src/style.css` 和 `src/ui/css/` 目录下的样式文件，或通过 CSS 变量进行主题定制。

---

## 📫 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 项目讨论区
