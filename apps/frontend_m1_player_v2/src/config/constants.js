/**
 * @file This file contains shared constants used throughout the player application.
 * Centralizing them here makes the code easier to read and maintain.
 */

// --- Player Configuration ---

// The number of video elements to maintain in the DOM pool.
// Reduced for mobile performance - fewer simultaneous videos = less memory usage
export const NUM_VIDEO_SLOTS = 3;

// Additional performance constants for mobile WebView
export const MOBILE_PERFORMANCE_CONFIG = {
  // Reduce video quality for better performance
  VIDEO_PRELOAD: 'metadata', // Changed from 'auto' to 'metadata'

  // Throttle gesture events for better responsiveness
  GESTURE_THROTTLE_MS: 16, // ~60fps

  // Memory cleanup intervals
  MEMORY_CLEANUP_INTERVAL: 30000, // 30 seconds

  // Video switching performance
  VIDEO_SWITCH_DEBOUNCE: 100, // ms
};


// --- Gesture & Interaction ---

// The percentage of the screen height that a user must swipe up or down
// to trigger a video change. (e.g., 0.3 = 30% of the screen height)
export const SWIPE_THRESHOLD_RATIO = 0.2;

// The time in milliseconds a user must hold their finger down to trigger
// a 'long press' event, used for showing/hiding the video overlay.
export const LONG_PRESS_TIME_MS = 300;

// The number of videos from the end of the current list at which
// the app should start pre-fetching the next page of videos.
export const PRELOAD_THRESHOLD = 3;

// The number of seconds to seek forward or backward when the user double-taps
// on the right or left side of the screen.
export const DOUBLE_TAP_SEEK_SECONDS = 10; 