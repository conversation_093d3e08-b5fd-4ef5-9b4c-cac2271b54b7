import { apiService } from '../services/ApiService.js';

export class DataManager {
  constructor(initialVideos = [], requestNextPageFn) {
    this.videos = [...initialVideos];
    this.requestNextPageFn = requestNextPageFn;

    this.isLoading = false;
    // Assume there is more data if a function to request it is provided
    this.hasMoreData = typeof this.requestNextPageFn === 'function';

    // User states cache
    this.userStatesCache = new Map();
    this.userStatesLoaded = false;

    // 防止重复操作
    this.pendingOperations = new Set();
  }

  /**
   * Gets video data for a specific index.
   * @param {number} index The index of the video to get.
   * @returns {object | null} The video data object or null if not found.
   */
  getVideo(index) {
    if (index < 0 || index >= this.videos.length) {
      return null;
    }
    return this.videos[index];
  }

  /**
   * Requests more videos using the provided function.
   * @returns {Promise<boolean>} A promise that resolves to true if new videos were added, false otherwise.
   */
  async requestMoreVideos() {
    if (this.isLoading || !this.hasMoreData) {
      return false;
    }

    this.isLoading = true;
    console.log('Requesting next page of videos...');

    try {
      const newVideos = await this.requestNextPageFn();
      if (newVideos && newVideos.length > 0) {
        this.videos.push(...newVideos);
        console.log(`Loaded ${newVideos.length} new videos.`);
        return true;
      } else {
        console.log('No more videos to load.');
        this.hasMoreData = false;
        return false;
      }
    } catch (error) {
      console.error('Failed to request more videos:', error);
      return false;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Gets the total number of videos currently loaded.
   * @returns {number}
   */
  getTotalVideos() {
    return this.videos.length;
  }

  /**
   * Load user states for all videos
   * @returns {Promise<void>}
   */
  async loadUserStates() {
    if (this.videos.length === 0) {
      return;
    }

    try {
      console.log('DataManager: Loading user states...');

      const videoIds = this.videos.map(video => video.id);
      const states = await apiService.getUserStates(videoIds);

      // Update video user states
      states.forEach(state => {
        const video = this.videos.find(v => v.id === state.videoId);
        if (video) {
          video.userState = {
            isLiked: state.liked || false,
            isCollected: state.favorited || false,
            isBlocked: state.blocked || false,
            progressSeconds: state.progressSeconds || 0,
          };

          // Cache the state
          this.userStatesCache.set(state.videoId, video.userState);
        }
      });

      this.userStatesLoaded = true;
      console.log(`DataManager: User states loaded for ${states.length} videos`);

    } catch (error) {
      console.error('DataManager: Failed to load user states:', error);
    }
  }

  /**
   * Update video user state and sync with backend
   * @param {string} videoId - Video ID
   * @param {string} action - Action type ('like', 'collect', etc.)
   * @param {Object} newState - New state data
   * @returns {Promise<boolean>}
   */
  async updateVideoUserState(videoId, action, newState) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) {
      console.warn('DataManager: Video not found:', videoId);
      return false;
    }

    try {
      // Update local state immediately for responsive UI
      video.userState = {
        ...video.userState,
        ...newState,
      };

      // Cache the updated state
      this.userStatesCache.set(videoId, video.userState);

      // Sync with backend
      const success = await apiService.createInteraction(videoId, action, newState);

      if (success) {
        console.log(`DataManager: ${action} interaction created for video ${videoId}`);
      } else {
        console.warn(`DataManager: Failed to create ${action} interaction for video ${videoId}`);
      }

      return success;

    } catch (error) {
      console.error('DataManager: Failed to update video user state:', error);
      return false;
    }
  }

  /**
   * Notify UI about state changes
   * @param {string} videoId - Video ID
   * @param {Object} video - Video data
   */
  notifyStateChange(videoId, video) {
    // 触发自定义事件通知UI更新
    const event = new CustomEvent('videoStateChanged', {
      detail: { videoId, video }
    });
    window.dispatchEvent(event);
  }

  /**
   * Toggle video like state
   * @param {string} videoId - Video ID
   * @returns {Promise<boolean>} New like state
   */
  async toggleVideoLike(videoId) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) {
      console.error('DataManager: Video not found:', videoId);
      return false;
    }

    // 防止重复操作
    const operationKey = `like_${videoId}`;
    if (this.pendingOperations.has(operationKey)) {
      console.log('DataManager: Like operation already in progress');
      return video.userState.isLiked;
    }

    this.pendingOperations.add(operationKey);

    const currentLikedState = video.userState.isLiked;
    const newLikedState = !currentLikedState;

    // 保存原始状态用于回滚
    const originalState = { ...video.userState };
    const originalStats = { ...video.stats };

    try {
      // 1. 立即更新状态（乐观更新）
      video.userState.isLiked = newLikedState;

      // 2. 更新统计数字
      const currentLikes = parseInt(video.stats.likes.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.likes = newLikedState ?
        (currentLikes + 1).toLocaleString() :
        Math.max(0, currentLikes - 1).toLocaleString();

      // 3. 更新缓存
      this.userStatesCache.set(videoId, video.userState);

      // 4. 立即通知UI更新
      this.notifyStateChange(videoId, video);

      // 5. 同步到后端
      const success = await this.updateVideoUserState(videoId, 'like', {
        liked: newLikedState
      });

      if (!success) {
        throw new Error('API request failed');
      }

      console.log(`DataManager: Like toggled successfully for video ${videoId}: ${newLikedState}`);
      return newLikedState;

    } catch (error) {
      console.error('DataManager: Failed to toggle like:', error);

      // 回滚状态
      video.userState = originalState;
      video.stats = originalStats;
      this.userStatesCache.set(videoId, originalState);

      // 触发UI更新以反映回滚
      this.notifyStateChange(videoId, video);

      return currentLikedState;
    } finally {
      this.pendingOperations.delete(operationKey);
    }
  }

  /**
   * Toggle video collection state
   * @param {string} videoId - Video ID
   * @returns {Promise<boolean>} New collection state
   */
  async toggleVideoCollection(videoId) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) {
      console.error('DataManager: Video not found:', videoId);
      return false;
    }

    // 防止重复操作
    const operationKey = `collect_${videoId}`;
    if (this.pendingOperations.has(operationKey)) {
      console.log('DataManager: Collection operation already in progress');
      return video.userState.isCollected;
    }

    this.pendingOperations.add(operationKey);

    const currentCollectedState = video.userState.isCollected;
    const newCollectedState = !currentCollectedState;

    // 保存原始状态用于回滚
    const originalState = { ...video.userState };
    const originalStats = { ...video.stats };

    try {
      // 1. 立即更新状态（乐观更新）
      video.userState.isCollected = newCollectedState;

      // 2. 更新统计数字
      const currentCollections = parseInt(video.stats.collections.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.collections = newCollectedState ?
        (currentCollections + 1).toLocaleString() :
        Math.max(0, currentCollections - 1).toLocaleString();

      // 3. 更新缓存
      this.userStatesCache.set(videoId, video.userState);

      // 4. 立即通知UI更新
      this.notifyStateChange(videoId, video);

      // 5. 同步到后端
      const success = await this.updateVideoUserState(videoId, 'favorite', {
        favorited: newCollectedState
      });

      if (!success) {
        throw new Error('API request failed');
      }

      console.log(`DataManager: Collection toggled successfully for video ${videoId}: ${newCollectedState}`);
      return newCollectedState;

    } catch (error) {
      console.error('DataManager: Failed to toggle collection:', error);

      // 回滚状态
      video.userState = originalState;
      video.stats = originalStats;
      this.userStatesCache.set(videoId, originalState);

      // 触发UI更新以反映回滚
      this.notifyStateChange(videoId, video);

      return currentCollectedState;
    } finally {
      this.pendingOperations.delete(operationKey);
    }
  }

  /**
   * Update video progress
   * @param {string} videoId - Video ID
   * @param {number} progressSeconds - Progress in seconds
   */
  async updateVideoProgress(videoId, progressSeconds) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) return;

    video.userState.progressSeconds = progressSeconds;
    this.userStatesCache.set(videoId, video.userState);

    // Throttle progress updates to backend (every 10 seconds)
    if (!video._lastProgressUpdate ||
        Date.now() - video._lastProgressUpdate > 10000) {

      video._lastProgressUpdate = Date.now();
      await this.updateVideoUserState(videoId, 'view_progress', {
        progressSeconds
      });
    }
  }

  /**
   * Get cached user state for a video
   * @param {string} videoId - Video ID
   * @returns {Object|null}
   */
  getCachedUserState(videoId) {
    return this.userStatesCache.get(videoId) || null;
  }

  /**
   * Clear user states cache
   */
  clearUserStatesCache() {
    this.userStatesCache.clear();
    this.userStatesLoaded = false;
  }
}