import Hammer from 'hammerjs';
import { SWIPE_THRESHOLD_RATIO, LONG_PRESS_TIME_MS, MOBILE_PERFORMANCE_CONFIG } from '../config/constants.js';

// Throttle function for performance optimization
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

export class GestureManager {
  constructor(wrapper, startIndex, getTotalVideosFn, onTap, onDoubleTap, onLongPress) {
    this.wrapper = wrapper;
    this.container = wrapper.parentElement; // The #app container
    this.currentIndex = startIndex;
    this.getTotalVideos = getTotalVideosFn;
    this.onTap = onTap;
    this.onDoubleTap = onDoubleTap;
    this.onLongPress = onLongPress;

    this.containerHeight = this.container.offsetHeight;
    this.isPanning = false;
    this.isPressActive = false;
    this.isFullscreenMode = false;
    this.onFullscreenTap = null;

    this.handleResize = this.onResize.bind(this); // Save the reference

    // Set initial position based on the data index
    this.wrapper.style.transition = 'none';
    this.setWrapperY(-this.currentIndex * this.containerHeight);
    
    this.hammer = new Hammer.Manager(this.container);
    this.setupGestures();
  }

  setupGestures() {
    // Manually create and add all recognizers to avoid default conflicts
    // Increased threshold for better performance on mobile
    const pan = new Hammer.Pan({ direction: Hammer.DIRECTION_VERTICAL, threshold: 15 });
    const singleTap = new Hammer.Tap({ event: 'singletap' });
    const doubleTap = new Hammer.Tap({ event: 'doubletap', taps: 2 });
    const tripleTap = new Hammer.Tap({ event: 'tripletap', taps: 3 });
    const press = new Hammer.Press({ event: 'press', time: LONG_PRESS_TIME_MS });

    // Add them to the manager
    this.hammer.add([pan, press, tripleTap, doubleTap, singleTap]);

    // Define the relationships
    tripleTap.recognizeWith([doubleTap, singleTap]);
    doubleTap.recognizeWith(singleTap);
    doubleTap.requireFailure(tripleTap);
    singleTap.requireFailure([tripleTap, doubleTap, press]); // Prevent tap when a long press is successful
    
    
    // Set up the listeners with throttling for better performance
    this.hammer.on('panstart', (e) => this.onPanStart(e));
    this.hammer.on('panmove', throttle((e) => this.onPanMove(e), MOBILE_PERFORMANCE_CONFIG.GESTURE_THROTTLE_MS));
    this.hammer.on('panend', (e) => this.onPanEnd(e));
    
    this.hammer.on('singletap', (e) => {
      // Ignore taps on interactive UI elements to prevent play/pause toggle
      if (e.target.closest('.video-sidebar') || 
          e.target.closest('.bottom-sheet') || 
          e.target.closest('.bottom-sheet-backdrop') ||
          e.target.closest('.video-info') ||
          e.target.closest('.house-drawer-container') ||
          e.target.closest('.progress-container') ||
          e.target.closest('.fs-interactive') ||
          e.target.closest('.house-drawer-toggle')) { // Also ignore fullscreen interactive elements
        return;
      }

      if (this.isFullscreenMode) {
        if (this.onFullscreenTap) {
          this.onFullscreenTap();
        }
        return;
      }

      if (this.onTap) {
        this.onTap();
      }
    });

    this.hammer.on('doubletap', (e) => {
      if (this.onDoubleTap) {
        this.onDoubleTap(e);
      }
    });

    this.hammer.on('press', (e) => {
      // The context menu is prevented by a native event listener below.
      this.isPressActive = true;

      // Ignore presses on the sidebar
      if (e.target.closest('.video-sidebar')) {
        return;
      }
      if (this.onLongPress) {
        this.onLongPress(e);
      }
    });

    // Reset press lock when the interaction is over
    this.hammer.on('hammer.input', (e) => {
      if (e.isFinal) {
        this.isPressActive = false;
      }
    });

    window.addEventListener('resize', this.handleResize);

    // Prevent native context menu on the entire container
    this.handleContextMenu = (e) => e.preventDefault();
    this.container.addEventListener('contextmenu', this.handleContextMenu);
  }

  onResize() {
    this.containerHeight = this.container.offsetHeight;
    this.wrapper.style.transition = 'none'; // Disable animation during resize adjustment
    this.snapTo(this.currentIndex);
  }

  onPanStart(e) {
    if (this.isFullscreenMode) {
      return;
    }
    if (this.isPressActive) {return;}
    if (e.target.closest('.progress-container')) {
      return;
    }
    if (!e.target.closest('.house-drawer-container')) {
      this.isPanning = true;
      this.panStartY = -this.currentIndex * this.containerHeight;
      this.wrapper.style.transition = 'none';
    }
  }

  onPanMove(e) {
    if (!this.isPanning) {return;}
    const newY = this.panStartY + e.deltaY;
    this.setWrapperY(newY);
  }

  onPanEnd(e) {
    if (!this.isPanning) {return;}
    this.isPanning = false;
    this.wrapper.style.transition = 'transform 300ms ease-out';

    const dragDistance = e.deltaY;
    const swipeThreshold = this.containerHeight * SWIPE_THRESHOLD_RATIO;

    let newIndex = this.currentIndex;
    const totalVideos = this.getTotalVideos();

    if (dragDistance < -swipeThreshold && this.currentIndex < totalVideos - 1) {
      newIndex++;
    } else if (dragDistance > swipeThreshold && this.currentIndex > 0) {
      newIndex--;
    }

    this.snapTo(newIndex);
  }
  
  snapTo(newIndex) {
    if (this.currentIndex !== newIndex) {
      const event = new CustomEvent('changevideo', {
        detail: { index: newIndex },
        bubbles: true,
      });
      this.container.dispatchEvent(event);
    }
    this.currentIndex = newIndex;
    const finalY = -this.currentIndex * this.containerHeight;
    this.setWrapperY(finalY);
  }

  setFullscreenMode(isFullscreen, onFullscreenTap = null) {
    this.isFullscreenMode = isFullscreen;
    this.onFullscreenTap = onFullscreenTap;
    console.log('[GestureManager] Fullscreen mode set to:', isFullscreen);
  }

  setWrapperY(y) {
    this.wrapper.style.transform = `translateY(${y}px)`;
  }

  destroy() {
    console.log('[GestureManager] Destroying gesture manager');

    // Clean up Hammer.js instance
    if (this.hammer) {
      this.hammer.destroy();
      this.hammer = null;
    }

    // Remove event listeners
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
      this.handleResize = null;
    }
    if (this.handleContextMenu && this.container) {
      this.container.removeEventListener('contextmenu', this.handleContextMenu);
      this.handleContextMenu = null;
    }

    // Clear references to prevent memory leaks
    this.container = null;
    this.wrapper = null;
    this.onTap = null;
    this.onDoubleTap = null;
    this.onLongPress = null;
    this.onFullscreenTap = null;
    this.getTotalVideos = null;

    console.log('[GestureManager] Gesture manager destroyed');
  }
} 