import './style.css'
import { Player } from './player/Player.js';
import { apiService } from './services/ApiService.js';

// Global language state
let currentLanguage = 'en'; // default to English
window.currentLanguage = currentLanguage;

// --- This part simulates the main application environment ---

// 1. Initial data will be provided by external API (React Native)
let initialVideos = [];

// 2. Fetch next page from external API (provided by React Native)
let externalFetchNextPageFn = null;

function fetchNextPageFromServer() {
  if (externalFetchNextPageFn) {
    return externalFetchNextPageFn();
  }

  // Fallback: return empty array if no external function provided
  console.log('No external fetch function provided, returning empty array');
  return Promise.resolve([]);
}

// --- End of main application simulation ---

// Global function to initialize player with external data
window.initializePlayer = (videoData, startIndex = 0, fetchMoreFn = null) => {
  console.log('Initializing player with external data:', videoData.length, 'videos');

  // 后端返回完整URL，直接使用（与M2项目一致）
  initialVideos = videoData;
  externalFetchNextPageFn = fetchMoreFn;

  const appContainer = document.querySelector('#app');
  if (!appContainer) {
    console.error('App container #app not found!');
    return;
  }

  // Launch the player with external data
  const player = new Player(appContainer, initialVideos, startIndex, fetchNextPageFromServer);

  // Store player reference globally for language updates
  window.playerInstance = player;
};

// --- RN Communication Bridge ---

// Promise-based mechanism to request the next page of videos from RN
let messageId = 0;
const pendingPromises = new Map();

const fetchNextPageFromRN = () => {
  console.log('M1 Player: Requesting next page from RN...');
  return new Promise((resolve, reject) => {
    const currentMessageId = messageId++;
    pendingPromises.set(currentMessageId, { resolve, reject });
    
    // Check if ReactNativeWebView is available
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'FETCH_NEXT_PAGE',
        id: currentMessageId
      }));
    } else {
      // Handle cases where this is not running in a RN WebView
      console.warn('ReactNativeWebView not found. Cannot fetch next page.');
      reject(new Error('ReactNativeWebView not available'));
    }

    // Optional: Add a timeout for the request
    setTimeout(() => {
      if (pendingPromises.has(currentMessageId)) {
        console.warn(`M1 Player: Fetch next page request (id: ${currentMessageId}) timed out.`);
        pendingPromises.get(currentMessageId).reject(new Error('Request timed out'));
        pendingPromises.delete(currentMessageId);
      }
    }, 10000); // 10-second timeout
  });
};

// Property fetching is now handled directly by ApiService
// No need for RN communication for property data

// --- App Initialization ---

document.addEventListener('DOMContentLoaded', () => {
  // Centralized message listener for all communication from React Native
  window.addEventListener('message', (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('M1 Player: Received message from RN:', data.type);

      switch (data.type) {
        case 'SET_JWT_TOKEN':
          // RN sends JWT token for API authentication
          console.log('M1 Player: Received JWT token from RN');
          apiService.setToken(data.token);
          break;

        case 'INITIALIZE_PLAYER':
          // RN sends initial data to start the player
          if (window.initializePlayer) {
            console.log('M1 Player: Initializing player with data from RN.');
            window.initializePlayer(data.videoData, data.startIndex, fetchNextPageFromRN);
            console.log('M1 Player: Player initialized. Property fetching will use direct API calls.');
          } else {
            console.error('M1 Player: window.initializePlayer function not found!');
          }
          break;
        
        case 'FETCH_NEXT_PAGE_RESULT':
          // RN sends back the next page of videos
          if (pendingPromises.has(data.id)) {
            console.log(`M1 Player: Received next page data from RN for request ${data.id}`);
            pendingPromises.get(data.id).resolve(data.videos);
            pendingPromises.delete(data.id);
          }
          break;
        
        // Property fetching is now handled directly by ApiService
        // No need for FETCH_PROPERTIES_RESULT message handling

        case 'LANGUAGE_CHANGE':
          // RN requests a language update
          console.log(`M1 Player: Received LANGUAGE_CHANGE. New language: ${data.language}`);
          currentLanguage = data.language;
          window.currentLanguage = currentLanguage;
          if (window.playerInstance) {
            window.playerInstance.updateLanguage(currentLanguage);
          }
          break;
      
        default:
          // For debugging, log unknown message types
          console.log('M1 Player: Received unknown message type from RN:', data.type);
      }
    } catch (error) {
      console.error('M1 Player: Failed to parse message in WebView', error);
    }
  });

  // Notify React Native that the WebView is ready and waiting for data
  if (window.ReactNativeWebView) {
    console.log('M1 Player: DOM loaded, notifying RN that WebView is ready.');
    window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'WEBVIEW_READY' }));
  } else {
    // Fallback for non-RN environments
    console.warn('M1 Player: Not in a React Native WebView. Displaying fallback message.');
    const appContainer = document.querySelector('#app');
    if (appContainer) {
      appContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Player is ready. Waiting for data... (Non-RN mode)</div>';
    }
  }
});
