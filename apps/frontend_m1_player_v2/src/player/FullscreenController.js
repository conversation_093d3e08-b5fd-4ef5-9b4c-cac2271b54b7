import { NUM_VIDEO_SLOTS } from '../config/constants.js';

/**
 * FullscreenController
 *
 * This class is responsible for managing all UI and interactions 
 * when the player is in fullscreen mode. It is created when entering 
 * fullscreen and destroyed when exiting.
 */
export class FullscreenController {
  constructor(container, player) {
    this.container = container; // The main #app container
    this.player = player;       // Reference to the main Player instance

    this.controlsVisible = false;
    this.hideControlsTimeout = null;

    console.log('[FullscreenController] Initialized');
    this.init();
  }

  init() {
    this.video = this.player.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');

    // Create a container for all fullscreen controls
    this.controlsContainer = document.createElement('div');
    this.controlsContainer.className = 'fullscreen-controls';

    // Create and append UI components
    this.topBar = this.createTopBar();
    this.bottomBar = this.createBottomBar();
    this.playButton = this.createPlayButton();
    
    this.controlsContainer.appendChild(this.topBar);
    this.controlsContainer.appendChild(this.bottomBar);
    this.controlsContainer.appendChild(this.playButton);

    this.container.appendChild(this.controlsContainer);

    // Set up event listeners
    this.boundHandleActionsClick = this.handleActionsClick.bind(this);
    this.actionsContainer.addEventListener('click', this.boundHandleActionsClick);

    this.boundUpdatePlayButton = this.updatePlayButton.bind(this);
    this.video.addEventListener('play', this.boundUpdatePlayButton);
    this.video.addEventListener('pause', this.boundUpdatePlayButton);
  }

  createTopBar() {
    const topBar = document.createElement('div');
    topBar.className = 'fs-top-bar';

    const backButton = this.createBackButton();
    
    const title = document.createElement('div');
    title.className = 'fs-video-title';
    const currentVideoData = this.player.dataManager.getVideo(this.player.currentIndex);
    
    // Correctly handle multilingual titles
    if (currentVideoData && typeof currentVideoData.title === 'object') {
      const currentLang = window.currentLanguage || 'en';
      title.textContent = currentVideoData.title[currentLang] || currentVideoData.title.en || '';
    } else {
      title.textContent = currentVideoData?.title || '';
    }

    topBar.appendChild(backButton);
    topBar.appendChild(title);
    
    return topBar;
  }

  createBottomBar() {
    const bottomBar = document.createElement('div');
    bottomBar.className = 'fs-bottom-bar';
    
    // Create and set up the progress bar
    this.progressContainer = document.createElement('div');
    this.progressContainer.className = 'fs-progress-container';
    this.setupFullscreenProgressBar(this.progressContainer);
    
    // Create and set up the action buttons
    this.actionsContainer = this.createActionButtons();

    bottomBar.appendChild(this.progressContainer);
    bottomBar.appendChild(this.actionsContainer);

    return bottomBar;
  }

  createPlayButton() {
    const button = document.createElement('div');
    button.className = 'fs-play-button fs-interactive';
    
    // Set initial icon state
    const iconName = this.player.isPlaying ? 'pause' : 'play_button';
    button.innerHTML = `<img src="/icons/play/${iconName}.svg" alt="Play/Pause">`;

    button.addEventListener('click', (e) => {
      e.stopPropagation();
      this.resetHideTimeout();
      this.player.togglePlayPause();
    });

    return button;
  }

  updatePlayButton() {
    if (!this.playButton) {return;}
    const iconName = this.player.isPlaying ? 'pause' : 'play_button';
    this.playButton.innerHTML = `<img src="/icons/play/${iconName}.svg" alt="Play/Pause">`;
  }

  createActionButtons() {
    const actions = document.createElement('div');
    actions.className = 'fs-actions-container fs-interactive';

    const currentVideoData = this.player.dataManager.getVideo(this.player.currentIndex);
    if (!currentVideoData) {return actions;}

    const { userState } = currentVideoData;

    const buttons = [
      { id: 'like', icon: userState.isLiked ? 'is_like' : 'like', path: 'like' },
      { id: 'collect', icon: userState.isCollected ? 'is_collect' : 'collect', path: 'collect' },
      { id: 'share', icon: 'share', path: 'share' },
      { id: 'more', icon: 'more', path: 'more' },
    ];

    buttons.forEach(btn => {
      const buttonEl = document.createElement('div');
      buttonEl.className = 'fs-action-button';
      buttonEl.dataset.action = btn.id;
      buttonEl.innerHTML = `<img src="/icons/${btn.path}/${btn.icon}.svg" alt="${btn.id}">`;
      actions.appendChild(buttonEl);
    });

    return actions;
  }

  async handleActionsClick(event) {
    const button = event.target.closest('.fs-action-button');
    if (!button) {return;}

    this.resetHideTimeout();

    const action = button.dataset.action;
    if (!action) {return;}

    const currentVideoData = this.player.dataManager.getVideo(this.player.currentIndex);
    if (!currentVideoData) {return;}

    switch (action) {
      case 'like': {
        console.log('[Fullscreen] Like action triggered');
        // 委托给DataManager处理，不直接修改状态
        const newLikedState = await this.player.dataManager.toggleVideoLike(currentVideoData.id);

        // 更新全屏按钮图标
        const img = button.querySelector('img');
        if (img) {img.src = `/icons/like/${newLikedState ? 'is_like' : 'like'}.svg`;}
        break;
      }
      case 'collect': {
        console.log('[Fullscreen] Collect action triggered');
        // 委托给DataManager处理，不直接修改状态
        const newCollectedState = await this.player.dataManager.toggleVideoCollection(currentVideoData.id);

        // 更新全屏按钮图标
        const img = button.querySelector('img');
        if (img) {img.src = `/icons/collect/${newCollectedState ? 'is_collect' : 'collect'}.svg`;}
        break;
      }
      case 'share':
        console.log('[Fullscreen] Share action triggered');
        // Future implementation: this.player.share()
        break;
      case 'more':
        // Reuse the long-press logic to show the bottom menu
        this.player.handleLongPress();
        break;
    }
  }

  setupFullscreenProgressBar(container) {
    container.innerHTML = `
      <div class="fs-seek-bar">
        <div class="fs-seek-bar-track">
          <div class="fs-seek-bar-fill"></div>
          <div class="fs-seek-bar-thumb"></div>
        </div>
      </div>
      <div class="fs-time-display">00:00 / 00:00</div>
    `;

    const video = this.player.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');
    const seekBar = container.querySelector('.fs-seek-bar');
    const seekBarFill = container.querySelector('.fs-seek-bar-fill');
    const seekBarThumb = container.querySelector('.fs-seek-bar-thumb');
    const timeDisplay = container.querySelector('.fs-time-display');

    if (!video || !seekBar) {return;}

    this.boundUpdateProgress = () => {
      if (isNaN(video.duration)) {return;}
      const percentage = (video.currentTime / video.duration) * 100;
      seekBarFill.style.width = `${percentage}%`;
      seekBarThumb.style.left = `${percentage}%`;
      timeDisplay.textContent = `${this.player.formatTime(video.currentTime)} / ${this.player.formatTime(video.duration)}`;
    };

    this.boundOnMetadataLoaded = () => this.boundUpdateProgress();
    
    video.addEventListener('timeupdate', this.boundUpdateProgress);
    video.addEventListener('loadedmetadata', this.boundOnMetadataLoaded);

    let isSeeking = false;
    const handleSeek = (event) => {
      if (isNaN(video.duration)) {return;}
      const seekBarRect = seekBar.getBoundingClientRect();
      const clickPosition = (event.clientX - seekBarRect.left) / seekBarRect.width;
      video.currentTime = Math.max(0, Math.min(clickPosition * video.duration, video.duration));
      this.boundUpdateProgress();
    };

    const onPointerMove = (moveEvent) => {
      if (isSeeking) {
        this.resetHideTimeout();
        handleSeek(moveEvent);
      }
    };

    const onPointerUp = () => {
      if(isSeeking) {
        isSeeking = false;
        video.play().catch(e => console.warn("Play after seek failed", e));
        document.removeEventListener('pointermove', onPointerMove);
        document.removeEventListener('pointerup', onPointerUp);
      }
    };

    this.boundPointerDown = (e) => {
      e.stopPropagation();
      this.resetHideTimeout();
      isSeeking = true;
      video.pause();
      handleSeek(e);
      document.addEventListener('pointermove', onPointerMove);
      document.addEventListener('pointerup', onPointerUp);
    };
    
    seekBar.addEventListener('pointerdown', this.boundPointerDown);
  }

  createBackButton() {
    const button = document.createElement('div');
    // fs-interactive class prevents handleTap from hiding controls when the button is clicked
    button.className = 'fs-back-button fs-interactive';
    button.innerHTML = `<img src="/icons/back/back.svg" alt="Back">`;

    this.boundExitFullscreen = () => this.player.exitFullscreen();
    button.addEventListener('click', this.boundExitFullscreen);

    return button;
  }
  
  toggleControls(forceShow = false) {
    if (forceShow) {
      this.controlsVisible = true;
    } else {
      this.controlsVisible = !this.controlsVisible;
    }

    // This is a placeholder for where we'll add the DOM manipulation
    this.controlsContainer.classList.toggle('visible', this.controlsVisible);
    console.log(`[FullscreenController] Toggling controls. Visible: ${this.controlsVisible}`);

    this.resetHideTimeout();
  }

  resetHideTimeout() {
    // Clear any existing hide timeout
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
      this.hideControlsTimeout = null;
    }

    // If controls are visible, set a new timeout to hide them
    if (this.controlsVisible) {
      this.hideControlsTimeout = setTimeout(() => {
        this.toggleControls(false);
      }, 4000); // 4-second auto-hide
    }
  }

  destroy() {
    console.log('[FullscreenController] Destroying...');

    // Clear any pending timeouts
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
    }
    
    const video = this.player.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');
    if (video) {
      if (this.boundUpdateProgress) {video.removeEventListener('timeupdate', this.boundUpdateProgress);}
      if (this.boundOnMetadataLoaded) {video.removeEventListener('loadedmetadata', this.boundOnMetadataLoaded);}
    }
    if (this.progressContainer) {
      const seekBar = this.progressContainer.querySelector('.fs-seek-bar');
      if (seekBar && this.boundPointerDown) {
        seekBar.removeEventListener('pointerdown', this.boundPointerDown);
      }
    }
    if (this.actionsContainer && this.boundHandleActionsClick) {
      this.actionsContainer.removeEventListener('click', this.boundHandleActionsClick);
    }

    if (this.video) {
      this.video.removeEventListener('play', this.boundUpdatePlayButton);
      this.video.removeEventListener('pause', this.boundUpdatePlayButton);
    }

    // Remove any UI elements this controller created
    if (this.controlsContainer) {
      this.controlsContainer.remove();
    }
  }
} 