/**
 * @file Manages all interactions with the backend API.
 * Handles video interactions, user states, and property data.
 */

// Property fetching is handled directly by this service
// No external dependencies needed

/**
 * API Service for backend interactions
 */
class ApiService {
  constructor() {
    const base = import.meta.env.VITE_API_BASE_URL;
    if (!base) {
      const msg = 'VITE_API_BASE_URL is not defined. Please set it in .env(.development) at repo root.';
      console.error(msg);
      throw new Error(msg);
    }
    this.baseURL = base;
    this.token = null;
    // 后端返回完整URL，直接使用（与M2项目一致）
  }

  /**
   * Set JWT token for authentication
   * @param {string} token - JWT token
   */
  setToken(token) {
    this.token = token;
    console.log('ApiService: Token updated');
  }

  /**
   * Get authentication headers
   * @returns {Object}
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make API request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise}
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: this.getHeaders(),
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.ok !== 1) {
        throw new Error(data.err || data.msg || 'API请求失败');
      }

      return data.data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * Create interaction event
   * @param {string} videoId - Video ID
   * @param {string} type - Interaction type
   * @param {Object} meta - Metadata
   * @returns {Promise<boolean>}
   */
  async createInteraction(videoId, type, meta = {}) {
    try {
      await this.request('/video/public/interactions', {
        method: 'POST',
        body: JSON.stringify({
          videoId,
          type,
          meta,
        }),
      });
      return true;
    } catch (error) {
      console.error('Create interaction failed:', error);
      return false;
    }
  }

  /**
   * Get user states for videos
   * @param {Array<string>} videoIds - Video IDs
   * @returns {Promise<Array>}
   */
  async getUserStates(videoIds) {
    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      return [];
    }

    try {
      const userId = this.extractUserIdFromToken();
      if (!userId) {
        console.warn('No user ID found in token');
        return [];
      }

      const data = await this.request('/video/public/states/batch', {
        method: 'POST',
        body: JSON.stringify({
          userId,
          videoIds,
        }),
      });

      // 后端返回格式：{ states: [...], count: 2 }
      if (data && data.states && Array.isArray(data.states)) {
        return data.states;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('ApiService: Invalid states response format:', data);
        return [];
      }
    } catch (error) {
      console.error('Get user states failed:', error);
      return [];
    }
  }

  /**
   * Extract user ID from JWT token
   * @returns {string|null}
   */
  extractUserIdFromToken() {
    if (!this.token) {return null;}

    try {
      const payload = JSON.parse(atob(this.token.split('.')[1]));
      return payload.sub;
    } catch (error) {
      console.error('Failed to parse JWT token:', error);
      return null;
    }
  }

  /**
   * Get properties by IDs (returns array)
   * @param {Array<string>} propertyIds - Property IDs
   * @returns {Promise<Array>}
   */
  async getPropertiesByIds(propertyIds) {
    if (!Array.isArray(propertyIds) || propertyIds.length === 0) {
      return [];
    }

    try {
      const data = await this.request('/video/admin/properties/batch-get', {
        method: 'POST',
        body: JSON.stringify({ ids: propertyIds }),
      });
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Get properties failed:', error);
      return [];
    }
  }
}

export const apiService = new ApiService();
export { ApiService };

/**
 * Compatibility export for existing callers (e.g., HouseDrawer)
 * Returns { ok: 1, data: [...] } shape
 */
export async function fetchPropertiesByIds(propertyIds) {
  try {
    const properties = await apiService.getPropertiesByIds(propertyIds);
    return { ok: 1, data: properties };
  } catch (error) {
    console.error('fetchPropertiesByIds failed:', error);
    return { ok: 1, data: [] };
  }
}