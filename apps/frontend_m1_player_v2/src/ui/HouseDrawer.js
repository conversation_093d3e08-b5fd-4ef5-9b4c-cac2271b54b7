import Hammer from 'hammerjs';
import { fetchPropertiesByIds } from '../services/ApiService.js';

// Icons are now served from public directory
const bedIcon = '/icons/property/bedroom.svg';
const bathIcon = '/icons/property/bathroom.svg';
const parkingIcon = '/icons/property/parking.svg';
const houseIcon = '/icons/property/house.svg';

const AUTO_CLOSE_DELAY = 3000; // 3 seconds

export class HouseDrawer {
  constructor(container) {
    this.container = container; // This is the .house-drawer-container
    this.drawer = null;
    this.drawerContainer = null; // Initialize property
    this.cards = [];
    this.toggleButton = null;
    this.autoCloseTimer = null;
    this.hasBeenInteractedWith = false;
    this.isOpen = false;
    this.currentIndex = 0;
    this.hammer = null;
  }

  async load(propertyIds) {
    this.destroy(); // Clean up previous state

    if (!propertyIds || propertyIds.length === 0) {
      return; // No properties, do nothing
    }

    try {
      const response = await fetchPropertiesByIds(propertyIds);
      if (response.ok && response.data.length > 0) {
        this.container.classList.add('drawer-visible');
        this._render(response.data);
        this._autoOpenAndScheduleClose();
      }
    } catch (error) {
      console.error('[HouseDrawer] Failed to fetch properties:', error);
    }
  }

  _render(properties) {
    this.hasBeenInteractedWith = false;
    this.isOpen = false;
    this.currentIndex = 0;
    this.cards = [];

    // The container is passed in, clear it before rendering new content
    this.container.innerHTML = '';

    // Create drawer (the "filmstrip") and add it to the container
    this.drawer = document.createElement('div');
    this.drawer.className = 'house-drawer';
    this.container.appendChild(this.drawer);

    properties.forEach((prop, index) => {
      const card = this._createPropertyCard(prop, index);
      this.cards.push(card);
      this.drawer.appendChild(card);
    });
    console.log(this.cards);

    // Create toggle button and add it as a SIBLING to the container
    this.toggleButton = document.createElement('div');
    this.toggleButton.className = 'house-drawer-toggle';
    this.toggleButton.innerHTML = `<img src="${houseIcon}" alt="Open Properties"/>`;

    if (this.container.parentNode) {
      // Insert it after the container to be a sibling
      this.container.parentNode.insertBefore(this.toggleButton, this.container.nextSibling);
    } else {
        console.error('[HouseDrawer] Container has no parent node, cannot append toggle button.');
    }

    // Initialize Hammer on the filmstrip itself
    this.hammer = new Hammer.Manager(this.drawer);
    this._addEventListeners();
  }

  _createPropertyCard(prop, index) {
    const card = document.createElement('div');
    card.className = 'property-card';
    card.style.transform = `translateX(${index * 100}%)`; // Position cards side-by-side

    card.addEventListener('click', (_e) => {
      // Logic to prevent click during drag will be in gesture handler
      window.open(prop.webUrl, '_blank');
    });

    card.innerHTML = `
      <div class="property-card-thumb">
        <img src="${prop.thumbUrl}" alt="Property Image">
      </div>
      <div class="property-card-info">
        <div class="property-card-price">${prop.price}</div>
        <div class="property-card-specs">
          <div class="spec-item">
            <img src="${bedIcon}" />
            <span>${prop.bedroom}</span>
          </div>
          <div class="spec-item">
            <img src="${bathIcon}" />
            <span>${prop.bathroom}</span>
          </div>
          <div class="spec-item">
            <img src="${parkingIcon}" />
            <span>${prop.parking}</span>
          </div>
        </div>
        <div class="property-card-address">${prop.city}, ${prop.prov}</div>
      </div>
    `;
    return card;
  }

  _addEventListeners() {
    this.toggleButton.addEventListener('click', () => {
      this.hasBeenInteractedWith = true;
      if (this.isOpen) {
        this.hide();
      } else {
        this.show();
      }
    });

    this._addDragListeners();
  }

  _addDragListeners() {
    this.hammer.add(new Hammer.Pan({ direction: Hammer.DIRECTION_HORIZONTAL, threshold: 10 }));

    let startXPositions = [];
    let isDragging = false;

    this.hammer.on('panstart', (_e) => {
      isDragging = false;
      this.hasBeenInteractedWith = true;
      clearTimeout(this.autoCloseTimer);
      startXPositions = this.cards.map(card => {
        const transform = new DOMMatrix(getComputedStyle(card).transform);
        return transform.m41; // Get the current translateX value
      });
      this.cards.forEach(card => card.style.transition = 'none');
    });

    this.hammer.on('panmove', (e) => {
      const deltaX = e.deltaX;
      if (Math.abs(deltaX) > 5) {isDragging = true;} // Confirms a drag action

      this.cards.forEach((card, index) => {
        card.style.transform = `translateX(${startXPositions[index] + deltaX}px)`;
      });
    });

    this.hammer.on('panend', (e) => {
      this.cards.forEach(card => card.style.transition = 'transform 0.3s ease-out');
      
      const cardWidth = this.drawer.offsetWidth;
      const dragThreshold = cardWidth * 0.3;

      if (e.deltaX < -dragThreshold) {
        this.currentIndex++; // Swiped left
      } else if (e.deltaX > dragThreshold) {
        this.currentIndex--; // Swiped right
      }

      this.snapTo(this.currentIndex);

      // Prevent click after drag, with a null check
      if (isDragging) {
        const draggedCard = e.srcEvent.target.closest('.property-card');
        if (draggedCard) { // Check if a card was actually dragged
          draggedCard.style.pointerEvents = 'none';
        }
        // Re-enable pointer events on all cards after a short delay
        setTimeout(() => {
          this.cards.forEach(card => card.style.pointerEvents = 'auto');
        }, 300);
      }
    });
  }
  
  snapTo(index) {
    this.currentIndex = Math.max(0, Math.min(index, this.cards.length - 1));
    const cardWidth = this.drawer.offsetWidth;

    this.cards.forEach((card, i) => {
      const targetX = (i - this.currentIndex) * cardWidth;
      card.style.transform = `translateX(${targetX}px)`;
    });
  }

  _autoOpenAndScheduleClose() {
    // Use a timeout to allow the element to be in the DOM before animating
    setTimeout(() => {
      this.show(true); // Show it and indicate it's an auto-show
    }, 100);
  }

  show(isAuto = false) {
    clearTimeout(this.autoCloseTimer);
    this.container.classList.add('drawer-visible');
    this.isOpen = true;

    if (isAuto && !this.hasBeenInteractedWith) {
      this.autoCloseTimer = setTimeout(() => {
        this.hide();
      }, AUTO_CLOSE_DELAY);
    }
  }

  hide() {
    clearTimeout(this.autoCloseTimer);
    this.container.classList.remove('drawer-visible');
    this.isOpen = false;
  }

  destroy() {
    clearTimeout(this.autoCloseTimer);
    if (this.hammer) {
      this.hammer.destroy();
      this.hammer = null;
    }

    // Safely remove the toggle button created by this component
    if (this.toggleButton && this.toggleButton.parentNode) {
      this.toggleButton.parentNode.removeChild(this.toggleButton);
    }

    // Clear the contents of the container provided to us and hide it
    if (this.container) {
      this.container.innerHTML = '';
      this.container.classList.remove('drawer-visible');
    }

    this.drawer = null;
    this.cards = [];
    this.toggleButton = null;
  }
} 