import { videoLoader } from '../core/VideoLoader.js';

// Icons are now served from public directory
const playIcon = '/icons/play/play_button.svg';
const likeIcon = '/icons/like/like.svg';
const isLikeIcon = '/icons/like/is_like.svg';
const collectIcon = '/icons/collect/collect.svg';
const isCollectIcon = '/icons/collect/is_collect.svg';
const shareIcon = '/icons/share/share.svg';
const fullScreenIcon = '/icons/full_screen/full_screen.svg';
const moreIcon = '/icons/more/more.svg';

/**
 * Creates the video player UI structure efficiently using a DocumentFragment.
 * This includes a wrapper and the individual video item slots.
 * @param {number} numberOfSlots The number of reusable video item elements to create.
 * @param {HTMLElement} container The main application container element.
 * @returns {{wrapper: HTMLElement, videoItems: HTMLElement[]}}
 */
export function createVideoElements(numberOfSlots, container) {
  const fragment = document.createDocumentFragment();
  
  const wrapper = document.createElement('div');
  wrapper.className = 'video-wrapper';

  const videoItems = [];
  for (let i = 0; i < numberOfSlots; i++) {
    const videoItem = document.createElement('div');
    videoItem.className = 'video-item';

    const video = document.createElement('video');
    video.className = 'video-player';
    video.loop = true;
    video.playsInline = true;
    video.muted = true;
    video.autoplay = true;
    video.preload = 'metadata'; // Changed from 'auto' to 'metadata' for better performance
    // Additional mobile WebView attributes
    video.setAttribute('playsinline', 'true');
    video.setAttribute('webkit-playsinline', 'true');
    video.setAttribute('x5-playsinline', 'true');
    video.setAttribute('x5-video-player-type', 'h5-page');

    const icon = document.createElement('img');
    icon.src = playIcon;
    icon.className = 'play-icon';
    icon.style.display = 'none'; // Initially hidden

    const seekIndicator = document.createElement('div');
    seekIndicator.className = 'seek-indicator';

    const overlay = document.createElement('div');
    overlay.className = 'video-overlay';

    // --- Video Info (Title, Description) ---
    const infoContainer = document.createElement('div');
    infoContainer.className = 'video-info';

    const titleElement = document.createElement('div');
    titleElement.className = 'video-title';

    const descriptionWrapper = document.createElement('div');
    descriptionWrapper.className = 'video-description-wrapper';
    
    const descriptionElement = document.createElement('span');
    descriptionElement.className = 'video-description';

    const moreToggle = document.createElement('span');
    moreToggle.className = 'more-toggle';
    moreToggle.textContent = 'More';

    descriptionWrapper.appendChild(descriptionElement);
    descriptionWrapper.appendChild(moreToggle);
    infoContainer.appendChild(titleElement);
    infoContainer.appendChild(descriptionWrapper);

    // --- Progress Bar ---
    const progressContainer = document.createElement('div');
    progressContainer.className = 'progress-container';
    
    const timeDisplay = document.createElement('span');
    timeDisplay.className = 'time-display';
    timeDisplay.textContent = '0:00 / 0:00';

    const seekBar = document.createElement('div');
    seekBar.className = 'seek-bar';
    seekBar.innerHTML = `
      <div class="seek-bar-track">
        <div class="seek-bar-fill"></div>
        <div class="seek-bar-thumb"></div>
      </div>
    `;

    progressContainer.appendChild(timeDisplay);
    progressContainer.appendChild(seekBar);

    // --- House Drawer ---
    const houseDrawerContainer = document.createElement('div');
    houseDrawerContainer.className = 'house-drawer-container';
    // This container will be populated by HouseDrawer.js

    
    // --- Action Sidebar ---
    const sidebar = document.createElement('div');
    sidebar.className = 'video-sidebar';

    // Like
    const likeButton = document.createElement('div');
    likeButton.className = 'sidebar-button like-button';
    likeButton.dataset.action = 'like';
    likeButton.innerHTML = `
      <img src="${likeIcon}" class="icon-like" />
      <img src="${isLikeIcon}" class="icon-is-like" style="display: none;" />
      <span class="count like-count">0</span>
    `;

    // Collect
    const collectButton = document.createElement('div');
    collectButton.className = 'sidebar-button collect-button';
    collectButton.dataset.action = 'collect';
    collectButton.innerHTML = `
      <img src="${collectIcon}" class="icon-collect" />
      <img src="${isCollectIcon}" class="icon-is-collect" style="display: none;" />
      <span class="count collect-count">0</span>
    `;

    // Share
    const shareButton = document.createElement('div');
    shareButton.className = 'sidebar-button share-button';
    shareButton.dataset.action = 'share';
    shareButton.innerHTML = `<img src="${shareIcon}" />`;

    // More (placeholder)
    const moreButton = document.createElement('div');
    moreButton.className = 'sidebar-button more-button';
    moreButton.dataset.action = 'more';
    moreButton.innerHTML = `<img src="${moreIcon}" />`;

    // Fullscreen (placeholder)
    const fullscreenButton = document.createElement('div');
    fullscreenButton.className = 'sidebar-button fullscreen-button';
    fullscreenButton.dataset.action = 'fullscreen';
    fullscreenButton.innerHTML = `<img src="${fullScreenIcon}" />`;

    sidebar.appendChild(likeButton);
    sidebar.appendChild(collectButton);
    sidebar.appendChild(shareButton);
    sidebar.appendChild(moreButton);
    sidebar.appendChild(fullscreenButton);

    videoItem.appendChild(video);
    videoItem.appendChild(overlay);
    videoItem.appendChild(icon);
    videoItem.appendChild(seekIndicator);
    videoItem.appendChild(infoContainer);
    videoItem.appendChild(progressContainer);
    videoItem.appendChild(houseDrawerContainer);
    videoItem.appendChild(sidebar);
    wrapper.appendChild(videoItem);
    videoItems.push(videoItem);
  }
  
  fragment.appendChild(wrapper);
  container.appendChild(fragment);

  return { wrapper, videoItems };
}

/**
 * Updates a specific video item slot with new video data.
 * @param {HTMLElement} videoItem The video item element to update.
 * @param {object} videoData The new video data.
 * @param {string} language The current language code (e.g., 'en', 'zh').
 */
export function updateVideoSlot(videoItem, videoData, language) {
  if (!videoItem) {return;}

  const video = videoItem.querySelector('video');

  if (!videoData) {
    // Clear the slot and clean up video
    cleanupVideo(video);
    clearVideoSlot(videoItem);
    return;
  }

  if (video.src !== videoData.videoUrl) {
    // Clean up previous video before loading new one
    cleanupVideo(video);

    // Use VideoLoader to handle both HLS and MP4
    videoLoader.loadVideo(video, videoData.videoUrl)
      .then(() => {
        console.log('Video loaded successfully:', videoData.videoUrl);
        // Reset the time display when new video is loaded
        const timeDisplay = videoItem.querySelector('.time-display');
        if (timeDisplay) {
          timeDisplay.textContent = '0:00 / 0:00';
        }
      })
      .catch(error => {
        console.error('Video load failed:', error);
        // Show error state or fallback
        const timeDisplay = videoItem.querySelector('.time-display');
        if (timeDisplay) {
          timeDisplay.textContent = 'Load Error';
        }
      });
  }

  // Update title and description (support multilingual)
  const title = videoItem.querySelector('.video-title');
  if (typeof videoData.title === 'object') {
    title.textContent = videoData.title[language] || videoData.title.en || 'No title';
  } else {
    title.textContent = videoData.title || 'No title';
  }

  const description = videoItem.querySelector('.video-description');
  if (typeof videoData.description === 'object') {
    description.textContent = videoData.description[language] || videoData.description.en || 'No description';
  } else {
    description.textContent = videoData.description || 'No description';
  }

  // Update sidebar data
  const likeCount = videoItem.querySelector('.like-count');
  likeCount.textContent = videoData.stats.likes;

  const collectCount = videoItem.querySelector('.collect-count');
  collectCount.textContent = videoData.stats.collections;

  const likeButton = videoItem.querySelector('.like-button');
  const iconLike = likeButton.querySelector('.icon-like');
  const iconIsLike = likeButton.querySelector('.icon-is-like');
  if (videoData.userState.isLiked) {
    iconLike.style.display = 'none';
    iconIsLike.style.display = 'block';
  } else {
    iconLike.style.display = 'block';
    iconIsLike.style.display = 'none';
  }

  const collectButton = videoItem.querySelector('.collect-button');
  const iconCollect = collectButton.querySelector('.icon-collect');
  const iconIsCollect = collectButton.querySelector('.icon-is-collect');
  if (videoData.userState.isCollected) {
    iconCollect.style.display = 'none';
    iconIsCollect.style.display = 'block';
  } else {
    iconCollect.style.display = 'block';
    iconIsCollect.style.display = 'none';
  }

  // Control fullscreen button visibility based on video dimensions
  const fullscreenButton = videoItem.querySelector('.fullscreen-button');
  if (fullscreenButton && videoData.width && videoData.height) {
    // Hide fullscreen button for portrait videos (height > width)
    if (videoData.height > videoData.width) {
      fullscreenButton.style.display = 'none';
    } else {
      fullscreenButton.style.display = 'flex';
    }
  } else if (fullscreenButton) {
    // Default to showing fullscreen button if dimensions are not available
    fullscreenButton.style.display = 'flex';
  }
}

/**
 * Clean up video element to prevent memory leaks
 */
function cleanupVideo(video) {
  if (video) {
    // Clean up HLS instances first
    videoLoader.cleanup(video);

    // Standard video cleanup
    if (video.src) {
      video.pause();
      video.currentTime = 0;
      // Remove src and load to free memory
      video.removeAttribute('src');
      video.load();
    }
  }
}

/**
 * Clear all content from a video slot
 */
function clearVideoSlot(videoItem) {
  const title = videoItem.querySelector('.video-title');
  const description = videoItem.querySelector('.video-description');
  const likeCount = videoItem.querySelector('.like-count');
  const collectCount = videoItem.querySelector('.collect-count');
  const timeDisplay = videoItem.querySelector('.time-display');

  if (title) {title.textContent = '';}
  if (description) {description.textContent = '';}
  if (likeCount) {likeCount.textContent = '0';}
  if (collectCount) {collectCount.textContent = '0';}
  if (timeDisplay) {timeDisplay.textContent = '0:00 / 0:00';}

  // Reset button states
  const likeButton = videoItem.querySelector('.like-button');
  const collectButton = videoItem.querySelector('.collect-button');

  if (likeButton) {
    const iconLike = likeButton.querySelector('.icon-like');
    const iconIsLike = likeButton.querySelector('.icon-is-like');
    if (iconLike) {iconLike.style.display = 'block';}
    if (iconIsLike) {iconIsLike.style.display = 'none';}
  }

  if (collectButton) {
    const iconCollect = collectButton.querySelector('.icon-collect');
    const iconIsCollect = collectButton.querySelector('.icon-is-collect');
    if (iconCollect) {iconCollect.style.display = 'block';}
    if (iconIsCollect) {iconIsCollect.style.display = 'none';}
  }
}