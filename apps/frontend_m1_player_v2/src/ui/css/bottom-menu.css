/* --- Bottom Sheet Styles --- */
.bottom-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.bottom-sheet-backdrop.visible {
  opacity: 1;
  pointer-events: auto;
}

.bottom-sheet {
  width: 100%;
  max-width: 400px; /* Max width for wider screens */
  background-color: white;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  z-index: 100;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  padding: 0vh 4vw 1vh; /* Responsive padding (top, sides, bottom) */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.bottom-sheet.visible {
  transform: translateY(0);
}

.sheet-handle {
  width: clamp(35px, 10vw, 45px);
  height: 5px;
  background-color: #ccc;
  border-radius: 2.5px;
  margin: 0 auto clamp(12px, 3vh, 18px);
}

.sheet-content {
  display: flex;
  flex-direction: column;
  gap: clamp(15px, 3vh, 25px);
}

.sheet-row, .sheet-row-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: clamp(12px, 2.5vh, 18px);
  font-size: clamp(15px, 4vw, 18px); /* Responsive font size */
}

.sheet-row-group {
  flex-direction: column;
  align-items: stretch;
  gap: clamp(10px, 2vh, 15px);
}

.sheet-row:last-child, .sheet-row-group:last-child {
  border-bottom: none;
}

/* Toggle Switch Styles */
.toggle-switch {
  width: clamp(50px, 13vw, 60px);
  height: clamp(28px, 7vw, 34px);
  background-color: #e0e0e0;
  border-radius: 99px; /* Pill shape */
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-switch.active {
  background-color: #f44336; /* Red when active */
}

.toggle-knob {
  width: clamp(24px, 6vw, 28px);
  height: clamp(24px, 6vw, 28px);
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: clamp(2px, 0.5vw, 3px);
  left: clamp(2px, 0.5vw, 4px);
  transition: transform 0.3s;
}

.toggle-switch.active .toggle-knob {
  transform: translateX(clamp(22px, 6vw, 28px));
}

/* Speed Options Styles */
.speed-options {
  display: flex;
  gap: 1.5%;
}

.speed-options button {
  flex: 1;
  text-align: center;
  border: 1px solid #ccc;
  background-color: white;
  color: #333;
  border-radius: 99px;
  padding: clamp(5px, 1%, 8px) clamp(5px, 0.5%, 18px); /* Responsive padding */
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  font-size: clamp(7px, 4vw, 10px); /* Responsive font size */
}

.speed-options button.active {
  background-color: #f44336;
  color: white;
  border-color: #f44336;
}

.current-speed {
  color: #f44336;
  font-weight: bold;
} 