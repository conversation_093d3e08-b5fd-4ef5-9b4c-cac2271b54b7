/*
  Styles for the House (Property) Drawer feature.
*/

/* --- Container and Toggle --- */
.house-drawer-container {
  position: absolute;
  left: 0;
  bottom: 20vh; /* Increased to avoid collision with video info */
  width: 70%;
  height: 8%; /* Slightly increased height for better visual */
  z-index: 50;
  overflow: hidden;
  border-radius: 0 12px 12px 0;
  padding: 0;
  box-sizing: border-box;
  transform: translateX(-101%);
  transition: transform 0.4s ease-in-out, bottom 0.01s ease-in-out;
}

.house-drawer-container.drawer-visible {
  transform: translateX(0);
}

.house-drawer {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: grab;
  /* This container itself should not capture pointer events */
  pointer-events: none;
}

.house-drawer-toggle {
  position: absolute;
  left: 10px;
  bottom: 20vh;
  transform: translateY(0%);
  width: 40px;
  height: 8%;
  background-color: rgba(0, 0, 0, 0);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  opacity: 1; /* Always visible */
  transition: left 0.4s ease-in-out, bottom 0.01s ease-in-out;
  z-index: 51;
}

.house-drawer-container.drawer-visible + .house-drawer-toggle {
  left: calc(70% + 10px);
}

.house-drawer-toggle img {
  width: 24px;
  height: 24px;
}

/* --- Property Card --- */
.property-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  background-color: white;
  display: flex;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  cursor: grab;
  transition: transform 0.3s ease-out;
  pointer-events: auto; /* Ensure cards are always interactive */
}
.property-card:last-child {
}

.property-card:active {
  cursor: grabbing;
}

.property-card-thumb {
  width: 45%;
  height: 100%;
  flex-shrink: 0;
}

.property-card-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.property-card-info {
  flex-grow: 1;
  padding: 0 0 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #333;
}

.property-card-price {
  font-size: clamp(18px, 4vw, 22px);
  font-weight: bold;
  color: #d9534f; /* A reddish color for price */
}

.property-card-specs {
  display: flex;
  gap: 10px;
  align-items: center;
  color: #555;
  font-size: 14px;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.spec-item img {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.property-card-address {
  font-size: 13px;
  color: #777;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* --- Scroll Snap --- */
/* This section is now unused because the structure will be simplified */
.house-drawer-scroll-container {
  
}

.house-drawer-scroll-container.snapping {
  /* No longer needed here */
} 