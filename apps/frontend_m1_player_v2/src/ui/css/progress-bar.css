/*
  Styles for the custom progress bar and time display.
*/

.progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 4vw 4.5vh; /* top, sides, bottom */
  box-sizing: border-box;
  display: flex;
  flex-direction: column; /* Stack time over bar */
  align-items: stretch; /* Stretch children to fill width */
  gap: 0.1vh; /* Space between time and bar */
  z-index: 40; /* Ensure it's above other elements */
}

.time-display {
  color: white;
  font-size: clamp(12px, 3vw, 15px);
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
  white-space: nowrap;
}

.seek-bar {
  /* No longer needs flex-grow as it's a block in a column */
  height: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  touch-action: none; /* Prevent browser from hijacking touch gestures */
}

.seek-bar-track {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.seek-bar-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0; /* Updated by JS */
  background-color: white;
  border-radius: 2px;
}

.seek-bar-thumb {
  position: absolute;
  top: 50%;
  left: 0; /* Updated by JS */
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  /* Add a subtle shadow to make it stand out */
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
} 