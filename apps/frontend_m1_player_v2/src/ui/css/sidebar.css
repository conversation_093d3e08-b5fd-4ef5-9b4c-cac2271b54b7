/* --- Sidebar Styles (Responsive) --- */
.video-sidebar {
  position: absolute;
  bottom: 8vh; /* Responsive vertical positioning */
  right: 2vw;   /* Responsive horizontal positioning */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: clamp(12px, 3vh, 22px); /* Responsive gap between buttons */
  z-index: 30;
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.sidebar-button {
  width: clamp(40px, 12vw, 50px); /* Give the button a fixed, responsive width */
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
}

.sidebar-button img {
  /* Responsive icon size */
  width: clamp(18px, 8vw, 36px);
  height: clamp(18px, 8vw, 36px);
  margin-bottom: 4px;
}

.sidebar-button .count {
  /* Responsive font size for counts */
  font-size: clamp(11px, 2.5vw, 14px);
  font-weight: bold;
}

.more-button {
  /* Styling for the '...' placeholder icon */
  font-size: clamp(28px, 8vw, 40px);
  font-weight: bold;
  width: clamp(24px, 6vw, 32px);
  height: clamp(24px, 6vw, 32px);
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 0.5; /* Fine-tune vertical alignment of '...' */
}

/* Fullscreen button - smaller than other buttons */
.fullscreen-button {
  width: clamp(32px, 10vw, 40px); /* Smaller than default sidebar buttons */
}

.fullscreen-button img {
  width: clamp(16px, 7vw, 32px); /* Smaller icon size */
  height: clamp(16px, 7vw, 32px);
  margin-bottom: 4px;
}