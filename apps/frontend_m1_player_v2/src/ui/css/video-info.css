/* 
  Styles for the video information overlay (title and description).
*/

.video-info {
  position: absolute;
  bottom: 8vh; /* Responsive: Leave 5% of viewport height for progress bar */
  left: 0;
  right: 18vw;
  padding: 0vh 0vw 2vh 4vw; /* top/bottom, right, left. Right padding avoids sidebar */
  box-sizing: border-box;
  color: white;
  pointer-events: none; /* Allow clicks to pass through to the video by default */
  z-index: 55; /* Base z-index for the info container */
  background: rgba(0, 0, 0, 0);
}

.video-info > * {
  pointer-events: auto; /* Re-enable pointer events for children like the 'more' button */
}

.video-title {
  font-size: clamp(16px, 4.5vw, 22px); /* Responsive font size */
  font-weight: bold;
  line-height: 1.4;
  
  /* Truncate to 2 lines */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.video-description-wrapper {
  margin-top: 1vh; /* Responsive margin */
  font-size: clamp(14px, 3.8vw, 18px); /* Responsive font size */
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
}

.video-description {
  /* Truncate to a single line by default */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* --- Expanded State --- */
.video-description-wrapper.expanded .video-description {
  white-space: normal;
  word-break: break-word;
  
}

.more-toggle {
  color: #87ceeb; /* A nice blue color */
  font-weight: bold;
  cursor: pointer;
  padding-left: 5px;
  flex-shrink: 0; /* Prevent the button from being shrunk by flexbox */
} 