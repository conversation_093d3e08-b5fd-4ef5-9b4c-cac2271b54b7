/**
 * Autoplay helper for mobile WebView environments
 * Handles autoplay restrictions and provides fallback mechanisms
 */

export class AutoplayHelper {
  static async attemptAutoplay(videoElement, options = {}) {
    const {
      showPlayButton = true,
      hidePlayButton = true,
      maxRetries = 3,
      retryDelay = 500
    } = options;

    if (!videoElement) {
      console.warn('AutoplayHelper: No video element provided');
      return false;
    }

    // Find associated UI elements
    const videoItem = videoElement.closest('.video-item');
    const playIcon = videoItem?.querySelector('.play-icon');
    const overlay = videoItem?.querySelector('.video-overlay');

    let attempts = 0;
    
    const tryPlay = async () => {
      attempts++;
      console.log(`AutoplayHelper: Attempt ${attempts} to play video`);
      
      try {
        // Ensure video is ready
        if (videoElement.readyState < 2) { // HAVE_CURRENT_DATA
          console.log('AutoplayHelper: Video not ready, waiting...');
          await new Promise(resolve => {
            const onReady = () => {
              videoElement.removeEventListener('loadeddata', onReady);
              videoElement.removeEventListener('canplay', onReady);
              resolve();
            };
            videoElement.addEventListener('loadeddata', onReady);
            videoElement.addEventListener('canplay', onReady);
          });
        }

        // Attempt to play
        await videoElement.play();
        
        console.log('AutoplayHelper: Autoplay successful');
        
        // Hide play controls on success
        if (hidePlayButton) {
          if (playIcon) {playIcon.style.display = 'none';}
          if (overlay) {overlay.classList.remove('visible');}
        }
        
        return true;
        
      } catch (error) {
        console.warn(`AutoplayHelper: Attempt ${attempts} failed:`, error.message);
        
        // Show play controls on failure
        if (showPlayButton && attempts >= maxRetries) {
          if (playIcon) {playIcon.style.display = 'block';}
          if (overlay) {overlay.classList.add('visible');}
        }
        
        // Retry if we haven't exceeded max attempts
        if (attempts < maxRetries) {
          console.log(`AutoplayHelper: Retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return tryPlay();
        }
        
        return false;
      }
    };

    return tryPlay();
  }

  /**
   * Set up autoplay for when user interaction occurs
   */
  static setupUserInteractionAutoplay(videoElement) {
    if (!videoElement) {return;}

    const videoItem = videoElement.closest('.video-item');
    if (!videoItem) {return;}

    let hasTriggered = false;

    const triggerAutoplay = () => {
      if (hasTriggered) {return;}
      hasTriggered = true;
      
      console.log('AutoplayHelper: User interaction detected, attempting autoplay');
      
      setTimeout(() => {
        if (videoElement.paused) {
          AutoplayHelper.attemptAutoplay(videoElement, {
            maxRetries: 1,
            retryDelay: 100
          });
        }
      }, 50);
    };

    // Listen for various user interaction events
    const events = ['click', 'touchstart', 'touchend'];
    events.forEach(eventType => {
      document.addEventListener(eventType, triggerAutoplay, { 
        once: true, 
        passive: true 
      });
    });

    // Also listen for video-specific interactions
    videoItem.addEventListener('click', triggerAutoplay, { once: true });
  }

  /**
   * Enhanced autoplay for WebView environments
   */
  static async webViewAutoplay(videoElement) {
    if (!videoElement) {return false;}

    console.log('AutoplayHelper: Starting WebView autoplay sequence');

    // Method 1: Direct autoplay attempt
    const directSuccess = await AutoplayHelper.attemptAutoplay(videoElement, {
      maxRetries: 1,
      retryDelay: 100,
      showPlayButton: false
    });

    if (directSuccess) {
      return true;
    }

    // Method 2: Set up user interaction trigger
    AutoplayHelper.setupUserInteractionAutoplay(videoElement);

    // Method 3: Try again after a short delay (sometimes WebView needs time)
    setTimeout(async () => {
      if (videoElement.paused) {
        console.log('AutoplayHelper: Delayed autoplay attempt');
        await AutoplayHelper.attemptAutoplay(videoElement, {
          maxRetries: 1,
          retryDelay: 0,
          showPlayButton: true
        });
      }
    }, 1000);

    return false;
  }
}
