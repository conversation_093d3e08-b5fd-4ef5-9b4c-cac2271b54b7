/**
 * Performance helper for mobile optimization
 * Handles memory management and performance monitoring
 */

export class PerformanceHelper {
  static memoryCheckInterval = null;
  static performanceStats = {
    videoSwitches: 0,
    memoryWarnings: 0,
    lastCleanup: Date.now()
  };

  /**
   * Initialize performance monitoring
   */
  static init() {
    // Monitor memory usage every 30 seconds
    this.memoryCheckInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000);

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    console.log('[PerformanceHelper] Initialized');
  }

  /**
   * Check memory usage and trigger cleanup if needed
   */
  static checkMemoryUsage() {
    if (performance.memory) {
      const { usedJSHeapSize, jsHeapSizeLimit } = performance.memory;
      const memoryUsagePercent = (usedJSHeapSize / jsHeapSizeLimit) * 100;

      if (memoryUsagePercent > 70) {
        console.warn('[PerformanceHelper] High memory usage:', memoryUsagePercent.toFixed(1) + '%');
        this.performanceStats.memoryWarnings++;
        this.triggerMemoryCleanup();
      }
    }
  }

  /**
   * Trigger memory cleanup
   */
  static triggerMemoryCleanup() {
    console.log('[PerformanceHelper] Triggering memory cleanup');
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }

    // Clean up unused video elements
    this.cleanupUnusedVideos();
    
    this.performanceStats.lastCleanup = Date.now();
  }

  /**
   * Clean up unused video elements
   */
  static cleanupUnusedVideos() {
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      // If video is not currently visible or playing, clean it up
      const videoItem = video.closest('.video-item');
      if (videoItem && !this.isVideoVisible(videoItem)) {
        this.cleanupVideo(video);
      }
    });
  }

  /**
   * Check if video is currently visible
   */
  static isVideoVisible(videoItem) {
    const rect = videoItem.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // Consider video visible if it's within the viewport or close to it
    return rect.top < windowHeight && rect.bottom > -windowHeight;
  }

  /**
   * Clean up a specific video element
   */
  static cleanupVideo(video) {
    if (video && video.src && !video.paused) {
      return; // Don't clean up currently playing video
    }

    if (video && video.src) {
      video.pause();
      video.currentTime = 0;
      video.removeAttribute('src');
      video.load();
    }
  }

  /**
   * Optimize video loading based on position
   */
  static optimizeVideoLoading(videoItems, currentIndex) {
    videoItems.forEach((item, index) => {
      const video = item.querySelector('video');
      if (!video) {return;}

      const distance = Math.abs(index - currentIndex);
      
      if (distance === 0) {
        // Current video - ensure it's loaded
        if (video.preload !== 'auto') {
          video.preload = 'auto';
          video.load();
        }
      } else if (distance === 1) {
        // Adjacent videos - preload metadata only
        if (video.preload !== 'metadata') {
          video.preload = 'metadata';
        }
      } else {
        // Distant videos - no preload
        if (video.preload !== 'none') {
          video.preload = 'none';
          this.cleanupVideo(video);
        }
      }
    });
  }

  /**
   * Throttle function calls for better performance
   */
  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }

  /**
   * Debounce function calls
   */
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Record video switch for performance tracking
   */
  static recordVideoSwitch() {
    this.performanceStats.videoSwitches++;
    
    // Trigger cleanup every 10 video switches
    if (this.performanceStats.videoSwitches % 10 === 0) {
      this.triggerMemoryCleanup();
    }
  }

  /**
   * Get performance statistics
   */
  static getStats() {
    return {
      ...this.performanceStats,
      memoryInfo: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      } : 'Not available'
    };
  }

  /**
   * Clean up performance helper
   */
  static cleanup() {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
    
    // Clean up all videos
    this.cleanupUnusedVideos();
    
    console.log('[PerformanceHelper] Cleaned up');
  }
}
