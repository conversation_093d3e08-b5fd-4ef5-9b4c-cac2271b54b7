import { defineConfig, loadEnv } from 'vite';
import path from 'node:path';

export default defineConfig(({ mode }) => {
  const rootEnvDir = path.resolve(__dirname, '../../');
  const env = loadEnv(mode, rootEnvDir, '');
  const isProd = mode === 'production';

  const devFallback = 'http://192.168.2.162:8080';
  const resolvedApi = env.VITE_API_BASE_URL || env.API_BASE_URL || (isProd ? '' : devFallback);
  const resolvedDebug = env.DEBUG_MODE ?? (!isProd ? 'true' : 'false');
  const resolvedNodeEnv = env.NODE_ENV || (isProd ? 'production' : 'development');

  if (isProd && !resolvedApi) {
    throw new Error('Missing API_BASE_URL (or VITE_API_BASE_URL) for production build. Set it in root .env.production or CI env.');
  }

  return {
    envDir: rootEnvDir,
    define: {
      'import.meta.env.VITE_API_BASE_URL': JSON.stringify(resolvedApi),
      'import.meta.env.VITE_DEBUG_MODE': JSON.stringify(String(resolvedDebug)),
      'import.meta.env.VITE_NODE_ENV': JSON.stringify(resolvedNodeEnv),
    },
    server: {
      host: true,
      port: 5174,
    },
  };
}); 