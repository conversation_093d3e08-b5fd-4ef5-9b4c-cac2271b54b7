# ===========================================
# M2 Uploader 配置模板
# ===========================================
# 复制此文件为 .env 并根据你的环境修改配置值
# 使用方法: cp .env.example .env

# ===========================================
# 基础配置 - 必需配置
# ===========================================

# 后端API服务地址 (必需)
# 开发环境通常是 http://localhost:8080
# 生产环境应该是你的实际API域名
VITE_API_BASE_URL=http://localhost:8080

# 媒体文件服务地址 (必需)
# 通常由nginx提供静态文件服务
VITE_MEDIA_BASE_URL=http://localhost:3000

# API请求超时时间（毫秒）
# 建议值: 5000-30000
VITE_REQUEST_TIMEOUT=10000

# 最大重试次数
# 建议值: 1-5
VITE_MAX_RETRIES=3

# 应用信息
VITE_APP_NAME=M2 Uploader
VITE_APP_VERSION=1.0.0

# ===========================================
# UI 界面配置
# ===========================================

# 每页显示的视频数量
# 建议值: 10-50，过大可能影响性能
VITE_VIDEOS_PER_PAGE=20

# 最大页面大小限制
VITE_MAX_PAGE_SIZE=100

# 状态轮询间隔（毫秒）
# 建议值: 5000-30000，过频繁会增加服务器负载
VITE_STATUS_POLLING_INTERVAL=10000

# 是否启用自动刷新
VITE_ENABLE_AUTO_REFRESH=true

# 最大轮询尝试次数
VITE_MAX_POLLING_ATTEMPTS=10

# 错误消息显示时长（毫秒）
VITE_ERROR_DISPLAY_DURATION=3000

# 成功消息显示时长（毫秒）
VITE_SUCCESS_DISPLAY_DURATION=2000

# 警告消息显示时长（毫秒）
VITE_WARNING_DISPLAY_DURATION=2500

# 是否自动隐藏通知
VITE_AUTO_HIDE_NOTIFICATIONS=true

# 默认错误消息
VITE_DEFAULT_ERROR_MESSAGE=操作失败，请稍后重试

# 默认成功消息
VITE_DEFAULT_SUCCESS_MESSAGE=操作成功

# 默认警告消息
VITE_DEFAULT_WARNING_MESSAGE=请注意

# 默认信息消息
VITE_DEFAULT_INFO_MESSAGE=提示信息

# 信息消息显示时长（毫秒）
VITE_INFO_DISPLAY_DURATION=4000

# 是否启用全局错误处理
VITE_ENABLE_GLOBAL_ERROR_HANDLER=true

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=false

# 每个会话最大错误数量
VITE_MAX_ERRORS_PER_SESSION=10

# ===========================================
# 异步处理配置
# ===========================================

# 默认异步操作超时时间（毫秒）
VITE_DEFAULT_ASYNC_TIMEOUT=30000

# 最大并发请求数
VITE_MAX_CONCURRENT_REQUESTS=3

# 重试延迟（毫秒）
VITE_RETRY_DELAY=1000

# 是否启用请求取消功能
VITE_ENABLE_REQUEST_CANCELLATION=true

# 是否启用加载状态显示
VITE_ENABLE_LOADING_STATES=true

# 防抖延迟（毫秒）
VITE_DEBOUNCE_DELAY=300

# 节流延迟（毫秒）
VITE_THROTTLE_DELAY=300

# 批处理大小
VITE_BATCH_SIZE=5

# 模态框点击遮罩层是否关闭
VITE_MODAL_CLOSE_ON_OVERLAY=true

# 模态框是否支持ESC键关闭
VITE_MODAL_ENABLE_ESCAPE=true

# 模态框动画持续时间（毫秒）
VITE_MODAL_ANIMATION_DURATION=300

# 是否显示加载动画
VITE_SHOW_LOADING_SPINNER=true

# 最小加载时间（毫秒，防止闪烁）
VITE_MIN_LOADING_TIME=500

# 加载超时时间（毫秒）
VITE_LOADING_TIMEOUT=30000

# ===========================================
# 文件上传配置
# ===========================================

# 分块上传的块大小（字节，默认5MB）
# 建议值: 1MB-10MB，过小影响性能，过大可能导致超时
VITE_CHUNK_SIZE=5242880

# 上传最大重试次数
VITE_UPLOAD_MAX_RETRIES=3

# 启用分块上传的文件大小阈值（字节，默认10MB）
# 超过此大小的文件将使用分块上传
VITE_CHUNKED_UPLOAD_THRESHOLD=10485760

# 上传重试延迟（毫秒）
VITE_UPLOAD_RETRY_DELAY=1000

# 并行上传数量
VITE_PARALLEL_UPLOADS=3

# 最大视频文件大小（字节，默认2GB）
VITE_MAX_VIDEO_SIZE=2147483648

# 最大图片文件大小（字节，默认50MB）
VITE_MAX_IMAGE_SIZE=52428800

# 支持的视频格式（逗号分隔）
VITE_ALLOWED_VIDEO_TYPES=mp4,avi,mov,wmv,mkv,flv

# 支持的图片格式（逗号分隔）
VITE_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp,bmp

# 最大文件名长度
VITE_MAX_FILE_NAME_LENGTH=255

# 进度更新间隔（毫秒）
VITE_PROGRESS_UPDATE_INTERVAL=100

# 是否显示上传百分比
VITE_SHOW_PROGRESS_PERCENTAGE=true

# 是否显示上传速度
VITE_SHOW_UPLOAD_SPEED=true

# 是否显示预计剩余时间
VITE_SHOW_UPLOAD_ETA=true

# 是否显示文件大小
VITE_SHOW_FILE_SIZE=true

# 是否启用视频预览
VITE_ENABLE_VIDEO_PREVIEW=true

# 是否启用图片预览
VITE_ENABLE_IMAGE_PREVIEW=true

# 最大预览文件大小（字节，默认100MB）
VITE_MAX_PREVIEW_SIZE=104857600

# 预览超时时间（毫秒）
VITE_PREVIEW_TIMEOUT=10000

# ===========================================
# JWT 认证配置
# ===========================================

# JWT存储键名
VITE_JWT_STORAGE_KEY=dev_jwt

# 刷新令牌存储键名
VITE_REFRESH_TOKEN_STORAGE_KEY=dev_refresh_token

# 用户类型存储键名
VITE_USER_TYPE_STORAGE_KEY=dev_user_type

# 用户信息存储键名
VITE_USER_INFO_STORAGE_KEY=dev_user_info

# JWT刷新提前时间（秒）
# 在token过期前多长时间开始刷新
VITE_JWT_REFRESH_BEFORE_EXPIRY=300

# 最大刷新尝试次数
VITE_MAX_REFRESH_ATTEMPTS=3

# 刷新重试延迟（毫秒）
VITE_REFRESH_RETRY_DELAY=1000

# 是否自动刷新JWT
VITE_AUTO_REFRESH_JWT=true

# 是否启用开发环境JWT
VITE_ENABLE_DEV_JWT=true

# 默认用户类型：admin, user, realtor, default
VITE_DEFAULT_USER_TYPE=default

# 开发环境JWT过期时间（秒）
VITE_DEV_JWT_EXPIRATION=86400

# Mock用户列表（逗号分隔）
VITE_MOCK_USERS=admin,user,realtor

# 是否启用CSRF保护
VITE_ENABLE_CSRF_PROTECTION=true

# CSRF令牌头名称
VITE_CSRF_TOKEN_NAME=X-CSRF-Token

# 是否要求HTTPS
VITE_REQUIRE_HTTPS=false

# ===========================================
# 业务功能配置
# ===========================================

# 搜索最小长度
VITE_MIN_SEARCH_LENGTH=2

# 搜索防抖延迟（毫秒）
VITE_SEARCH_DEBOUNCE_DELAY=300

# 最大搜索结果数量
VITE_MAX_SEARCH_RESULTS=10

# 是否启用搜索历史
VITE_ENABLE_SEARCH_HISTORY=true

# 最大搜索历史条目数
VITE_MAX_SEARCH_HISTORY_ITEMS=10

# 翻译API地址
VITE_TRANSLATION_API_URL=https://api.mymemory.translated.net/get

# 默认源语言
VITE_DEFAULT_SOURCE_LANG=zh-CN

# 默认目标语言
VITE_DEFAULT_TARGET_LANG=en-GB

# 翻译请求超时（毫秒）
VITE_TRANSLATION_TIMEOUT=5000

# 是否启用翻译功能
VITE_ENABLE_TRANSLATION=true

# 最大翻译文本长度
VITE_MAX_TRANSLATION_LENGTH=1000

# 视频是否自动播放
VITE_VIDEO_AUTOPLAY=false

# 视频是否显示控制条
VITE_VIDEO_CONTROLS=true

# 视频预加载策略: none, metadata, auto
VITE_VIDEO_PRELOAD=metadata

# 视频是否静音
VITE_VIDEO_MUTED=false

# 视频是否循环播放
VITE_VIDEO_LOOP=false

# 视频默认音量 (0.0-1.0)
VITE_VIDEO_DEFAULT_VOLUME=0.8

# 分类名称最大长度
VITE_MAX_CATEGORY_NAME_LENGTH=50

# 分类最大层级深度
VITE_MAX_CATEGORY_DEPTH=3

# 分类列表最大数量
VITE_MAX_CATEGORY_LIST_LIMIT=150

# 是否启用分类图标
VITE_ENABLE_CATEGORY_ICONS=true

# 默认分类颜色
VITE_DEFAULT_CATEGORY_COLOR=#007bff

# 客户名称最大长度
VITE_MAX_CLIENT_NAME_LENGTH=100

# 客户列表最大数量
VITE_MAX_CLIENT_LIST_LIMIT=999

# 是否启用客户头像
VITE_ENABLE_CLIENT_AVATARS=true

# 最大头像文件大小（字节，默认5MB）
VITE_MAX_AVATAR_SIZE=5242880

# 支持的头像格式（逗号分隔）
VITE_ALLOWED_AVATAR_TYPES=jpg,jpeg,png,gif

# 默认头像服务地址
VITE_DEFAULT_AVATAR_SERVICE=https://i.pravatar.cc

# 是否启用MLS集成
VITE_ENABLE_MLS_INTEGRATION=true

# 最大房源ID数量
VITE_MAX_PROPERTY_IDS=10

# MLS搜索超时（毫秒）
VITE_MLS_SEARCH_TIMEOUT=5000

# MLS缓存过期时间（毫秒）
VITE_MLS_CACHE_EXPIRATION=300000

# ===========================================
# 开发调试配置
# ===========================================

# 是否启用控制台日志
VITE_ENABLE_CONSOLE_LOG=true

# 是否启用性能日志
VITE_ENABLE_PERFORMANCE_LOG=false

# 日志级别：debug, info, warn, error
VITE_LOG_LEVEL=info

# 是否启用堆栈跟踪
VITE_ENABLE_STACK_TRACE=false

# 最大日志条目数
VITE_MAX_LOG_ENTRIES=1000

# 是否启用Mock数据
VITE_ENABLE_MOCK_DATA=false

# Mock延迟（毫秒）
VITE_MOCK_DELAY=500

# Mock错误率 (0.0-1.0)
VITE_MOCK_ERROR_RATE=0.1

# 是否启用Mock认证
VITE_ENABLE_MOCK_AUTH=false

# 是否启用热重载
VITE_ENABLE_HOT_RELOAD=true

# 重载延迟（毫秒）
VITE_RELOAD_DELAY=100

# 是否启用CSS热重载
VITE_ENABLE_CSS_HOT_RELOAD=true

# 是否启用开发工具
VITE_ENABLE_DEV_TOOLS=false

# 是否显示性能指标
VITE_SHOW_PERFORMANCE_METRICS=false

# 是否启用API模拟
VITE_ENABLE_API_MOCKING=false

# 是否显示调试信息
VITE_SHOW_DEBUG_INFO=false

# ===========================================
# 性能优化配置
# ===========================================

# 是否启用API缓存
VITE_ENABLE_API_CACHE=true

# API缓存过期时间（毫秒）
VITE_API_CACHE_EXPIRATION=300000

# 是否启用图片缓存
VITE_ENABLE_IMAGE_CACHE=true

# 最大缓存大小（字节，默认100MB）
VITE_MAX_CACHE_SIZE=104857600

# 是否启用懒加载
VITE_ENABLE_LAZY_LOADING=true

# 懒加载阈值（像素）
VITE_LAZY_LOADING_THRESHOLD=100

# 是否启用图片懒加载
VITE_ENABLE_IMAGE_LAZY_LOADING=true

# 是否启用视频懒加载
VITE_ENABLE_VIDEO_LAZY_LOADING=true

# 是否启用Gzip压缩
VITE_ENABLE_GZIP=true

# 是否启用代码压缩
VITE_ENABLE_MINIFICATION=true

# 是否启用Tree Shaking
VITE_ENABLE_TREE_SHAKING=true

# 代码块大小限制（字节，默认500KB）
VITE_CHUNK_SIZE_LIMIT=512000
