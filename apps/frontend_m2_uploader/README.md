# RealMaster 视频管理前端 (M2 Uploader)

这是一个用于管理视频后台的前端界面。它允许用户上传新视频、管理视频分类、查看客户信息和浏览系统仪表盘。该项目使用 Vite 构建，并采用 HTMX 和 Mustache.js 进行模板渲染。

## ✨ 主要特性

- 🎬 **视频管理**: 上传、编辑、删除和发布视频内容
- 📊 **仪表盘**: 实时统计数据和系统概览
- 🏷️ **分类管理**: 创建和管理视频分类体系
- 👥 **广告主管理**: 管理广告主信息和关联视频
- 🏠 **房源集成**: 搜索和关联房地产信息
- 🔍 **智能搜索**: 支持多条件筛选和搜索
- 📱 **响应式设计**: 适配桌面和移动设备
- 🎨 **现代UI**: 基于SCSS的模块化样式系统

---

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 9+

### 安装与运行

1.  **进入项目目录**
    ```bash
    git clone <your-repository-url>
    cd apps/frontend_m2_uploader
    ```

2.  **安装依赖**
    ```bash
    npm install
    ```

3.  **配置服务**
    - 推荐在仓库根目录配置 `.env`：
    ```env
    API_BASE_URL=http://192.168.x.x:8080
    MEDIA_BASE_URL=http://192.168.x.x:3000
    ```
    - 也可在本项目目录使用 `.env` 的 `VITE_API_BASE_URL`/`VITE_MEDIA_BASE_URL` 覆盖。

4.  **运行开发服务器**
    ```bash
    npm run dev
    ```
    服务启动后，在浏览器中打开 Vite 提示的地址 (默认 `http://localhost:5175`)。

---

## 🛠️ 技术栈

- **构建工具**: Vite 6.x
- **前端框架**: 原生JavaScript + HTMX
- **模板引擎**: Mustache.js
- **样式**: SCSS/Sass
- **HTTP客户端**: Axios
- **日期选择器**: Flatpickr
- **代码质量**: ESLint + Prettier

---

## ⚙️ 配置说明

项目的主要配置位于 `src/js/config.js`，默认会优先读取仓库根 `.env` 中的 `API_BASE_URL`/`MEDIA_BASE_URL`，其次读取 `VITE_API_BASE_URL`/`VITE_MEDIA_BASE_URL`，未配置时开发环境回退到 `http://localhost:8080` 与 `http://localhost:3000`。

| 配置项         | 类型   | 默认值                  | 描述                                       |
|----------------|--------|-------------------------|--------------------------------------------|
| `API_BASE_URL` | string | 见上 | 后端 API 服务的基础地址（推荐在仓库根 `.env` 设置）。 |
| `MEDIA_BASE_URL` | string | 见上 | 媒体资源服务基础地址。 |

你也可以通过 `.env` 文件覆盖默认配置，例如（优先级较低）：
```env
VITE_API_BASE_URL=http://your-backend-api.com
VITE_MEDIA_BASE_URL=http://your-media.com
```

---

## 📁 项目结构

```text
.
├── public/               # 静态资源和 HTML 模板
│   └── templates/        # Mustache 模板文件
├── src/                  # 项目源代码
│   ├── assets/           # 静态资源
│   │   └── icons/        # 图标文件
│   ├── js/               # JavaScript 源码
│   │   ├── components/   # 可复用组件
│   │   ├── pages/        # 各页面的业务逻辑
│   │   ├── services/     # API 请求服务
│   │   ├── utils/        # 工具函数
│   │   ├── config.js     # 应用配置
│   │   ├── main.js       # 应用主入口
│   │   ├── router.js     # 前端路由
│   │   └── viewRenderer.js # 视图渲染器
│   └── styles/           # SCSS 样式文件
│       ├── base/         # 基础样式
│       ├── components/   # 组件样式
│       ├── pages/        # 页面样式
│       └── main.scss     # 主样式入口
├── index.html            # 主 HTML 入口文件
├── vite.config.js        # Vite 配置文件
└── package.json          # 项目依赖和脚本
```

---

## 📄 License

本项目采用 ISC 许可证。

---

## 🙋 常见问题

**Q: 为什么页面数据显示不出来?**  
A: 请确保后端的 `realmaster-video-backend` 服务正在运行，并且前端配置中的 `API_BASE_URL` (`src/js/config.js`) 指向了正确的后端地址和端口。同时，打开浏览器的开发者工具，检查网络请求是否出错。

**Q: 如何构建生产环境版本?**
A: 运行 `npm run build` 命令。Vite 会将构建好的文件输出到 `dist` 目录中。你可以通过 `npm run preview` 来本地预览生产版本的效果。

**Q: 如何修改API服务器地址?**
A: 编辑 `src/js/config.js` 文件中的 `API_BASE_URL` 常量，或者创建 `.env` 文件设置 `VITE_API_BASE_URL` 环境变量。

**Q: 页面样式如何组织?**
A: 项目使用SCSS模块化样式，主要分为base（基础样式）、components（组件样式）和pages（页面样式）三个层级。

## 🚀 开发指南

### 添加新页面
1. 在 `src/js/pages/` 目录下创建页面逻辑文件
2. 在 `public/templates/` 目录下创建对应的Mustache模板
3. 在 `src/js/router.js` 中添加路由配置
4. 在 `src/styles/pages/` 目录下添加页面样式

### API集成
所有API调用都通过 `src/js/services/` 目录下的服务模块进行，确保统一的错误处理和响应格式。

### 代码规范
- 使用ESLint进行代码检查：`npm run lint`
- 使用Prettier进行代码格式化：`npm run format`