<div class="clients-page">
    <header class="page-header">
        <button class="back-btn" data-route="/dashboard">
            <img src="/icons/back/back.svg" alt="Back">
        </button>
        <h1 class="header-title">Clients</h1>
    </header>

    <main class="main-content">
        <div class="content-header">
            <h2 class="content-title">Clients List</h2>
            <button class="btn-primary new-client-btn">
                <img src="/icons/create/plus.svg" alt="New Client">
                <span>New Client</span>
            </button>
        </div>

        <div class="client-list-container">
            <table class="client-table">
                <thead>
                    <tr>
                        <th>Profile Picture</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Memo</th>
                        <th class="actions-header"></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Client rows will be dynamically rendered here -->
                </tbody>
            </table>
            <div class="loading-indicator">Loading clients...</div>
        </div>
    </main>

    <footer class="page-footer">
        <!-- Pagination controls will be dynamically rendered here -->
    </footer>

    <!-- Add/Edit Client Modal -->
    <div id="client-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <h3 class="modal-title" id="client-modal-title">Add Client</h3>
            <form id="client-modal-form">
                <input type="hidden" id="client-id">

                <div class="form-group">
                    <label>Profile Picture<span class="required">*</span></label>
                    <div class="image-uploader" id="avatar-uploader">
                        <input type="file" id="avatar-upload-input" accept="image/*" style="display: none;">
                        <div class="upload-area">
                            <img id="avatar-preview" src="" alt="Avatar Preview" class="hidden">
                            <div class="upload-placeholder">
                                <img src="/icons/create/plus.svg" alt="Upload">
                                <span>Upload</span>
                            </div>
                            <div class="preview-overlay"><span>Preview</span></div>
                        </div>
                        <button type="button" id="remove-avatar-btn" class="hidden">
                            <img src="/icons/delete/delete.svg" alt="Remove">
                        </button>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="client-name">Name<span class="required">*</span></label>
                        <input type="text" id="client-name" placeholder="Enter name" required>
                    </div>
                    <div class="form-group">
                        <label for="client-email">Email<span class="required">*</span></label>
                        <input type="email" id="client-email" placeholder="Enter email" required>
                    </div>
                    <div class="form-group">
                        <label for="client-phone">Phone<span class="required">*</span></label>
                        <div class="phone-input-container">
                            <div class="country-selector" id="country-selector">
                                <div class="selected-country" id="selected-country">
                                    <img src="/icons/flag/Canada.svg" alt="Canada" class="flag-icon">
                                    <span class="country-code">+1</span>
                                    <svg class="dropdown-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="country-dropdown" id="country-dropdown">
                                    <div class="country-option" data-country="US" data-code="+1">
                                        <img src="/icons/flag/USA.svg" alt="USA" class="flag-icon">
                                        <span class="country-code">+1</span>
                                    </div>
                                    <div class="country-option" data-country="CA" data-code="+1">
                                        <img src="/icons/flag/Canada.svg" alt="Canada" class="flag-icon">
                                        <span class="country-code">+1</span>
                                    </div>
                                </div>
                            </div>
                            <input type="tel" id="client-phone" placeholder="(*************" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="client-rm-id">RealMaster User ID</label>
                        <input type="text" id="client-rm-id" placeholder="Enter ID">
                    </div>
                </div>
                
                <div class="form-group full-width-form-group">
                    <label for="client-memo">Memo</label>
                    <textarea id="client-memo" placeholder="Enter Memo"></textarea>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn-secondary" id="cancel-client-btn">Cancel</button>
                    <button type="button" class="btn-primary" id="save-client-btn">Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Fullscreen Image Preview Modal -->
    <div id="image-preview-modal" class="modal-overlay hidden">
        <span class="close-btn" id="close-preview-btn">&times;</span>
        <img class="modal-content" id="fullscreen-image">
    </div>

    <!-- Merge Client Modal -->
    <div id="merge-client-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <h3 class="modal-title">Merge and Delete Client</h3>
            <p>Please select a client to merge all videos from <strong id="source-client-name"></strong> into, before deleting.</p>
            
            <div class="form-group">
                <label for="target-client-dropdown">Merge into:</label>
                <div id="target-client-dropdown" class="custom-dropdown">
                    <div class="dropdown-selected">
                        <span class="selected-value">Select a client...</span>
                        <div class="dropdown-arrow"></div>
                    </div>
                    <div class="dropdown-options">
                        <ul class="options-list">
                            <!-- Options will be populated dynamically -->
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn-secondary" id="cancel-merge-btn">Cancel</button>
                <button type="button" class="btn-danger" id="confirm-merge-btn">Confirm Merge & Delete</button>
            </div>
        </div>
    </div>
</div> 