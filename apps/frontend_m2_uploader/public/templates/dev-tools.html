<div class="dev-tools-page">
    <header class="dev-tools-header">
        <h1 class="header-title">🔧 开发工具 - JWT 测试</h1>
        <p class="text-muted">用于测试和调试 JWT 认证功能</p>
    </header>

    <div class="row">
        <!-- JWT 状态卡片 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🔐 JWT 状态</h5>
                </div>
                <div class="card-body">
                    <div id="jwt-status">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">检查中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-primary btn-sm me-2" onclick="window.devTools.refreshJWT()">
                            🔄 刷新 JWT
                        </button>
                        <button class="btn btn-warning btn-sm me-2" onclick="window.devTools.clearJWT()">
                            🗑️ 清除 JWT
                        </button>
                        <button class="btn btn-info btn-sm" onclick="window.devTools.checkStatus()">
                            ✅ 检查状态
                        </button>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">切换用户类型：</label>
                        <select class="form-select form-select-sm" id="user-type-select" onchange="window.devTools.switchUser()">
                            <option value="default">默认用户 (admin权限)</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="realtor">房产经纪人</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- API 测试卡片 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 API 测试</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">测试 API 端点：</label>
                        <select class="form-select" id="api-endpoint">
                            <option value="/video/admin/videos">获取视频列表</option>
                            <option value="/video/admin/videos/stats">获取统计数据</option>
                            <option value="/video/admin/categories">获取分类列表</option>
                            <option value="/video/admin/advertisers">获取客户列表</option>
                        </select>
                    </div>
                    <button class="btn btn-success btn-sm me-2" onclick="window.devTools.testAPI()">
                        🚀 测试 API
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="window.devTools.clearResults()">
                        🧹 清除结果
                    </button>
                    <div id="api-results" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JWT 详情 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>📋 JWT 详细信息</h5>
                </div>
                <div class="card-body">
                    <div id="jwt-details">
                        <p class="text-muted">JWT 详情将在这里显示...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志输出 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>📝 操作日志</h5>
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.devTools.clearLogs()">
                        清除日志
                    </button>
                </div>
                <div class="card-body">
                    <div id="dev-logs" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                        <div class="text-success">[加载中...] 开发工具已加载</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .dev-tools-page {
        padding: 20px;
    }
    .dev-tools-header {
        margin-bottom: 30px;
        text-align: center;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
        margin-bottom: 20px;
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    .status-valid {
        background-color: #d4edda;
        color: #155724;
    }
    .status-invalid {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-expired {
        background-color: #fff3cd;
        color: #856404;
    }
    #dev-logs {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    .log-success { color: #28a745; }
    .log-error { color: #dc3545; }
    .log-warning { color: #ffc107; }
    .log-info { color: #17a2b8; }
</style>
