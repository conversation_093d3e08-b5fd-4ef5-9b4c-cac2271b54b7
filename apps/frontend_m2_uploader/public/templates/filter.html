<div class="filter-page">
    <header class="page-header">
        <button class="back-btn" data-route="/dashboard">
            <img src="/icons/back/back.svg" alt="Back">
        </button>
        <h1 class="header-title">Filter</h1>
        <div class="filter-count-badge hidden"></div>
    </header>

    <main class="filter-options-container">
        <!-- Category Section -->
        <div class="filter-group" id="category-filter-group">
            <label class="filter-label">Category</label>
            <div class="filter-select-box">
                <div class="selected-value-display">None</div>
                <img src="/icons/arrow/arrow.svg" alt="toggle" class="arrow-icon">
            </div>
            <div class="filter-group-body">
                <ul class="options-list" id="category-options-list">
                    <!-- Dynamic content -->
                    <div class="loading-indicator">Loading...</div>
                </ul>
            </div>
        </div>

        <!-- Client Section -->
        <div class="filter-group" id="client-filter-group">
            <div class="filter-label-container">
                <label class="filter-label">Client</label>
            </div>
            <div class="filter-select-box">
                <div class="selected-value-display">None</div>
                <img src="/icons/arrow/arrow.svg" alt="toggle" class="arrow-icon">
            </div>
            <div class="filter-group-body">
                <ul class="options-list" id="client-options-list">
                    <!-- Dynamic content -->
                    <div class="loading-indicator">Loading...</div>
                </ul>
            </div>
        </div>
        
        <!-- Status Section -->
        <div class="filter-group" id="status-filter-group">
            <label class="filter-label">Video Status</label>
            <div class="filter-select-box">
                <div class="selected-value-display">All</div>
                <img src="/icons/arrow/arrow.svg" alt="toggle" class="arrow-icon">
            </div>
            <div class="filter-group-body">
                <ul class="options-list" id="status-options-list">
                    <li><label><input type="checkbox" name="status" value=""> All</label></li>
                    <li><label><input type="checkbox" name="status" value="Draft"> Draft</label></li>
                    <li><label><input type="checkbox" name="status" value="Unpublished"> Unpublished</label></li>
                    <li><label><input type="checkbox" name="status" value="Processing"> Processing</label></li>
                    <li><label><input type="checkbox" name="status" value="Published"> Published</label></li>
                    <li><label><input type="checkbox" name="status" value="Failed"> Failed</label></li>
                </ul>
            </div>
        </div>

        <!-- Date Range Section -->
        <div class="filter-group" id="date-range-filter-group">
            <label class="filter-label">Date Range</label>
            <div class="date-range-inputs">
                <div class="date-input-container">
                    <label class="date-input-label">Start Time</label>
                    <div class="date-input-wrapper" id="start-date-wrapper">
                        <input type="text" id="start-date-input" placeholder="Select date" data-input>
                        <img src="/icons/arrow/arrow.svg" alt="date picker" class="arrow-icon" data-toggle>
                    </div>
                </div>
                <div class="date-input-container">
                    <label class="date-input-label">End Time</label>
                    <div class="date-input-wrapper" id="end-date-wrapper">
                        <input type="text" id="end-date-input" placeholder="Select date" data-input>
                        <img src="/icons/arrow/arrow.svg" alt="date picker" class="arrow-icon" data-toggle>
                    </div>
                </div>
            </div>
        </div>

    </main>
    
    <footer class="filter-footer">
        <button id="clear-filters-btn" class="btn-secondary">Clear</button>
        <button id="apply-filters-btn" class="btn-primary">Filter</button>
    </footer>
</div> 