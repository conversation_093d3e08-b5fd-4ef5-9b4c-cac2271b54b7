import { createClient, updateClient } from '../services/apiClient';
import { invalidateDropdownCache } from './customDropdown';
import ErrorHandler, { ErrorTypes, ErrorSeverity, showSuccess } from '../utils/errorHandler';
import { initPhoneInput } from './phoneInput';

/**
 * A reusable module for the client creation/editing modal.
 */

let selectedAvatarFile = null;
let onSuccessCallback = null;
let phoneInputInstance = null;

// Cache DOM elements to avoid repeated queries
let modal, form, saveBtn, cancelBtn, uploader, uploaderInput, preview, uploadArea, removeBtn, previewModal, fullscreenImage, closePreviewBtn;

/**
 * Initializes the modal's DOM elements and attaches event listeners.
 * This should be called once when the page containing the modal is loaded.
 */
export const initClientModal = () => {
    try {
        modal = document.getElementById('client-modal');
        if (!modal) {
            console.warn('Client modal not found on this page');
            return; // Don't proceed if the modal isn't on the current page
        }

        form = document.getElementById('client-modal-form');
        saveBtn = document.getElementById('save-client-btn');
        cancelBtn = document.getElementById('cancel-client-btn');
        uploader = document.getElementById('avatar-uploader');
        uploaderInput = document.getElementById('avatar-upload-input');
        preview = document.getElementById('avatar-preview');
        removeBtn = document.getElementById('remove-avatar-btn');
        previewModal = document.getElementById('image-preview-modal');
        fullscreenImage = document.getElementById('fullscreen-image');
        closePreviewBtn = document.getElementById('close-preview-btn');

        // 检查关键元素是否存在
        if (!form || !saveBtn || !cancelBtn) {
            console.error('Critical client modal elements not found');
            return;
        }

        // 安全地查询 uploadArea
        uploadArea = uploader?.querySelector('.upload-area');

        // Attach all event listeners - 只有在元素存在时才绑定
        if (cancelBtn) {
            cancelBtn.addEventListener('click', closeModal);
        }

        if (modal) {
            modal.addEventListener('click', (e) => (e.target === modal) && closeModal());
        }

        if (uploader && uploaderInput) {
            uploader.addEventListener('click', (e) => {
                if (e.target.closest('#remove-avatar-btn')) return;
                uploaderInput.click();
            });
        }

        if (removeBtn) {
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                resetUploader();
            });
        }

        if (uploaderInput) {
            uploaderInput.addEventListener('change', handleFileSelect);
        }

        if (preview) {
            preview.addEventListener('click', handlePreviewClick);
        }

        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', closePreviewModal);
        }

        if (previewModal) {
            previewModal.addEventListener('click', (e) => (e.target === previewModal) && closePreviewModal());
        }

        if (form) {
            form.addEventListener('input', checkFormValidity);
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', handleSave);
        }

        // 初始化电话号码输入组件
        phoneInputInstance = initPhoneInput('country-selector');
        if (!phoneInputInstance) {
            console.warn('Failed to initialize phone input component');
        }
    } catch (error) {
        console.error('Error initializing client modal:', error);
    }
};

/**
 * Opens the client modal for creating or editing.
 * @param {object | null} clientData - The client data for editing, or null for creating.
 * @param {function} onSuccess - The callback function to execute after a successful save.
 */
export const openClientModal = (clientData, onSuccess) => {
    onSuccessCallback = onSuccess;
    form.reset();
    resetUploader();
    
    if (clientData) {
        // Edit mode
        document.getElementById('client-modal-title').textContent = 'Edit Client';
        document.getElementById('client-id').value = clientData.id;
        document.getElementById('client-name').value = clientData.name;
        document.getElementById('client-email').value = clientData.email;
        document.getElementById('client-memo').value = clientData.memo;

        // 设置电话号码（处理国际格式）
        if (phoneInputInstance && clientData.phone) {
            phoneInputInstance.setPhoneNumber(clientData.phone);
        } else {
            document.getElementById('client-phone').value = clientData.phone || '';
        }

        if (clientData.avatarUrl && !clientData.avatarUrl.endsWith('head.svg')) {
            preview.src = clientData.avatarUrl;
            preview.classList.remove('hidden');
            uploadArea.classList.add('has-image');
            removeBtn.classList.remove('hidden');
        }
    } else {
        // Create mode
        document.getElementById('client-modal-title').textContent = 'New Client';
        document.getElementById('client-id').value = '';
    }
    
    checkFormValidity();
    modal.classList.remove('hidden');
};

function closeModal() {
    modal.classList.add('hidden');
    selectedAvatarFile = null;
    onSuccessCallback = null;

    // 清除验证错误状态
    clearValidationErrors();

    // 重置按钮状态
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.style.opacity = '1';
        saveBtn.title = '';
    }

    // 重置表单
    if (form) {
        form.reset();
    }
}

function resetUploader() {
    preview.src = '';
    preview.classList.add('hidden');
    uploadArea.classList.remove('has-image');
    uploaderInput.value = '';
    selectedAvatarFile = null;
    removeBtn.classList.add('hidden');
    checkFormValidity();
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
            preview.src = e.target.result;
            preview.classList.remove('hidden');
            uploadArea.classList.add('has-image');
            removeBtn.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
        selectedAvatarFile = file;
    } else {
        resetUploader();
    }
    checkFormValidity();
}

function handlePreviewClick(event) {
    event.stopPropagation();
    if (preview.src && !preview.classList.contains('hidden')) {
        fullscreenImage.src = preview.src;
        previewModal.classList.remove('hidden');
    }
}

function closePreviewModal() {
    previewModal.classList.add('hidden');
}

function checkFormValidity() {
    try {
        // 安全的 DOM 查询，添加 null 检查
        const clientIdElement = form?.querySelector('#client-id');
        const clientNameElement = form?.querySelector('#client-name');
        const clientEmailElement = form?.querySelector('#client-email');
        const clientPhoneElement = form?.querySelector('#client-phone');

        // 如果关键元素不存在，直接返回
        if (!clientNameElement || !clientEmailElement || !clientPhoneElement || !saveBtn) {
            console.warn('Client modal form elements not found');
            return;
        }

        const isEditMode = !!(clientIdElement?.value);
        const isNameValid = clientNameElement.value.trim() !== '';
        const isEmailValid = clientEmailElement.value.trim() !== '';

        // 实时验证并显示错误样式
        // 姓名验证
        if (clientNameElement.value.trim()) {
            clientNameElement.style.borderColor = '';
            clientNameElement.classList.remove('error');
        } else if (clientNameElement.value.length > 0) {
            clientNameElement.style.borderColor = '#dc3545';
            clientNameElement.classList.add('error');
        }

        // 邮箱验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const emailValue = clientEmailElement.value.trim();
        if (emailValue && emailRegex.test(emailValue)) {
            clientEmailElement.style.borderColor = '';
            clientEmailElement.classList.remove('error');
        } else if (emailValue.length > 0) {
            clientEmailElement.style.borderColor = '#dc3545';
            clientEmailElement.classList.add('error');
        }

        // 使用电话号码组件验证
        let isPhoneValid = false;
        if (phoneInputInstance) {
            const phoneValidation = phoneInputInstance.validatePhoneNumber(clientPhoneElement.value);
            isPhoneValid = phoneValidation.isValid;

            // 显示电话号码验证错误
            const phoneContainer = clientPhoneElement.closest('.phone-input-container');
            if (phoneContainer) {
                if (clientPhoneElement.value.trim() && !isPhoneValid) {
                    phoneContainer.style.borderColor = '#dc3545';
                    phoneContainer.classList.add('error');
                    phoneContainer.title = phoneValidation.message;
                } else {
                    phoneContainer.style.borderColor = '';
                    phoneContainer.classList.remove('error');
                    phoneContainer.title = '';
                }
            }
        } else {
            // 降级处理：基本的非空验证
            isPhoneValid = clientPhoneElement.value.trim() !== '';
        }

        // 不再禁用按钮，让用户始终可以点击
        // 验证将在 handleSave 中进行，并显示相应的错误提示

        // 可选：改变按钮样式来提示用户表单状态
        if (isEditMode) {
            const allValid = isNameValid && isEmailValid && isPhoneValid;
            saveBtn.style.opacity = allValid ? '1' : '0.7';
            saveBtn.title = allValid ? '' : 'Please fill in all required fields';
        } else {
            const isAvatarSelected = !!selectedAvatarFile;
            const allValid = isNameValid && isEmailValid && isPhoneValid && isAvatarSelected;
            saveBtn.style.opacity = allValid ? '1' : '0.7';
            saveBtn.title = allValid ? '' : 'Please fill in all required fields and upload a profile picture';
        }
    } catch (error) {
        console.error('Error in checkFormValidity:', error);
        // 出错时显示提示，但不禁用按钮
        if (saveBtn) {
            saveBtn.style.opacity = '0.7';
            saveBtn.title = 'Form validation error occurred';
        }
    }
}

/**
 * 验证表单数据
 * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
 */
function validateForm() {
    const errors = [];

    try {
        // 获取表单元素
        const clientIdElement = document.getElementById('client-id');
        const clientNameElement = document.getElementById('client-name');
        const clientEmailElement = document.getElementById('client-email');
        const clientPhoneElement = document.getElementById('client-phone');

        const isEditMode = !!(clientIdElement?.value);

        // 验证姓名
        if (!clientNameElement || !clientNameElement.value.trim()) {
            errors.push({
                field: 'name',
                element: clientNameElement,
                message: 'Name is required'
            });
        }

        // 验证邮箱
        if (!clientEmailElement || !clientEmailElement.value.trim()) {
            errors.push({
                field: 'email',
                element: clientEmailElement,
                message: 'Email is required'
            });
        } else {
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(clientEmailElement.value.trim())) {
                errors.push({
                    field: 'email',
                    element: clientEmailElement,
                    message: 'Please enter a valid email address'
                });
            }
        }

        // 验证电话号码
        if (!clientPhoneElement || !clientPhoneElement.value.trim()) {
            errors.push({
                field: 'phone',
                element: clientPhoneElement,
                message: 'Phone number is required'
            });
        } else if (phoneInputInstance) {
            const phoneValidation = phoneInputInstance.validatePhoneNumber(clientPhoneElement.value);
            if (!phoneValidation.isValid) {
                errors.push({
                    field: 'phone',
                    element: clientPhoneElement,
                    message: phoneValidation.message
                });
            }
        }

        // 验证头像（仅在创建模式下）
        if (!isEditMode && !selectedAvatarFile) {
            errors.push({
                field: 'avatar',
                element: document.getElementById('avatar-uploader'),
                message: 'Profile picture is required'
            });
        }

    } catch (error) {
        console.error('Error in form validation:', error);
        errors.push({
            field: 'general',
            element: null,
            message: 'Form validation error occurred'
        });
    }

    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

/**
 * 显示验证错误
 * @param {Array} errors 错误数组
 */
function showValidationErrors(errors) {
    // 清除之前的错误样式
    clearValidationErrors();

    // 收集错误消息
    const errorMessages = [];

    errors.forEach(error => {
        // 添加错误样式到对应字段
        if (error.element) {
            addErrorStyle(error.element, error.field);
        }

        // 收集错误消息
        errorMessages.push(error.message);
    });

    // 显示错误消息
    if (errorMessages.length > 0) {
        const message = errorMessages.length === 1
            ? errorMessages[0]
            : `Please fix the following issues:\n• ${errorMessages.join('\n• ')}`;

        // 尝试多种方式显示错误消息
        // 方法1: 使用 ErrorHandler
        try {
            ErrorHandler.showUserMessage(message, 'error', 5000);
        } catch (errorHandlerError) {
            // 方法2: 尝试创建简单的错误提示
            try {
                showSimpleErrorMessage(message);
            } catch (simpleError) {
                // 方法3: 降级处理：使用 alert
                alert(message);
            }
        }
    }
}

/**
 * 显示简单的错误消息（备选方案）
 * @param {string} message 错误消息
 */
function showSimpleErrorMessage(message) {
    // 移除现有的错误消息
    const existingError = document.querySelector('.client-modal-error-message');
    if (existingError) {
        existingError.remove();
    }

    // 创建错误消息元素
    const errorDiv = document.createElement('div');
    errorDiv.className = 'client-modal-error-message';
    errorDiv.style.cssText = `
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        line-height: 1.4;
    `;
    errorDiv.textContent = message;

    // 插入到表单顶部
    const modalContent = modal?.querySelector('.modal-content');
    const firstFormGroup = modalContent?.querySelector('.form-group');

    if (modalContent && firstFormGroup) {
        modalContent.insertBefore(errorDiv, firstFormGroup);
    } else {
        // 降级：插入到模态框开头
        if (modalContent) {
            modalContent.insertBefore(errorDiv, modalContent.firstChild);
        }
    }

    // 5秒后自动移除
    setTimeout(() => {
        if (errorDiv && errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

/**
 * 清除验证错误样式
 */
function clearValidationErrors() {
    // 清除输入框错误样式
    const inputs = form?.querySelectorAll('input, textarea');
    inputs?.forEach(input => {
        input.style.borderColor = '';
        input.classList.remove('error');
    });

    // 清除电话号码容器错误样式
    const phoneContainer = document.querySelector('.phone-input-container');
    if (phoneContainer) {
        phoneContainer.style.borderColor = '';
        phoneContainer.classList.remove('error');
    }

    // 清除头像上传器错误样式
    const avatarUploader = document.getElementById('avatar-uploader');
    if (avatarUploader) {
        avatarUploader.style.borderColor = '';
        avatarUploader.classList.remove('error');
    }

    // 清除简单错误消息
    const existingError = document.querySelector('.client-modal-error-message');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * 添加错误样式到字段
 * @param {HTMLElement} element 表单元素
 * @param {string} fieldType 字段类型
 */
function addErrorStyle(element, fieldType) {
    if (!element) return;

    switch (fieldType) {
        case 'phone':
            // 电话号码字段特殊处理
            const phoneContainer = element.closest('.phone-input-container');
            if (phoneContainer) {
                phoneContainer.style.borderColor = '#dc3545';
                phoneContainer.classList.add('error');
            } else {
                element.style.borderColor = '#dc3545';
                element.classList.add('error');
            }
            break;

        case 'avatar':
            // 头像上传器特殊处理
            element.style.borderColor = '#dc3545';
            element.classList.add('error');
            break;

        default:
            // 普通输入框
            element.style.borderColor = '#dc3545';
            element.classList.add('error');
            break;
    }
}

async function handleSave() {
    try {
        // 先进行完整的表单验证
        const validationResult = validateForm();

        if (!validationResult.isValid) {
            // 显示验证错误消息
            showValidationErrors(validationResult.errors);
            // 重置按钮样式，确保用户知道可以重试
            saveBtn.style.opacity = '1';
            saveBtn.title = '';
            return; // 不禁用按钮，让用户可以修正后重试
        }

        // 验证通过，开始提交过程，此时禁用按钮防止重复提交
        saveBtn.disabled = true;
        saveBtn.style.opacity = '0.6';
        saveBtn.title = 'Saving...';

        // 安全的 DOM 查询
        const clientIdElement = document.getElementById('client-id');
        const clientNameElement = document.getElementById('client-name');
        const clientEmailElement = document.getElementById('client-email');
        const clientPhoneElement = document.getElementById('client-phone');
        const clientMemoElement = document.getElementById('client-memo');
        const clientRmIdElement = document.getElementById('client-rm-id');

        // 检查必需的元素是否存在
        if (!clientNameElement || !clientEmailElement || !clientPhoneElement) {
            throw new Error('Required form elements not found');
        }

        const clientId = clientIdElement?.value || '';
        const formData = new FormData();

        formData.append('nm', clientNameElement.value.trim());
        formData.append('em', clientEmailElement.value.trim());

        // 使用完整的电话号码格式（包含国家代码）
        let phoneNumber = clientPhoneElement.value.trim();
        if (phoneInputInstance) {
            const fullPhoneNumber = phoneInputInstance.getFullPhoneNumber();
            if (fullPhoneNumber) {
                phoneNumber = fullPhoneNumber;
            }
        }
        formData.append('ph', phoneNumber);

        formData.append('rem', clientMemoElement?.value.trim() || '');
        formData.append('mUid', clientRmIdElement?.value.trim() || '');

        if (selectedAvatarFile) {
            formData.append('avatar', selectedAvatarFile);
        }

        let result;
        if (clientId) {
            result = await updateClient(clientId, formData);
        } else {
            result = await createClient(formData);
        }

        if (onSuccessCallback) {
            try {
                // 安全地传递客户数据，即使 result.data 不存在也不会崩溃
                const clientData = result?.data || result || {};
                onSuccessCallback(clientData);
            } catch (callbackError) {
                console.error('Error in success callback:', callbackError);
                // 回调错误不应该阻止其他操作
            }
        }

        // 触发客户缓存失效事件
        invalidateDropdownCache('clients-changed');

        // 显示成功消息
        showSuccess(clientId ? '客户信息更新成功' : '客户创建成功');

        closeModal();
    } catch (error) {
        console.error('Error in handleSave:', error);

        // 重新启用保存按钮并恢复样式
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.style.opacity = '1';
            saveBtn.title = '';
        }

        // 显示用户友好的错误消息
        let errorMessage = 'Failed to save client information. Please try again.';
        if (error.message && error.message.includes('Required form elements')) {
            errorMessage = 'Form validation failed. Please check all required fields.';
        } else if (error.message) {
            errorMessage = error.message;
        }

        // 使用错误处理器显示错误
        try {
            ErrorHandler.handleError(error, ErrorTypes.BUSINESS, ErrorSeverity.MEDIUM, {
                context: clientId ? 'Update client failed' : 'Create client failed',
                clientId: clientId
            });
        } catch (handlerError) {
            // 降级处理：直接显示错误消息
            alert(errorMessage);
        }

        // 重新检查表单有效性
        checkFormValidity();
    }
}