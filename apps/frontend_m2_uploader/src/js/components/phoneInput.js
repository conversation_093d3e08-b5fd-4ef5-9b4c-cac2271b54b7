/**
 * 北美电话号码输入组件
 * 支持美国和加拿大的电话号码格式化和验证
 */

// 北美国家配置
const NORTH_AMERICAN_COUNTRIES = {
    US: {
        name: 'United States',
        code: '+1',
        flag: '/icons/flag/USA.svg',
        format: '(###) ###-####',
        placeholder: '(*************'
    },
    CA: {
        name: 'Canada',
        code: '+1',
        flag: '/icons/flag/Canada.svg',
        format: '(###) ###-####',
        placeholder: '(*************'
    }
};

/**
 * 初始化电话号码输入组件
 * @param {string} containerId - 包含电话输入组件的容器ID
 */
export const initPhoneInput = (containerId = 'country-selector') => {
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn('Phone input container not found:', containerId);
        return null;
    }

    const selectedCountry = container.querySelector('#selected-country');
    const dropdown = container.querySelector('#country-dropdown');
    const phoneInput = document.querySelector('#client-phone');
    const countryOptions = container.querySelectorAll('.country-option');

    if (!selectedCountry || !dropdown || !phoneInput) {
        console.warn('Phone input elements not found');
        return null;
    }

    let currentCountry = 'CA'; // 默认选择加拿大

    // 初始化时设置默认占位符
    const initializeDefaults = () => {
        const country = NORTH_AMERICAN_COUNTRIES[currentCountry];
        if (country && phoneInput) {
            phoneInput.placeholder = country.placeholder;
        }
    };

    // 切换下拉框显示/隐藏
    const toggleDropdown = () => {
        const isOpen = dropdown.classList.contains('open');
        if (isOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    };

    // 打开下拉框
    const openDropdown = () => {
        dropdown.classList.add('open');
        selectedCountry.classList.add('open');
        container.classList.add('dropdown-open');

        // 添加全局点击监听器来关闭下拉框
        setTimeout(() => {
            document.addEventListener('click', handleOutsideClick);
        }, 0);
    };

    // 关闭下拉框
    const closeDropdown = () => {
        dropdown.classList.remove('open');
        selectedCountry.classList.remove('open');
        container.classList.remove('dropdown-open');
        document.removeEventListener('click', handleOutsideClick);
    };

    // 处理外部点击
    const handleOutsideClick = (event) => {
        if (!container.contains(event.target)) {
            closeDropdown();
        }
    };

    // 选择国家
    const selectCountry = (countryCode) => {
        const country = NORTH_AMERICAN_COUNTRIES[countryCode];
        if (!country) return;

        currentCountry = countryCode;

        // 更新选中的国家显示
        const flagImg = selectedCountry.querySelector('.flag-icon');
        const codeSpan = selectedCountry.querySelector('.country-code');

        if (flagImg) {
            flagImg.src = country.flag;
            flagImg.alt = country.name;
        }
        if (codeSpan) {
            codeSpan.textContent = country.code;
        }

        // 更新输入框占位符
        phoneInput.placeholder = country.placeholder;

        // 重新格式化当前输入的号码
        formatPhoneNumber();

        closeDropdown();
    };

    // 格式化电话号码
    const formatPhoneNumber = () => {
        const value = phoneInput.value.replace(/\D/g, ''); // 只保留数字
        const country = NORTH_AMERICAN_COUNTRIES[currentCountry];
        
        if (value.length === 0) {
            phoneInput.value = '';
            return;
        }

        // 北美号码格式: (###) ###-####
        let formatted = '';
        if (value.length >= 1) {
            formatted = '(' + value.substring(0, 3);
        }
        if (value.length >= 4) {
            formatted += ') ' + value.substring(3, 6);
        }
        if (value.length >= 7) {
            formatted += '-' + value.substring(6, 10);
        }

        phoneInput.value = formatted;
    };

    // 验证电话号码
    const validatePhoneNumber = (phoneNumber) => {
        // 移除所有非数字字符
        const digits = phoneNumber.replace(/\D/g, '');
        
        // 北美号码应该是10位数字
        if (digits.length !== 10) {
            return {
                isValid: false,
                message: 'Phone number must be 10 digits'
            };
        }

        // 检查区号（前3位不能以0或1开头）
        const areaCode = digits.substring(0, 3);
        if (areaCode.startsWith('0') || areaCode.startsWith('1')) {
            return {
                isValid: false,
                message: 'Invalid area code'
            };
        }

        // 检查交换码（第4-6位不能以0或1开头）
        const exchange = digits.substring(3, 6);
        if (exchange.startsWith('0') || exchange.startsWith('1')) {
            return {
                isValid: false,
                message: 'Invalid exchange code'
            };
        }

        return {
            isValid: true,
            message: 'Valid phone number'
        };
    };

    // 获取完整的电话号码（包含国家代码）
    const getFullPhoneNumber = () => {
        const country = NORTH_AMERICAN_COUNTRIES[currentCountry];
        const digits = phoneInput.value.replace(/\D/g, '');
        
        if (digits.length === 10) {
            return `${country.code}${digits}`;
        }
        
        return '';
    };

    // 设置电话号码（用于编辑模式）
    const setPhoneNumber = (fullNumber) => {
        if (!fullNumber) return;

        // 如果号码以+1开头，移除国家代码
        let digits = fullNumber.replace(/\D/g, '');
        if (digits.startsWith('1') && digits.length === 11) {
            digits = digits.substring(1);
        }

        if (digits.length === 10) {
            phoneInput.value = digits;
            formatPhoneNumber();
        }
    };

    // 绑定事件监听器
    selectedCountry.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        toggleDropdown();
    });

    // 绑定国家选项点击事件
    countryOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const countryCode = option.dataset.country;
            selectCountry(countryCode);
        });
    });

    // 绑定电话输入框事件
    phoneInput.addEventListener('input', formatPhoneNumber);
    
    // 限制输入长度
    phoneInput.addEventListener('keypress', (e) => {
        const digits = phoneInput.value.replace(/\D/g, '');
        if (digits.length >= 10 && e.key !== 'Backspace' && e.key !== 'Delete') {
            e.preventDefault();
        }
    });

    // 初始化默认值
    initializeDefaults();

    // 返回公共API
    return {
        validatePhoneNumber,
        getFullPhoneNumber,
        setPhoneNumber,
        getCurrentCountry: () => currentCountry,
        selectCountry
    };
};

/**
 * 格式化电话号码用于显示
 * @param {string} phoneNumber - 原始电话号码
 * @returns {string} 格式化后的电话号码
 */
export const formatPhoneForDisplay = (phoneNumber) => {
    if (!phoneNumber) return '';

    // 移除所有非数字字符
    let digits = phoneNumber.replace(/\D/g, '');

    // 如果以1开头且有11位数字，移除国家代码
    if (digits.startsWith('1') && digits.length === 11) {
        digits = digits.substring(1);
    }

    // 如果是10位数字，格式化为 (###) ###-####
    if (digits.length === 10) {
        return `(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}`;
    }

    // 如果不是标准格式，返回原始号码
    return phoneNumber;
};

/**
 * 检查是否为有效的北美电话号码
 * @param {string} phoneNumber - 电话号码
 * @returns {boolean} 是否有效
 */
export const isValidNorthAmericanPhone = (phoneNumber) => {
    if (!phoneNumber) return false;

    const digits = phoneNumber.replace(/\D/g, '');

    // 检查长度
    if (digits.length !== 10 && digits.length !== 11) return false;

    // 如果是11位，必须以1开头
    if (digits.length === 11 && !digits.startsWith('1')) return false;

    // 获取实际的10位号码
    const actualDigits = digits.length === 11 ? digits.substring(1) : digits;

    // 检查区号和交换码
    const areaCode = actualDigits.substring(0, 3);
    const exchange = actualDigits.substring(3, 6);

    return !areaCode.startsWith('0') && !areaCode.startsWith('1') &&
           !exchange.startsWith('0') && !exchange.startsWith('1');
};

export default {
    initPhoneInput,
    formatPhoneForDisplay,
    isValidNorthAmericanPhone,
    NORTH_AMERICAN_COUNTRIES
};
