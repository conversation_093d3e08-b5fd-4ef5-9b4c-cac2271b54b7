// Main JavaScript entry point
import '../styles/main.scss';

// 导入配置系统
import { initializeConfig, baseConfig } from './config';

// 导入路由初始化函数
import { initializeRouter } from './router';

// 导入统一错误处理
import ErrorHandler from './utils/errorHandler';

// 初始化配置并验证
const configValidation = initializeConfig();
if (!configValidation.isValid) {
  console.error('❌ Configuration validation failed. Please check your .env file.');
}

console.log(`🚀 ${baseConfig.appName} v${baseConfig.appVersion} Initialized`);

// HTMX 全局配置
document.addEventListener('htmx:configRequest', (event) => {
  // 添加 CSRF token 到所有请求
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
  if (csrfToken) {
    event.detail.headers['X-CSRF-Token'] = csrfToken;
  }
});

// 添加加载指示器
document.addEventListener('htmx:beforeRequest', (event) => {
  const target = event.detail.target;
  const indicator = target.querySelector('.loading-indicator');
  if (indicator) {
    indicator.style.display = 'flex';
  }
});

document.addEventListener('htmx:afterRequest', (event) => {
  const target = event.detail.target;
  const indicator = target.querySelector('.loading-indicator');
  if (indicator) {
    indicator.style.display = 'none';
  }
});

// 错误处理 - 使用统一错误处理系统
document.addEventListener('htmx:responseError', (event) => {
  const error = event.detail.error;
  const requestInfo = {
    url: event.detail.xhr?.responseURL,
    method: event.detail.xhr?.method,
    status: event.detail.xhr?.status
  };

  // 使用统一错误处理
  ErrorHandler.handleNetworkError(error, requestInfo);
});

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  // 初始化路由
  initializeRouter();
  
  // 可以在这里添加其他初始化代码
  console.log('应用已初始化');
}); 