import Mustache from 'mustache';
import { renderView } from '../viewRenderer';
import { getClientList, createClient, deleteClient, updateClient } from '../services/apiClient';
import { initClientModal, openClientModal } from '../components/clientModal';
import { invalidateDropdownCache } from '../components/customDropdown';

let currentPage = 1;

/**
 * 显示客户创建成功的通知，并提供跳转选项
 * @param {Object} newClientData - 新创建的客户数据
 */
const showClientCreatedNotification = (newClientData) => {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'client-created-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">
                Client "${newClientData.name}" created successfully!
            </span>
            <button class="view-client-btn" type="button">View Client</button>
            <button class="dismiss-btn" type="button">×</button>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        max-width: 350px;
        animation: slideIn 0.3s ease-out;
    `;

    // 添加CSS动画
    if (!document.querySelector('#client-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'client-notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            .notification-message {
                flex: 1;
                font-size: 14px;
            }
            .view-client-btn {
                background: rgba(255,255,255,0.2);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                transition: background 0.2s;
            }
            .view-client-btn:hover {
                background: rgba(255,255,255,0.3);
            }
            .dismiss-btn {
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        `;
        document.head.appendChild(style);
    }

    // 添加到页面
    document.body.appendChild(notification);

    // 事件处理
    const viewBtn = notification.querySelector('.view-client-btn');
    const dismissBtn = notification.querySelector('.dismiss-btn');

    viewBtn.addEventListener('click', () => {
        // 跳转到第1页查看新客户
        fetchAndRenderClients(1);
        notification.remove();
    });

    dismissBtn.addEventListener('click', () => {
        notification.remove();
    });

    // 5秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
};

/**
 * 显示简单的成功消息
 * @param {string} message - 消息内容
 */
const showSimpleSuccessMessage = (message) => {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'simple-success-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="dismiss-btn" type="button">×</button>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        max-width: 350px;
        animation: slideIn 0.3s ease-out;
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 事件处理
    const dismissBtn = notification.querySelector('.dismiss-btn');
    dismissBtn.addEventListener('click', () => {
        notification.remove();
    });

    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
};

const fetchAndRenderClients = async (page) => {
    try {
        const container = document.querySelector('.client-list-container');
        const tableBody = container?.querySelector('tbody');
        const footer = document.querySelector('.page-footer');

        if (!container || !tableBody || !footer) {
            console.error('Required elements not found for rendering clients.');
            return;
        }
        
        // Show loading indicator
        container.querySelector('.loading-indicator').style.display = 'block';
        tableBody.innerHTML = ''; // Clear old table data
        footer.innerHTML = '';

        const data = await getClientList({ page, limit: 8 });
        currentPage = data.pgn.currentPage;



        // Hide loading indicator
        container.querySelector('.loading-indicator').style.display = 'none';

        const rowTemplate = `
            {{#items}}
            <tr data-client-id="{{id}}">
                <td><img src="{{profilePictureUrl}}" alt="{{name}}" class="profile-picture"></td>
                <td>{{name}}</td>
                <td>{{email}}</td>
                <td>{{phone}}</td>
                <td class="memo-cell">{{memo}}</td>
                <td>
                    <div class="item-actions" style="min-width: 140px; display: flex; justify-content: flex-end; align-items: center;">
                        <button class="action-btn edit-btn"><img src="/icons/edit/edit.svg" alt="Edit"></button>
                        <button class="action-btn delete-btn-initial"><img src="/icons/delete/delete.svg" alt="Delete"></button>
                        <button class="action-btn confirm-delete-btn" style="display: none; background-color: #d9534f; color: white; border: none; padding: 0 12px; line-height: 28px; height: 30px; border-radius: 4px; margin-right: 8px;">Delete</button>
                        <button class="action-btn cancel-delete-btn" style="display: none; background: transparent; border: none; box-shadow: none; text-decoration: underline; padding: 0; color: #888;">Cancel</button>
                    </div>
                </td>
            </tr>
            {{/items}}
        `;
        tableBody.innerHTML = Mustache.render(rowTemplate, { items: data.items });

        const paginationTemplate = `
            <div class="pagination-controls">
                <button class="pagination-btn prev-btn" {{#isFirstPage}}disabled{{/isFirstPage}}>◀</button>
                <span>{{currentPage}} / {{totalPages}}</span>
                <button class="pagination-btn next-btn" {{#isLastPage}}disabled{{/isLastPage}}>▶</button>
            </div>
        `;
        const paginationData = {
            ...data.pgn,
            isFirstPage: data.pgn.currentPage === 1,
            isLastPage: data.pgn.currentPage === data.pgn.totalPages
        };

        footer.innerHTML = Mustache.render(paginationTemplate, paginationData);

        document.querySelector('.prev-btn')?.addEventListener('click', () => {
            if (currentPage > 1) fetchAndRenderClients(currentPage - 1);
        });
        document.querySelector('.next-btn')?.addEventListener('click', () => {
            if (currentPage < data.pgn.totalPages) fetchAndRenderClients(currentPage + 1);
        });

    } catch (error) {
        console.error("Failed to fetch and render clients:", error);
    }
};

const setupClientListEventListeners = (container) => {
    container.addEventListener('click', async (event) => {
        const target = event.target;
        const clientRow = target.closest('tr[data-client-id]');
        if (!clientRow) return;

        const clientId = clientRow.dataset.clientId;
        const clientName = clientRow.querySelector('td:nth-child(2)').textContent;

        const initialDeleteBtn = target.closest('.delete-btn-initial');
        const confirmBtn = target.closest('.confirm-delete-btn'); // Legacy confirm
        const cancelBtn = target.closest('.cancel-delete-btn'); // Legacy cancel

        // --- New Merge-Delete Logic ---
        if (initialDeleteBtn) {
            try {
                // Fetch all clients to check count and get merge targets
                const allClientsData = await getClientList({ limit: 0 }); // Use 0 or a large number for all
                console.log('allClientsData', allClientsData);

                if (allClientsData.pgn.totalItems <= 1) {
                    alert('This is the only client and cannot be deleted.');
                    return;
                }

                const mergeCandidates = allClientsData.items.filter(client => client.id !== clientId);
                console.log('mergeCandidates', mergeCandidates);
                showMergeModal(clientId, clientName, mergeCandidates);

            } catch (error) {
                console.error('Failed to initiate deletion process:', error);
                alert(`Error: ${error.message}`);
            }
            return; // Stop further processing
        }
        
        // --- Legacy inline confirmation logic (can be removed if not needed) ---
        const actionsContainer = clientRow.querySelector('.item-actions');
        if (!actionsContainer) return;
        
        const editBtn = actionsContainer.querySelector('.edit-btn');
        const initialDelBtn = actionsContainer.querySelector('.delete-btn-initial');
        const confirmDelBtn = actionsContainer.querySelector('.confirm-delete-btn');
        const cancelDelBtn = actionsContainer.querySelector('.cancel-delete-btn');

        if (cancelBtn) {
            editBtn.style.display = '';
            initialDelBtn.style.display = '';
            confirmDelBtn.style.display = 'none';
            cancelDelBtn.style.display = 'none';
        }

        if (confirmBtn) {
            // This path should ideally not be taken with the new modal flow
            // Kept for safety, but logic should be migrated
            try {
                // Old direct delete, which will fail without a target ID on the backend
                alert("Deletion must be done through the merge process.");
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        }
        // --- End of Legacy Logic ---


        const editBtnClicked = target.closest('.edit-btn');
        if (editBtnClicked) {
            const email = clientRow.querySelector('td:nth-child(3)').textContent;
            const phone = clientRow.querySelector('td:nth-child(4)').textContent;
            const memo = clientRow.querySelector('td.memo-cell').textContent;
            const avatarUrl = clientRow.querySelector('.profile-picture').src;
            
            openClientModal({ id: clientId, name: clientName, email, phone, memo, avatarUrl }, () => {
                fetchAndRenderClients(currentPage);
            });
        }
    });
};

const showMergeModal = (sourceId, sourceName, mergeCandidates) => {
    const modal = document.getElementById('merge-client-modal');
    if (!modal) return;

    modal.querySelector('#source-client-name').textContent = sourceName;
    
    // --- Custom Dropdown Logic ---
    const dropdown = modal.querySelector('#target-client-dropdown');
    const selectedValueSpan = dropdown.querySelector('.selected-value');
    const optionsList = dropdown.querySelector('.options-list');

    // 1. Populate options
    optionsList.innerHTML = Mustache.render('{{#items}}<li data-value="{{id}}">{{name}}</li>{{/items}}', { items: mergeCandidates });

    // 2. Add event listeners for dropdown behavior
    const handleDropdownClick = () => {
        dropdown.classList.toggle('open');
    };

    const handleOptionClick = (event) => {
        if (event.target.tagName === 'LI') {
            event.stopPropagation(); // Stop event from bubbling up to the parent dropdown
            const value = event.target.dataset.value;
            const text = event.target.textContent;
            
            selectedValueSpan.textContent = text;
            dropdown.dataset.value = value; // Store selected value on the main dropdown element

            dropdown.classList.remove('open');
        }
    };
    
    dropdown.addEventListener('click', handleDropdownClick);
    optionsList.addEventListener('click', handleOptionClick);
    
    // --- Modal Actions ---
    modal.classList.remove('hidden');

    const handleConfirm = async () => {
        const targetId = dropdown.dataset.value; // Get value from custom dropdown
        if (!targetId) {
            alert('Please select a client to merge into.');
            return;
        }

        try {
            await deleteClient(sourceId, targetId);
            modal.classList.add('hidden');
            fetchAndRenderClients(currentPage);
            // 触发客户缓存失效事件
            invalidateDropdownCache('clients-changed');
        } catch (error) {
            alert(`Failed to merge and delete client: ${error.message}`);
        } finally {
            cleanup();
        }
    };

    const handleCancel = () => {
        modal.classList.add('hidden');
        cleanup();
    };
    
    const confirmBtn = modal.querySelector('#confirm-merge-btn');
    const cancelBtn = modal.querySelector('#cancel-merge-btn');

    // Cleanup function to remove listeners
    const cleanup = () => {
        dropdown.removeEventListener('click', handleDropdownClick);
        optionsList.removeEventListener('click', handleOptionClick);
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
    };

    confirmBtn.addEventListener('click', handleConfirm, { once: true });
    cancelBtn.addEventListener('click', handleCancel, { once: true });
};

const setupPage = () => {
    const newClientBtn = document.querySelector('.new-client-btn');
    if (newClientBtn) {
        newClientBtn.addEventListener('click', () => {
            openClientModal(null, (newClientData) => {
                // 保持在当前页，除非用户选择查看新创建的客户
                fetchAndRenderClients(currentPage).then(() => {
                    // 只有当不在第1页时才显示跳转选项
                    if (currentPage > 1) {
                        showClientCreatedNotification(newClientData);
                    } else {
                        // 如果已经在第1页，只显示简单的成功消息
                        showSimpleSuccessMessage(`Client "${newClientData.name}" created successfully!`);
                    }
                });
            });
        });
    }

    const clientContainer = document.querySelector('.client-list-container');
    if (clientContainer) {
        setupClientListEventListeners(clientContainer);
    }
    
    // Initialize the modal logic once the page is set up
    initClientModal();
};

export const renderClients = async () => {
    try {
        await renderView('clients', '#app-container');
        await fetchAndRenderClients(1);
        setupPage();
    } catch (error) {
        console.error("Failed to render clients page:", error);
    }
}; 