import { getCurrentUser, refreshAuth, logout, isAuthenticated } from '../services/apiClient';
import jwtManager from '../services/jwtManager';
import { renderView } from '../viewRenderer';

/**
 * 开发工具页面 - 用于测试 JWT 功能
 */
export async function renderDevTools() {
  console.log('🔧 渲染开发工具页面');

  const targetSelector = '#app-container';

  try {
    // 使用模板系统渲染页面
    const targetElement = await renderView('dev-tools', targetSelector);
    if (!targetElement) {
      console.error("Dev tools render failed: target element not found.");
      return;
    }

    // 更新当前时间
    const timeElement = targetElement.querySelector('#dev-logs .text-success');
    if (timeElement) {
      timeElement.textContent = `[${new Date().toLocaleTimeString()}] 开发工具已加载`;
    }

    // 立即初始化开发工具
    setTimeout(() => {
      initDevTools();
    }, 100);

    return targetElement;
  } catch (error) {
    console.error('渲染开发工具页面失败:', error);
    return null;
  }
}

/**
 * 初始化开发工具功能
 */
export function initDevTools() {
  console.log('🔧 初始化开发工具功能');

  // 创建全局 devTools 对象
  window.devTools = {
    log: (message, type = 'info') => {
      const logsContainer = document.getElementById('dev-logs');
      if (logsContainer) {
        const timestamp = new Date().toLocaleTimeString();
        const colorClass = `log-${type}`;
        const logEntry = document.createElement('div');
        logEntry.className = colorClass;
        logEntry.textContent = `[${timestamp}] ${message}`;
        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;
      }
    },

    checkStatus: async () => {
      try {
        console.log('🔍 检查 JWT 状态...');
        window.devTools.log('检查 JWT 状态...', 'info');

        const isAuth = isAuthenticated();
        const userInfo = getCurrentUser();
        const jwt = localStorage.getItem('dev_jwt');

        console.log('认证状态:', { isAuth, userInfo, hasJWT: !!jwt });

        let statusHtml = '';

        if (isAuth && userInfo) {
          const expiresIn = Math.max(0, Math.floor((userInfo.expiresAt - new Date()) / 1000));
          const expiresInMinutes = Math.floor(expiresIn / 60);
          
          statusHtml = `
            <div class="status-badge status-valid">✅ 已认证</div>
            <div class="mt-2">
              <strong>用户ID:</strong> ${userInfo.userId}<br>
              <strong>角色:</strong> ${userInfo.roles.join(', ')}<br>
              <strong>过期时间:</strong> ${userInfo.expiresAt.toLocaleString()}<br>
              <strong>剩余时间:</strong> ${expiresInMinutes} 分钟
            </div>
          `;
          
          // 显示 JWT 详情
          if (jwt) {
            try {
              const payload = JSON.parse(atob(jwt.split('.')[1]));
              document.getElementById('jwt-details').innerHTML = `
                <pre class="bg-light p-3 rounded"><code>${JSON.stringify(payload, null, 2)}</code></pre>
              `;
            } catch (e) {
              document.getElementById('jwt-details').innerHTML = '<p class="text-danger">JWT 格式错误</p>';
            }
          }
          
          window.devTools.log(`JWT 有效，剩余 ${expiresInMinutes} 分钟`, 'success');
        } else {
          statusHtml = `
            <div class="status-badge status-invalid">❌ 未认证</div>
            <div class="mt-2">
              <p class="text-muted">没有有效的 JWT token</p>
            </div>
          `;
          document.getElementById('jwt-details').innerHTML = '<p class="text-muted">无 JWT 信息</p>';
          window.devTools.log('JWT 无效或不存在', 'warning');
        }
        
        document.getElementById('jwt-status').innerHTML = statusHtml;
      } catch (error) {
        window.devTools.log(`检查状态失败: ${error.message}`, 'error');
      }
    },

    refreshJWT: async () => {
      try {
        window.devTools.log('正在刷新 JWT...', 'info');
        const success = await refreshAuth();
        
        if (success) {
          window.devTools.log('JWT 刷新成功', 'success');
          await window.devTools.checkStatus();
        } else {
          window.devTools.log('JWT 刷新失败', 'error');
        }
      } catch (error) {
        window.devTools.log(`刷新 JWT 失败: ${error.message}`, 'error');
      }
    },

    clearJWT: () => {
      logout();
      window.devTools.log('JWT 已清除', 'warning');
      window.devTools.checkStatus();
    },

    testAPI: async () => {
      const endpoint = document.getElementById('api-endpoint').value;
      const resultsContainer = document.getElementById('api-results');
      
      try {
        window.devTools.log(`测试 API: ${endpoint}`, 'info');
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 请求中...';
        
        // 动态导入 API 函数
        const { default: apiClient } = await import('../services/apiClient');
        
        let result;
        switch (endpoint) {
          case '/video/admin/videos':
            result = await apiClient.getVideos({ page: 1, limit: 5 });
            break;
          case '/video/admin/videos/stats':
            result = await apiClient.getStats();
            break;
          case '/video/admin/categories':
            result = await apiClient.getCategoryList({ page: 1, limit: 5 });
            break;
          case '/video/admin/advertisers':
            result = await apiClient.getClientList({ page: 1, limit: 5 });
            break;
          default:
            throw new Error('未知的 API 端点');
        }
        
        resultsContainer.innerHTML = `
          <div class="alert alert-success">
            <strong>✅ 请求成功</strong>
            <pre class="mt-2 mb-0"><code>${JSON.stringify(result, null, 2)}</code></pre>
          </div>
        `;
        
        window.devTools.log(`API 测试成功: ${endpoint}`, 'success');
      } catch (error) {
        resultsContainer.innerHTML = `
          <div class="alert alert-danger">
            <strong>❌ 请求失败</strong>
            <p class="mt-2 mb-0">${error.message}</p>
          </div>
        `;
        
        window.devTools.log(`API 测试失败: ${error.message}`, 'error');
      }
    },

    clearResults: () => {
      document.getElementById('api-results').innerHTML = '';
    },

    clearLogs: () => {
      const logsContainer = document.getElementById('dev-logs');
      if (logsContainer) {
        logsContainer.innerHTML = `<div class="text-success">[${new Date().toLocaleTimeString()}] 日志已清除</div>`;
      }
    },

    switchUser: async () => {
      const select = document.getElementById('user-type-select');
      const userType = select.value;

      try {
        window.devTools.log(`切换到用户类型: ${userType}`, 'info');

        // 使用 jwtManager 切换用户
        const success = await jwtManager.switchUser(userType);

        if (success) {
          window.devTools.log(`用户切换成功: ${userType}`, 'success');
          await window.devTools.checkStatus();
        } else {
          window.devTools.log(`用户切换失败: ${userType}`, 'error');
        }
      } catch (error) {
        window.devTools.log(`切换用户失败: ${error.message}`, 'error');
      }
    }
  };

  console.log('🔧 开发工具初始化完成');

  // 初始检查状态
  setTimeout(() => {
    console.log('🔍 开始初始状态检查');
    if (window.devTools && window.devTools.checkStatus) {
      window.devTools.checkStatus();
    } else {
      console.error('❌ devTools.checkStatus 不存在');
    }
  }, 500);
}
