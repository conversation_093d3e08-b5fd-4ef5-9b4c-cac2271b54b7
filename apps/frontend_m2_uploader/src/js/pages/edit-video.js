import { renderView } from '../viewRenderer';
import Hls from 'hls.js';
import {
    getCategoryList,
    getClientList,
    getVideoById,
    updateVideo,
    updateVideoFromChunkedUpload,
    uploadThumbnail,
    searchMlsProperties,
} from '../services/apiClient';
import { navigateTo, setPageCleanup } from '../router';
import { API_BASE_URL, MEDIA_BASE_URL, businessConfig } from '../config';
import { setupCustomDropdown } from '../components/customDropdown';
import { setupFileUploader } from '../components/fileUploader';

// Store the initial state of the video data for dirty checking
let videoDataSnapshot = {};
let videoUploaderInstance = null;
let coverUploaderInstance = null;

// --- State Flags ---
let isMetadataDirty = false;
let isCoverDirty = false;
let isVideoFileDirty = false;

// 全局变量存储当前的HLS实例
let currentEditHls = null;

const showPreviewModal = (url, mediaType) => {
    const modal = document.getElementById('preview-modal');
    const previewContent = document.getElementById('preview-content');
    if (!modal || !previewContent) return;

    previewContent.innerHTML = '';

    // 清理之前的HLS实例
    if (currentEditHls) {
        currentEditHls.destroy();
        currentEditHls = null;
    }

    // Determine media type explicitly
    const isVideo = mediaType === 'video';

    let mediaElement;
    if (isVideo) {
        mediaElement = document.createElement('video');
        mediaElement.controls = true;
        mediaElement.autoplay = true;

        // 根据文件扩展名判断是否使用HLS播放器
        if (url.endsWith('.m3u8')) {
            // HLS文件，使用HLS播放器
            if (Hls.isSupported()) {
                currentEditHls = new Hls();
                currentEditHls.loadSource(url);
                currentEditHls.attachMedia(mediaElement);
                currentEditHls.on(Hls.Events.MANIFEST_PARSED, () => {
                    mediaElement.play();
                });
            } else if (mediaElement.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持HLS
                mediaElement.src = url;
                mediaElement.play();
            } else {
                console.error('HLS not supported in this browser');
                alert('您的浏览器不支持HLS播放');
                return;
            }
        } else {
            // MP4文件，使用普通video元素
            mediaElement.src = url;
        }
    } else {
        mediaElement = document.createElement('img');
        mediaElement.src = url;
    }

    previewContent.appendChild(mediaElement);
    modal.classList.remove('hidden');
};

const setupModalControls = () => {
    const modal = document.getElementById('preview-modal');
    const closeBtn = document.getElementById('close-preview-btn');
    const previewContent = document.getElementById('preview-content');

    if (!modal || !closeBtn || !previewContent) return;

    const closeModal = () => {
        modal.classList.add('hidden');
        previewContent.innerHTML = '';

        // 清理HLS实例
        if (currentEditHls) {
            currentEditHls.destroy();
            currentEditHls = null;
        }

        // Note: Object URL revocation should happen here if needed,
        // but for simplicity, we rely on browser's garbage collection for now.
    };

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (event) => {
        if (event.target === modal) closeModal();
    });
};

const populateForm = (data) => {
    videoDataSnapshot = JSON.parse(JSON.stringify(data)); // Create a deep copy for dirty checking

    // Text inputs
    document.getElementById('title-cn').value = data.title?.zh || '';
    document.getElementById('title-en').value = data.title?.en || '';
    document.getElementById('description-cn').value = data.description?.zh || '';
    document.getElementById('description-en').value = data.description?.en || '';
    document.getElementById('url-link').value = data.externalUrl || '';

    // Set initial IDs for custom dropdowns BEFORE they are initialized
    const categoryDropdown = document.getElementById('category-dropdown');
    if (data.categoryId) {
        categoryDropdown.dataset.initialId = data.categoryId;
    }

    const clientDropdown = document.getElementById('client-dropdown');
    if (data.clientId) {
        clientDropdown.dataset.initialId = data.clientId;
    }
    
    // The rest of the setup happens in initializePage after dropdowns are configured

    // --- NEW: Update Status Badge ---
    const statusBadge = document.getElementById('video-status-badge');
    if (statusBadge) {
        const status = data.status || 'unknown';
        
        // Use user-friendly text for specific statuses
        const statusText = status === 'ProcessingFailed' ? 'Failed' : status;
        statusBadge.textContent = statusText;

        // Remove any existing status classes
        statusBadge.className = 'status-badge';
         
        // Convert backend status (PascalCase) to CSS-friendly class name (kebab-case)
        // e.g., "ProcessingFailed" becomes "processing-failed"
        const cssClassStatus = status.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
        statusBadge.classList.add(`status-${cssClassStatus}`);
    }
};

const configureUiByStatus = (video) => {
    const form = document.getElementById('edit-video-form');
    const allInputs = form.querySelectorAll('input, textarea, select');
    const videoUploaderContainer = document.getElementById('video-uploader');
    const coverUploaderContainer = document.getElementById('cover-uploader');
    const videoUploaderInput = videoUploaderContainer.querySelector('.file-input');
    const coverUploaderInput = coverUploaderContainer.querySelector('.file-input');

    const submitBtn = document.getElementById('submit-changes-btn');
    const actionBtn = document.getElementById('status-action-btn');

    // Default: all enabled
    allInputs.forEach(input => input.disabled = false);
    videoUploaderInput.disabled = false;
    coverUploaderInput.disabled = false;
    submitBtn.style.display = 'inline-block';
    actionBtn.style.display = 'inline-block';

    const failureBox = document.getElementById('failure-reason-container');
    failureBox.classList.add('hidden'); // Hide by default

    switch (video.status) {
        case 'Draft':
            // Full editing capabilities
            break;
        case 'ProcessingFailed':
            if (video.processingError) {
                const reasonText = document.getElementById('failure-reason-text');
                reasonText.textContent = video.processingError;
                failureBox.classList.remove('hidden');
            }
            break;

        case 'Published':
        case 'Unpublished':
            // For published/unpublished, video file upload is disabled by default
            // videoUploaderInput.disabled = true;
            // videoUploaderContainer.classList.add('disabled');
            break;

        case 'Pending':
        case 'Processing':
            allInputs.forEach(input => input.disabled = true);
            videoUploaderInput.disabled = true;
            coverUploaderInput.disabled = true;
            submitBtn.style.display = 'none';
            actionBtn.style.display = 'none';
            break;
    }
};

const initializePage = async (videoData) => {
    try {
        // Data is now passed directly, and form is populated before this function is called.
        // populateForm(videoData);
        configureUiByStatus(videoData);

        const submitBtn = document.getElementById('submit-changes-btn');
        const actionBtn = document.getElementById('status-action-btn');
        
        // This function reliably checks if the video uploader has a file,
        // based on the visibility of its UI elements.
        const hasVideoFile = () => {
            if (!videoUploaderInstance) return false;
            const detailsElement = document.querySelector('#video-uploader .file-details');
            return detailsElement ? !detailsElement.classList.contains('hidden') : false;
        };

        // This function will be the single source of truth for button states
        const updateButtonStates = () => {
            const currentStatus = videoData.status;

            // --- Determine UI Mode ---
            // "Upload" mode is for drafts, failed videos, or when a new video file is staged.
            const isUploadMode = isVideoFileDirty || currentStatus === 'Draft' || currentStatus === 'ProcessingFailed';

            const titleCn = document.getElementById('title-cn').value.trim();
            const isMetadataValid = titleCn !== '';
            const isFilePresent = hasVideoFile();
            const hasAnyChanges = isMetadataDirty || isCoverDirty;


            if (isUploadMode) {
                // --- UPLOAD MODE --- (Looks like the upload page)
                submitBtn.textContent = 'Save as Draft';
                submitBtn.dataset.action = 'save_as_draft';
                actionBtn.textContent = 'Publish';
                actionBtn.dataset.action = 'publish';

                // "Save as Draft" is enabled if metadata is valid.
                submitBtn.disabled = !isMetadataValid;
                // "Publish" is enabled if metadata is valid AND a video file exists.
                actionBtn.disabled = !isMetadataValid || !isFilePresent;

            } else {
                // --- METADATA EDIT MODE --- (For Published/Unpublished videos)
                submitBtn.textContent = 'Save Changes';
                submitBtn.dataset.action = 'save_changes';
                actionBtn.textContent = currentStatus === 'Published' ? 'Unpublish' : 'Publish';
                actionBtn.dataset.action = currentStatus === 'Published' ? 'unpublish' : 'publish';
                
                // "Save Changes" is only enabled if there are actual changes.
                submitBtn.disabled = !isMetadataValid || !hasAnyChanges;
                // "Publish/Unpublish" button is always enabled if metadata is valid.
                actionBtn.disabled = !isMetadataValid;
            }
        };

        const handleVideoFileChange = (newFile) => {
            const videoUploaderContainer = document.getElementById('video-uploader');
            const videoUploaderInput = videoUploaderContainer.querySelector('.file-input');

            if (newFile) {
                isVideoFileDirty = true;
                // When a new video is chosen, UI should switch to "upload mode"
                videoUploaderContainer.classList.remove('disabled');
                 // Ensure the input is enabled if it was previously disabled
                videoUploaderInput.disabled = false;
            } else {
                // User cleared the file selection. Revert to metadata edit mode.
                isVideoFileDirty = false;
                const currentStatus = videoDataSnapshot.status;
                if (currentStatus === 'Published' || currentStatus === 'Unpublished') {
                    videoUploaderInput.disabled = true;
                    videoUploaderContainer.classList.add('disabled');
                } else {
                    // For Drafts/Failed, clearing the file just means it's ready for a new one.
                    videoUploaderInput.disabled = false;
                    videoUploaderContainer.classList.remove('disabled');
                }
            }
            updateButtonStates();
        };

        const handleMetadataChange = () => {
            // Simple dirty check against the snapshot
            const newTitleZh = document.getElementById('title-cn').value;
            const newTitleEn = document.getElementById('title-en').value;
            const newDescZh = document.getElementById('description-cn').value;
            const newDescEn = document.getElementById('description-en').value;
            const newUrl = document.getElementById('url-link').value;
            const categoryId = document.getElementById('category-dropdown').dataset.value;
            const clientId = document.getElementById('client-dropdown').dataset.value;
            const newPropertyIds = Array.from(document.querySelectorAll('.mls-id-input')).map(input => input.value).filter(Boolean).join(',');
            const oldPropertyIds = (videoDataSnapshot.propertyIds || []).join(',');

            if (newTitleZh !== (videoDataSnapshot.title?.zh || '') ||
                newTitleEn !== (videoDataSnapshot.title?.en || '') ||
                newDescZh !== (videoDataSnapshot.description?.zh || '') ||
                newDescEn !== (videoDataSnapshot.description?.en || '') ||
                newUrl !== (videoDataSnapshot.externalUrl || '') ||
                categoryId !== (videoDataSnapshot.categoryId || '') ||
                clientId !== (videoDataSnapshot.clientId || '') ||
                newPropertyIds !== oldPropertyIds
            ) {
                isMetadataDirty = true;
            } else {
                // This else part is important to revert the dirty flag if user reverts changes
                isMetadataDirty = false;
            }
            updateButtonStates();
        };

        const handleCoverFileChange = () => {
            isCoverDirty = true;
            updateButtonStates();
        };


        const handleSubmit = async (event) => {
            event.preventDefault();
            const clickedButton = event.currentTarget;

            // 防止重复点击 - 如果按钮已经被禁用，直接返回
            if (clickedButton.disabled) {
                return;
            }

            // Rely on a stable data-action attribute instead of button text.
            const action = clickedButton.dataset.action; 

            if (!action) {
                console.error("Clicked button is missing a 'data-action' attribute.");
                return;
            }

            const formData = new FormData();
            
            // --- Metadata Dirty Check and Population ---
            const changedMetadata = {};
            // Title (example for one field, expand for all)
            const newTitle = {
                zh: document.getElementById('title-cn').value,
                en: document.getElementById('title-en').value,
            };
            if (newTitle.zh !== (videoDataSnapshot.title?.zh || '') || newTitle.en !== (videoDataSnapshot.title?.en || '')) {
                changedMetadata.title = newTitle;
            }
             // Description
            const newDescription = {
                zh: document.getElementById('description-cn').value,
                en: document.getElementById('description-en').value,
            };
            if (newDescription.zh !== (videoDataSnapshot.description?.zh || '') || newDescription.en !== (videoDataSnapshot.description?.en || '')) {
                changedMetadata.description = newDescription;
            }
             // Other fields
            const categoryId = document.getElementById('category-dropdown').dataset.value;
            if (categoryId !== (videoDataSnapshot.categoryId || '')) {
                changedMetadata.categoryId = categoryId;
            }
            const clientId = document.getElementById('client-dropdown').dataset.value;
            if (clientId !== (videoDataSnapshot.clientId || '')) {
                changedMetadata.clientId = clientId;
            }
            const externalUrl = document.getElementById('url-link').value;
            if (externalUrl !== (videoDataSnapshot.externalUrl || '')) {
                changedMetadata.externalUrl = externalUrl;
            }

            // Also check property IDs
            const newPropertyIds = Array.from(document.querySelectorAll('.mls-id-input')).map(input => input.value).filter(Boolean);
            const oldPropertyIds = videoDataSnapshot.propertyIds || [];
            let propertyIdsChanged = false;
            if (JSON.stringify(newPropertyIds) !== JSON.stringify(oldPropertyIds)) {
                changedMetadata.propertyIds = newPropertyIds;
                propertyIdsChanged = true;
            }

            // 为传统上传准备FormData
            if (Object.keys(changedMetadata).length > 0) {
                formData.append('metadata', JSON.stringify(changedMetadata));
            }

            // --- File Attachment ---
            const newCoverFile = coverUploaderInstance.getFile();
            if (newCoverFile) formData.append('thumbnail', newCoverFile);
            
            const newVideoFile = videoUploaderInstance.getFile();
            if (newVideoFile) formData.append('video', newVideoFile);

            // --- Action and final checks ---
             // Determine the definitive action for the backend
            let finalAction = action;
            if (isVideoFileDirty && (action === 'publish' || action === 'republish')) {
                finalAction = 'republish';
            }
            formData.append('action', finalAction);


            const hasMetadataChanges = formData.has('metadata');
            const hasFileChanges = formData.has('thumbnail') || formData.has('video');
            const isStateChangeOnlyAction = ['unpublish', 'publish'].includes(finalAction);

            // If the action is just to change state (publish/unpublish), it's allowed without other changes.
            if (isStateChangeOnlyAction && !hasMetadataChanges && !hasFileChanges) {
                 // Allow pure state changes to proceed
            } else if (!hasMetadataChanges && !hasFileChanges) {
                alert("No changes detected.");
                return;
            }


            try {
                submitBtn.disabled = true;
                actionBtn.disabled = true;

                // 检查是否需要使用分块上传
                const newVideoFile = videoUploaderInstance.getFile();
                const newCoverFile = coverUploaderInstance.getFile();

                if (newVideoFile && videoUploaderInstance.shouldUseChunkedUpload()) {
                    console.log('使用分块上传处理大文件...');
                    await handleChunkedUpload(changedMetadata, newVideoFile, newCoverFile, finalAction);
                } else {
                    console.log('使用传统上传...');
                    await handleTraditionalUpload(formData);
                }

                // 先导航，再显示成功消息，避免消息显示错误阻止导航
                try {
                    alert('Action completed successfully!');
                } catch (messageError) {
                    console.error('Error showing success message:', messageError);
                }

                // 每次编辑完点击按钮后都跳转到dashboard
                navigateTo('/dashboard');
            } catch (error) {
                alert(`Action failed: ${error.message}`);
                // 只有在出错时才重新启用按钮
                submitBtn.disabled = false;
                actionBtn.disabled = false;
            }
        };

        // 处理传统上传
        const handleTraditionalUpload = async (formData) => {
            const updatedVideoData = await updateVideo(videoData.id, formData);

            // 更新本地视频数据为最新状态
            videoData = updatedVideoData;
            videoDataSnapshot = JSON.parse(JSON.stringify(updatedVideoData));

            // 重置dirty状态标志
            isMetadataDirty = false;
            isCoverDirty = false;
            isVideoFileDirty = false;
        };

        // 处理分块上传
        const handleChunkedUpload = async (changedMetadata, newVideoFile, newCoverFile, finalAction) => {
            // 1. 如果有封面图，先单独上传封面图
            let coverUploadResult = null;
            if (newCoverFile) {
                console.log('开始上传封面图...');
                try {
                    coverUploadResult = await uploadThumbnail(newCoverFile);
                    console.log('封面图上传成功:', coverUploadResult);
                } catch (error) {
                    console.error('封面图上传失败:', error);
                    throw new Error('封面图上传失败: ' + error.message);
                }
            }

            // 2. 分块上传视频文件
            let videoUploadResult = null;
            if (newVideoFile) {
                console.log('开始分块上传视频文件...');
                videoUploadResult = await videoUploaderInstance.startChunkedUpload();
                console.log('视频分块上传成功:', videoUploadResult);
            }

            // 3. 准备元数据（changedMetadata已经包含了所有变更，包括propertyIds）
            const metadata = { ...changedMetadata };

            // 4. 使用分块上传结果更新视频记录（纯JSON请求）
            const updateData = {
                metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
                action: finalAction,
                draftVideoGouploadPath: videoUploadResult ? videoUploadResult.path : undefined,
                draftThumbGouploadPath: coverUploadResult ? coverUploadResult.path : undefined,
            };

            // 移除undefined字段
            Object.keys(updateData).forEach(key => {
                if (updateData[key] === undefined) {
                    delete updateData[key];
                }
            });

            const updatedVideoData = await updateVideoFromChunkedUpload(videoData.id, updateData);

            // 更新本地视频数据为最新状态
            videoData = updatedVideoData;
            videoDataSnapshot = JSON.parse(JSON.stringify(updatedVideoData));

            // 重置dirty状态标志
            isMetadataDirty = false;
            isCoverDirty = false;
            isVideoFileDirty = false;
        };

        // --- Event Listener Setup ---
        document.getElementById('title-cn').addEventListener('input', handleMetadataChange);
        document.getElementById('title-en').addEventListener('input', handleMetadataChange);
        document.getElementById('description-cn').addEventListener('input', handleMetadataChange);
        document.getElementById('description-en').addEventListener('input', handleMetadataChange);
        document.getElementById('url-link').addEventListener('input', handleMetadataChange);
        // Custom dropdowns will need a way to bubble up a change event
        document.getElementById('category-dropdown').addEventListener('click', handleMetadataChange);
        document.getElementById('client-dropdown').addEventListener('click', handleMetadataChange);
        // We need to listen for changes on the MLS ID container
        document.getElementById('mls-id-container').addEventListener('change', handleMetadataChange);
        

        submitBtn.addEventListener('click', handleSubmit);
        actionBtn.addEventListener('click', handleSubmit);
        
        // No need to set data-action here anymore, updateButtonStates is the source of truth.

        let initialVideoUrl = videoData.previewVideoUrl;
        if (initialVideoUrl && !initialVideoUrl.startsWith('http')) {
            // 使用nginx提供的媒体文件服务
            initialVideoUrl = `${MEDIA_BASE_URL}${videoData.previewVideoUrl}`;
        }

        let initialCoverUrl = videoData.previewThumbUrl;
        if (initialCoverUrl && !initialCoverUrl.startsWith('http')) {
            // 使用nginx提供的媒体文件服务
            initialCoverUrl = `${MEDIA_BASE_URL}${videoData.previewThumbUrl}`;
        }

        videoUploaderInstance = setupFileUploader('video-uploader', {
            existingMedia: {
                thumbUrl: initialCoverUrl,
                previewUrl: initialVideoUrl,
                name: videoData.title?.zh || 'video'
            },
            onPreview: (url) => showPreviewModal(url, 'video'),
            onFileChange: handleVideoFileChange,
        });

        coverUploaderInstance = setupFileUploader('cover-uploader', {
            existingMedia: {
                thumbUrl: initialCoverUrl,
                previewUrl: initialCoverUrl,
                name: videoData.title?.zh || 'cover'
            },
            onPreview: (url) => showPreviewModal(url, 'image'),
            onFileChange: handleCoverFileChange,
        });
        
        setupMlsIdComponent(videoData.propertyIds || []);
        setupModalControls();
        updateButtonStates(); // Initial button state setup
        setupTranslation(); // Initialize translation buttons

    } catch (error) {
        console.error('Failed to initialize edit page:', error);
        const container = document.querySelector('.upload-form-container');
        if(container) {
            container.innerHTML = `<p class="error-message">Failed to load video data. Please try again later.</p>`;
        }
    }
};

/**
 * Main function to render the video edit page.
 */
export const renderEditVideo = async (params) => {
    try {
        // First, render the static HTML template
        await renderView('edit-video', '#app-container');
        
        // Then, fetch the data for the specific video
        if (!params || !params.id) {
            throw new Error("No video ID provided for editing.");
        }
        const videoData = await getVideoById(params.id);

        // 检查视频状态，如果是Pending或Processing，跳转到dashboard
        // 因为这些状态下不应该提供编辑页面
        if (videoData.status === 'Pending' || videoData.status === 'Processing') {
            alert('视频正在处理中，无法编辑。');
            navigateTo('/dashboard');
            return;
        }

        // Immediately populate the form with the data. This will set the
        // 'data-initial-id' attributes on the dropdown containers BEFORE
        // the dropdowns are initialized.
        populateForm(videoData);

        // Now, initialize the dropdowns. They will use the 'data-initial-id'
        // to pre-select the correct value upon loading their options.
        await setupCustomDropdown(
            'category-dropdown',
            async () => getCategoryList({ limit: 100 }),
            { addNoneOption: false }
        );
        await setupCustomDropdown(
            'client-dropdown',
            async () => getClientList({ limit: 100 }),
            { addNoneOption: true }
        );

        // Finally, initialize the rest of the page logic (which is now lighter)
        await initializePage(videoData);

        // 设置页面清理函数
        setPageCleanup(() => {
            if (videoUploaderInstance && videoUploaderInstance.cleanup) {
                videoUploaderInstance.cleanup();
            }
            if (coverUploaderInstance && coverUploaderInstance.cleanup) {
                coverUploaderInstance.cleanup();
            }
        });

    } catch (error) {
        console.error('Failed to render edit video page:', error);
        const appContainer = document.getElementById('app-container');
        if (appContainer) {
            appContainer.innerHTML = `<div class="error-message">Error rendering page: ${error.message}</div>`;
        }
    }
};

// --- Translation Helper ---
const setupTranslation = () => {
    const translateButtons = document.querySelectorAll('.btn-translate');
    
    translateButtons.forEach(button => {
        button.addEventListener('click', async (e) => {
            const container = e.target.closest('.input-with-btn');
            if (!container) return;

            const sourceInput = container.querySelector('input');
            const targetInput = container.nextElementSibling;

            if (!sourceInput || !targetInput) return;

            const sourceText = sourceInput.value.trim();
            if (!sourceText) return;

            // Provide user feedback
            const originalButtonText = e.target.textContent;
            e.target.textContent = '...';
            e.target.disabled = true;

            try {
                const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(sourceText)}&langpair=zh-CN|en-GB`);
                const data = await response.json();
                
                if (data && data.responseData && data.responseData.translatedText) {
                    targetInput.value = data.responseData.translatedText;
                    // Trigger input event for form validation/dirty check
                    targetInput.dispatchEvent(new Event('input'));
                } else {
                    alert('Translation failed. Please try again.');
                }
            } catch (error) {
                console.error('Translation API error:', error);
                alert('Translation request failed. Check the console for details.');
            } finally {
                // Restore button
                e.target.textContent = originalButtonText;
                e.target.disabled = false;
            }
        });
    });
};

// --- New MLS ID Component Logic ---

// Debounce utility to limit API calls
const debounce = (func, delay) => {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
};

const setupMlsIdComponent = (initialIds = []) => {
    const container = document.getElementById('mls-id-container');
    const template = document.getElementById('mls-id-row-template');
    const addButton = document.getElementById('add-mls-id-btn');

    if (!container || !template || !addButton) {
        console.error("MLS ID component elements not found!");
        return;
    }
    
    // Clear any existing rows from the template
    container.innerHTML = '';

    const createMlsIdRow = (id = '') => {
        const clone = template.content.cloneNode(true);
        const row = clone.querySelector('.mls-id-row');
        const input = clone.querySelector('.mls-id-input');
        const dropdown = clone.querySelector('.search-results-dropdown');
        const resultsList = clone.querySelector('ul');
        const deleteBtn = clone.querySelector('.delete-mls-id-btn');

        input.value = id; // Set initial ID

        const closeDropdown = () => dropdown.classList.remove('open');
        
        const handleSearch = async (event) => {
            const keyword = event.target.value.trim();
            if (keyword.length < 2) { // Only search if 2+ characters
                closeDropdown();
                return;
            }

            // Note: `searchMlsProperties` needs to be imported if not already.
            const properties = await searchMlsProperties(keyword);
            resultsList.innerHTML = '';

            if (properties.length > 0) {
                properties.forEach(prop => {
                    const li = document.createElement('li');
                    li.dataset.id = prop.id;
                    li.innerHTML = `
                        <div class="result-addr">${prop.searchAddr}</div>
                        <div class="result-meta">${prop.id} &middot; ${prop.city}, ${prop.prov}</div>
                    `;
                    resultsList.appendChild(li);
                });
                dropdown.classList.add('open');
            } else {
                closeDropdown();
            }
        };

        input.addEventListener('input', debounce(handleSearch, 300));

        resultsList.addEventListener('click', (event) => {
            const li = event.target.closest('li');
            if (li) {
                input.value = li.dataset.id;
                closeDropdown();
            }
        });

        deleteBtn.addEventListener('click', () => {
            row.remove();
        });

        document.addEventListener('click', (event) => {
            if (!row.contains(event.target)) {
                closeDropdown();
            }
        });

        return row;
    };

    addButton.addEventListener('click', () => {
        const newRow = createMlsIdRow();
        container.appendChild(newRow);
    });

    // Initial rows based on provided IDs
    if (initialIds && initialIds.length > 0) {
        initialIds.forEach(id => {
            const newRow = createMlsIdRow(id);
            container.appendChild(newRow);
        });
    } else {
        // If no initial IDs, create one empty row to start with
        container.appendChild(createMlsIdRow());
    }
}; 