import { API_BASE_URL, authConfig } from '../config';

/**
 * JWT 管理器 - 处理 JWT 的获取、存储、刷新和验证
 */
class JWTManager {
  constructor() {
    this.jwt = localStorage.getItem(authConfig.tokenStorage.jwtKey);
    this.refreshToken = localStorage.getItem(authConfig.tokenStorage.refreshTokenKey);
    this.refreshTimer = null;
    this.isRefreshing = false;
    this.refreshPromise = null;
  }

  /**
   * 获取有效的 JWT token
   * @returns {Promise<string|null>} JWT token 或 null
   */
  async getValidJWT() {
    // 如果没有 JWT 或即将过期，获取新的
    if (!this.jwt || this.isExpiringSoon()) {
      await this.refreshJWT();
    }
    return this.jwt;
  }

  /**
   * 刷新 JWT token
   * @returns {Promise<boolean>} 是否成功刷新
   */
  async refreshJWT() {
    // 防止并发刷新
    if (this.isRefreshing) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this._performRefresh();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 执行实际的刷新操作
   * @private
   */
  async _performRefresh() {
    try {
      // 开发环境：使用开发专用接口获取长期 JWT
      if (import.meta.env?.DEV || window.location.hostname === 'localhost') {
        return await this._getDevJWT();
      }

      // 生产环境：从主 App 获取 JWT 或使用 refresh token 刷新
      const mainAppJWT = this._getJWTFromMainApp();
      if (mainAppJWT) {
        this._setTokens(mainAppJWT.jwt, mainAppJWT.refreshToken);
        return true;
      }

      // 使用 refresh token 刷新
      if (this.refreshToken) {
        return await this._refreshWithToken();
      }

      throw new Error('无法获取有效的 JWT');
    } catch (error) {
      console.error('Failed to refresh JWT:', error);
      this._clearTokens();
      return false;
    }
  }

  /**
   * 获取开发环境的 JWT（24小时有效期）
   * @private
   */
  async _getDevJWT() {
    try {
      // 检查是否指定了特定用户类型
      const userType = localStorage.getItem(authConfig.tokenStorage.userTypeKey) || authConfig.development.defaultUserType;

      // 直接使用开发专用接口获取 JWT
      const response = await fetch(`${API_BASE_URL}/dev/jwt?user=${userType}`);
      if (response.ok) {
        const data = await response.json();
        this._setTokens(data.jwt, data.refresh_token || 'dev_refresh');
        this._scheduleRefresh((data.expires_in - authConfig.refreshStrategy.refreshBeforeExpiry) * 1000); // 使用配置化的刷新提前时间
        console.log(`✅ 开发 JWT 获取成功 (用户类型: ${userType})`);
        return true;
      }

      // 如果开发接口失败，尝试使用 Mock 用户的 wk 获取 JWT
      const mockUsers = await this._getMockUsers();
      if (mockUsers && mockUsers.length > 0) {
        const firstUser = mockUsers[0];
        const jwtResponse = await this._convertWKToJWT(firstUser.wk);
        if (jwtResponse) {
          return true;
        }
      }

      throw new Error('无法获取开发 JWT');
    } catch (error) {
      console.error('获取开发 JWT 失败:', error);
      return false;
    }
  }

  /**
   * 从主 App 获取 JWT（生产环境）
   * @private
   */
  _getJWTFromMainApp() {
    try {
      // 方式1：从 URL 参数获取
      const urlParams = new URLSearchParams(window.location.search);
      const jwtFromUrl = urlParams.get('jwt');
      const refreshTokenFromUrl = urlParams.get('refresh_token');

      if (jwtFromUrl) {
        return {
          jwt: jwtFromUrl,
          refreshToken: refreshTokenFromUrl || 'main_app_refresh'
        };
      }

      // 方式2：从 postMessage 获取（iframe 通信）
      const jwtFromMessage = sessionStorage.getItem('main_app_jwt');
      const refreshFromMessage = sessionStorage.getItem('main_app_refresh_token');

      if (jwtFromMessage) {
        return {
          jwt: jwtFromMessage,
          refreshToken: refreshFromMessage || 'main_app_refresh'
        };
      }

      // 方式3：从全局变量获取（如果主 App 设置了）
      if (window.mainAppAuth && window.mainAppAuth.jwt) {
        return {
          jwt: window.mainAppAuth.jwt,
          refreshToken: window.mainAppAuth.refreshToken || 'main_app_refresh'
        };
      }

      return null;
    } catch (error) {
      console.error('从主 App 获取 JWT 失败:', error);
      return null;
    }
  }

  /**
   * 切换用户类型（开发环境）
   * @param {string} userType - 用户类型：admin, user, realtor, default
   */
  switchUser(userType) {
    localStorage.setItem('dev_user_type', userType);
    this._clearTokens();
    console.log(`🔄 切换到用户类型: ${userType}`);
    return this.refreshJWT();
  }

  /**
   * 获取 Mock 用户列表
   * @private
   */
  async _getMockUsers() {
    try {
      const response = await fetch(`${API_BASE_URL}/dev/mock/users`);
      if (response.ok) {
        const data = await response.json();
        return data.users;
      }
    } catch (error) {
      console.warn('无法获取 Mock 用户列表:', error);
    }
    return null;
  }

  /**
   * 使用 wk 转换为 JWT
   * @private
   */
  async _convertWKToJWT(wk) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/convertjwt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ wk }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.ok === 1) {
          this._setTokens(data.jwt, data.rft);
          this._scheduleRefresh((data.jwtexp - Date.now() / 1000 - 300) * 1000); // 提前5分钟刷新
          console.log('✅ JWT 转换成功');
          return data;
        }
      }
    } catch (error) {
      console.error('JWT 转换失败:', error);
    }
    return null;
  }

  /**
   * 使用 refresh token 刷新 JWT
   * @private
   */
  async _refreshWithToken() {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/refreshtoken`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rft: this.refreshToken,
          isweb: true,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.ok === 1) {
          this._setTokens(data.jwt, data.rft);
          this._scheduleRefresh((data.jwtexp - Date.now() / 1000 - 300) * 1000);
          console.log('✅ JWT 刷新成功');
          return true;
        }
      }

      throw new Error('刷新 token 失败');
    } catch (error) {
      console.error('使用 refresh token 刷新失败:', error);
      return false;
    }
  }

  /**
   * 检查 JWT 是否即将过期
   * @returns {boolean}
   */
  isExpiringSoon() {
    if (!this.jwt) return true;

    try {
      const payload = JSON.parse(atob(this.jwt.split('.')[1]));
      const expiresAt = payload.exp * 1000;
      const now = Date.now();
      // 如果还有不到 5 分钟就过期，认为需要刷新
      return (expiresAt - now) < (5 * 60 * 1000);
    } catch {
      return true;
    }
  }

  /**
   * 设置 tokens
   * @private
   */
  _setTokens(jwt, refreshToken) {
    this.jwt = jwt;
    this.refreshToken = refreshToken;
    localStorage.setItem(authConfig.tokenStorage.jwtKey, jwt);
    localStorage.setItem(authConfig.tokenStorage.refreshTokenKey, refreshToken);
  }

  /**
   * 清除 tokens
   * @private
   */
  _clearTokens() {
    this.jwt = null;
    this.refreshToken = null;
    localStorage.removeItem(authConfig.tokenStorage.jwtKey);
    localStorage.removeItem(authConfig.tokenStorage.refreshTokenKey);
  }

  /**
   * 安排自动刷新
   * @private
   */
  _scheduleRefresh(delayMs) {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    
    // 设置自动刷新时间（提前一些时间刷新）
    const refreshDelay = Math.max(delayMs - authConfig.refreshStrategy.refreshBeforeExpiry * 1000, authConfig.refreshStrategy.refreshRetryDelay); // 使用配置化的刷新延迟
    this.refreshTimer = setTimeout(() => {
      this.refreshJWT();
    }, refreshDelay);
  }

  /**
   * 获取用户信息（从 JWT 中解析）
   * @returns {Object|null}
   */
  getUserInfo() {
    if (!this.jwt) return null;

    try {
      const payload = JSON.parse(atob(this.jwt.split('.')[1]));
      return {
        userId: payload.sub,
        roles: payload.roles || [],
        expiresAt: new Date(payload.exp * 1000),
      };
    } catch {
      return null;
    }
  }

  /**
   * 手动清除所有 tokens（登出）
   */
  logout() {
    this._clearTokens();
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }
}

// 创建全局实例
const jwtManager = new JWTManager();

export default jwtManager;
