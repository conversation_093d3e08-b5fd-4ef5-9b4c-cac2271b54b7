import { baseConfig, uiConfig, developmentConfig } from '../config.js';
import ErrorHandler, { ErrorTypes, ErrorSeverity } from './errorHandler.js';

/**
 * 异步操作状态枚举
 */
export const AsyncStatus = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  CANCELLED: 'cancelled'
};

/**
 * 统一异步处理工具类
 */
export class AsyncManager {
  constructor() {
    this.activeRequests = new Map(); // 活跃的请求
    this.loadingStates = new Map(); // 加载状态
    this.retryQueues = new Map(); // 重试队列
  }

  /**
   * 执行异步操作并管理状态
   * @param {string} key - 操作唯一标识
   * @param {Function} asyncFn - 异步函数
   * @param {Object} options - 配置选项
   * @returns {Promise} 异步操作结果
   */
  async execute(key, asyncFn, options = {}) {
    const {
      timeout = baseConfig.requestTimeout,
      retries = baseConfig.maxRetries,
      retryDelay = 1000,
      showLoading = true,
      loadingText = '加载中...',
      onProgress = null,
      onStateChange = null,
      cancelPrevious = true
    } = options;

    // 取消之前的同名操作
    if (cancelPrevious && this.activeRequests.has(key)) {
      this.cancel(key);
    }

    // 创建可取消的Promise
    const { promise, cancel } = this.createCancellablePromise(asyncFn, timeout);
    this.activeRequests.set(key, { cancel, startTime: Date.now() });

    // 设置加载状态
    this.setLoadingState(key, AsyncStatus.LOADING, showLoading, loadingText);
    onStateChange?.(AsyncStatus.LOADING);

    try {
      const result = await promise;
      
      // 成功状态
      this.setLoadingState(key, AsyncStatus.SUCCESS, false);
      onStateChange?.(AsyncStatus.SUCCESS, result);
      this.activeRequests.delete(key);
      
      return result;
    } catch (error) {
      this.activeRequests.delete(key);
      
      if (error.name === 'AbortError' || error.message === 'Operation cancelled') {
        // 取消状态
        this.setLoadingState(key, AsyncStatus.CANCELLED, false);
        onStateChange?.(AsyncStatus.CANCELLED);
        throw error;
      }

      // 错误状态 - 尝试重试
      if (retries > 0 && this.shouldRetry(error)) {
        return this.executeWithRetry(key, asyncFn, { ...options, retries: retries - 1 });
      }

      // 最终失败
      this.setLoadingState(key, AsyncStatus.ERROR, false);
      onStateChange?.(AsyncStatus.ERROR, error);
      
      throw error;
    }
  }

  /**
   * 带重试的执行
   * @private
   */
  async executeWithRetry(key, asyncFn, options) {
    const { retryDelay = 1000 } = options;
    
    await this.delay(retryDelay);
    return this.execute(key, asyncFn, options);
  }

  /**
   * 创建可取消的Promise
   * @private
   */
  createCancellablePromise(asyncFn, timeout) {
    let cancelFn;
    let timeoutId;

    const promise = new Promise(async (resolve, reject) => {
      // 设置超时
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          reject(new Error(`Operation timed out after ${timeout}ms`));
        }, timeout);
      }

      // 取消函数
      cancelFn = () => {
        if (timeoutId) clearTimeout(timeoutId);
        reject(new Error('Operation cancelled'));
      };

      try {
        const result = await asyncFn();
        if (timeoutId) clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        if (timeoutId) clearTimeout(timeoutId);
        reject(error);
      }
    });

    return { promise, cancel: cancelFn };
  }

  /**
   * 取消异步操作
   * @param {string} key - 操作标识
   */
  cancel(key) {
    const request = this.activeRequests.get(key);
    if (request) {
      request.cancel();
      this.activeRequests.delete(key);
      this.setLoadingState(key, AsyncStatus.CANCELLED, false);
    }
  }

  /**
   * 取消所有活跃的异步操作
   */
  cancelAll() {
    for (const [key] of this.activeRequests) {
      this.cancel(key);
    }
  }

  /**
   * 设置加载状态
   * @private
   */
  setLoadingState(key, status, showLoading = false, loadingText = '') {
    this.loadingStates.set(key, { status, showLoading, loadingText, timestamp: Date.now() });
    
    if (showLoading) {
      this.updateLoadingUI(key, status, loadingText);
    }
  }

  /**
   * 更新加载UI
   * @private
   */
  updateLoadingUI(key, status, loadingText) {
    const loadingElement = document.querySelector(`[data-loading-key="${key}"]`);
    
    if (status === AsyncStatus.LOADING) {
      if (loadingElement) {
        loadingElement.style.display = 'flex';
        const textElement = loadingElement.querySelector('.loading-text');
        if (textElement) textElement.textContent = loadingText;
      } else {
        this.createLoadingElement(key, loadingText);
      }
    } else {
      if (loadingElement) {
        loadingElement.style.display = 'none';
      }
    }
  }

  /**
   * 创建加载元素
   * @private
   */
  createLoadingElement(key, loadingText) {
    const loadingElement = document.createElement('div');
    loadingElement.className = 'async-loading-overlay';
    loadingElement.setAttribute('data-loading-key', key);
    loadingElement.innerHTML = `
      <div class="async-loading-content">
        <div class="async-loading-spinner"></div>
        <div class="loading-text">${loadingText}</div>
      </div>
    `;
    
    document.body.appendChild(loadingElement);
    this.addLoadingStyles();
  }

  /**
   * 添加加载样式
   * @private
   */
  addLoadingStyles() {
    if (document.getElementById('async-loading-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'async-loading-styles';
    styles.textContent = `
      .async-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .async-loading-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .async-loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        color: #333;
        font-size: 14px;
      }
    `;
    document.head.appendChild(styles);
  }

  /**
   * 判断是否应该重试
   * @private
   */
  shouldRetry(error) {
    // 网络错误通常可以重试
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return true;
    }
    
    // 超时错误可以重试
    if (error.message.includes('timeout')) {
      return true;
    }
    
    // 5xx 服务器错误可以重试
    if (error.response && error.response.status >= 500) {
      return true;
    }
    
    // 429 限流错误可以重试
    if (error.response && error.response.status === 429) {
      return true;
    }
    
    return false;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取操作状态
   * @param {string} key - 操作标识
   */
  getStatus(key) {
    return this.loadingStates.get(key)?.status || AsyncStatus.IDLE;
  }

  /**
   * 检查是否正在加载
   * @param {string} key - 操作标识
   */
  isLoading(key) {
    return this.getStatus(key) === AsyncStatus.LOADING;
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.cancelAll();
    this.loadingStates.clear();
    this.retryQueues.clear();
    
    // 移除加载元素
    document.querySelectorAll('.async-loading-overlay').forEach(el => el.remove());
  }
}

// 创建全局实例
export const asyncManager = new AsyncManager();

/**
 * 并发控制工具
 */
export class ConcurrencyController {
  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }

  /**
   * 执行异步任务（带并发控制）
   * @param {Function} asyncFn - 异步函数
   * @returns {Promise} 执行结果
   */
  async execute(asyncFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ asyncFn, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * 处理队列
   * @private
   */
  async processQueue() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    this.running++;
    const { asyncFn, resolve, reject } = this.queue.shift();

    try {
      const result = await asyncFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.processQueue(); // 处理下一个任务
    }
  }
}

/**
 * 防抖函数 - 在事件停止触发后延迟执行
 * 适用场景：搜索输入、表单验证、窗口调整等
 * @param {Function} fn - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 *
 * @example
 * // 搜索防抖
 * const debouncedSearch = debounce((query) => {
 *   searchAPI(query);
 * }, 300);
 *
 * searchInput.addEventListener('input', (e) => {
 *   debouncedSearch(e.target.value);
 * });
 */
export function debounce(fn, delay = 300) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
}

/**
 * 立即执行防抖函数 - 第一次立即执行，后续防抖
 * 适用场景：按钮点击防抖、提交表单等
 * @param {Function} fn - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounceImmediate(fn, delay = 300) {
  let timeoutId;
  let isFirstCall = true;

  return function (...args) {
    if (isFirstCall) {
      fn.apply(this, args);
      isFirstCall = false;
    }

    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      isFirstCall = true;
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn - 要节流的函数
 * @param {number} delay - 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(fn, delay = 300) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn.apply(this, args);
    }
  };
}

/**
 * 带超时的Promise
 * @param {Promise} promise - 原始Promise
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} 带超时的Promise
 */
export function withTimeout(promise, timeout = baseConfig.requestTimeout) {
  return Promise.race([
    promise,
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
    )
  ]);
}

/**
 * 重试函数
 * @param {Function} fn - 要重试的异步函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟
 * @returns {Promise} 执行结果
 */
export async function retry(fn, maxRetries = baseConfig.maxRetries, delay = 1000) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw error;
      }
      
      // 指数退避
      const backoffDelay = delay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
    }
  }
  
  throw lastError;
}

/**
 * 批量执行异步操作
 * @param {Array} items - 要处理的项目数组
 * @param {Function} asyncFn - 异步处理函数
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 处理结果数组
 */
export async function batchProcess(items, asyncFn, options = {}) {
  const {
    batchSize = 5,
    onProgress = null,
    onBatchComplete = null,
    continueOnError = true
  } = options;

  const results = [];
  const errors = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchPromises = batch.map(async (item, index) => {
      try {
        const result = await asyncFn(item, i + index);
        return { success: true, result, item };
      } catch (error) {
        const errorInfo = { success: false, error, item };
        if (!continueOnError) {
          throw error;
        }
        return errorInfo;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    
    batchResults.forEach(result => {
      if (result.success) {
        results.push(result.result);
      } else {
        errors.push(result);
      }
    });

    // 进度回调
    onProgress?.(i + batch.length, items.length);
    onBatchComplete?.(batchResults, i / batchSize + 1);
  }

  return { results, errors };
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  asyncManager.cleanup();
});

export default {
  AsyncManager,
  asyncManager,
  ConcurrencyController,
  AsyncStatus,
  debounce,
  throttle,
  withTimeout,
  retry,
  batchProcess
};
