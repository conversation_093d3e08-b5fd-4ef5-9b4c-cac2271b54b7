import { initiateChunkedUpload, uploadChunk, completeChunkedUpload } from '../services/apiClient.js';
import { uploadConfig } from '../config.js';

/**
 * 分块上传工具类
 * 处理大文件的分块上传逻辑
 */
export class ChunkedUploader {
  constructor(file, options = {}) {
    this.file = file;
    this.chunkSize = options.chunkSize || uploadConfig.chunkedUpload.chunkSize; // 使用配置化的分块大小
    this.maxRetries = options.maxRetries || uploadConfig.chunkedUpload.maxRetries; // 使用配置化的重试次数
    this.onProgress = options.onProgress || (() => {}); // 进度回调
    this.onChunkProgress = options.onChunkProgress || (() => {}); // 单个分块进度回调
    this.onStateChange = options.onStateChange || (() => {}); // 状态变化回调

    this.totalChunks = Math.ceil(file.size / this.chunkSize);
    this.uploadedChunks = 0;
    this.uploadId = null;
    this.isUploading = false;
    this.isPaused = false;
    this.isCompleted = false;
    this.uploadedBytes = 0;

    // 失败的分块队列
    this.failedChunks = new Set();
  }

  /**
   * 开始分块上传
   * @returns {Promise<Object>} 上传完成后的结果，包含goupload路径
   */
  async upload() {
    if (this.isUploading) {
      throw new Error('Upload is already in progress');
    }

    this.isUploading = true;
    this.isPaused = false;

    try {
      // 1. 初始化分块上传
      const initResult = await initiateChunkedUpload(
        this.file.name,
        this.file.size,
        "video"  // 指定文件类型为视频
      );

      this.uploadId = initResult.uploadId;
      console.log(`分块上传初始化成功，uploadId: ${this.uploadId}`);

      // 2. 上传所有分块
      await this.uploadAllChunks();

      // 3. 完成分块上传
      const completeResult = await completeChunkedUpload(this.uploadId, this.totalChunks);

      this.isCompleted = true;
      this.isUploading = false;

      console.log('分块上传完成:', completeResult);
      return completeResult;

    } catch (error) {
      this.isUploading = false;
      console.error('分块上传失败:', error);
      throw error;
    }
  }

  /**
   * 上传所有分块
   */
  async uploadAllChunks() {
    const uploadPromises = [];
    
    for (let chunkNumber = 1; chunkNumber <= this.totalChunks; chunkNumber++) {
      if (this.isPaused) {
        throw new Error('Upload was paused');
      }

      const start = (chunkNumber - 1) * this.chunkSize;
      const end = Math.min(start + this.chunkSize, this.file.size);
      const chunk = this.file.slice(start, end);

      // 创建上传Promise
      const uploadPromise = this.uploadChunkWithRetry(chunkNumber, chunk);
      uploadPromises.push(uploadPromise);
    }

    // 等待所有分块上传完成
    await Promise.all(uploadPromises);
  }

  /**
   * 带重试机制的分块上传
   * @param {number} chunkNumber - 分块编号
   * @param {Blob} chunk - 分块数据
   * @returns {Promise<void>}
   */
  async uploadChunkWithRetry(chunkNumber, chunk) {
    let retries = 0;
    
    while (retries < this.maxRetries) {
      try {
        await this.uploadSingleChunk(chunkNumber, chunk);
        return; // 成功上传，退出重试循环
      } catch (error) {
        retries++;
        console.warn(`分块 ${chunkNumber} 上传失败 (重试 ${retries}/${this.maxRetries}):`, error);
        
        if (retries >= this.maxRetries) {
          throw new Error(`分块 ${chunkNumber} 上传失败，已达到最大重试次数: ${error.message}`);
        }
        
        // 等待一段时间后重试
        await this.delay(uploadConfig.chunkedUpload.retryDelay * retries);
      }
    }
  }

  /**
   * 上传单个分块
   * @param {number} chunkNumber - 分块编号
   * @param {Blob} chunk - 分块数据
   */
  async uploadSingleChunk(chunkNumber, chunk) {
    const onChunkProgress = (progressEvent) => {
      // 计算当前分块的上传进度
      const chunkProgress = (progressEvent.loaded / progressEvent.total) * 100;
      this.onChunkProgress(chunkNumber, chunkProgress);
      
      // 计算总体进度
      const completedBytes = (this.uploadedChunks * this.chunkSize) + progressEvent.loaded;
      const totalProgress = (completedBytes / this.file.size) * 100;
      this.uploadedBytes = completedBytes;
      this.onProgress(totalProgress, completedBytes, this.file.size);
    };

    await uploadChunk(this.uploadId, chunkNumber, chunk, onChunkProgress);
    
    this.uploadedChunks++;
    console.log(`分块 ${chunkNumber}/${this.totalChunks} 上传完成`);
  }

  /**
   * 暂停上传
   */
  pause() {
    this.isPaused = true;
  }

  /**
   * 恢复上传
   */
  resume() {
    this.isPaused = false;
  }

  /**
   * 取消上传
   */
  cancel() {
    this.isPaused = true;
    this.isUploading = false;
    // TODO: 可以添加取消上传的API调用
  }

  /**
   * 获取上传进度信息
   * @returns {Object} 进度信息
   */
  getProgress() {
    return {
      uploadedChunks: this.uploadedChunks,
      totalChunks: this.totalChunks,
      uploadedBytes: this.uploadedBytes,
      totalBytes: this.file.size,
      percentage: (this.uploadedBytes / this.file.size) * 100,
      isCompleted: this.isCompleted,
      isUploading: this.isUploading,
      isPaused: this.isPaused
    };
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 静态方法：检查文件是否需要分块上传
   * @param {File} file - 文件对象
   * @param {number} threshold - 阈值，使用配置化的阈值
   * @returns {boolean} 是否需要分块上传
   */
  static shouldUseChunkedUpload(file, threshold = uploadConfig.chunkedUpload.threshold) {
    return file.size > threshold;
  }
}

export default ChunkedUploader;
