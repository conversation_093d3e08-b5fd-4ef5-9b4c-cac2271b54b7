/* Reset all elements */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Document */
html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

/* Body */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Lists */
ul,
ol {
  list-style: none;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Forms */
input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
}

/* Links */
a {
  color: inherit;
  text-decoration: none;
}

/* Tables */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove animations for people who've turned them off */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
