// Colors
$primary-color: #007bff;
$secondary-color: #6c757d;
$success-color: #28a745;
$error-color: #dc3545;
$primary-color-red: #EE0011;
$background-color: #ffffff;
$text-color: #333333;

// CSS Variables
:root {
  // Background colors
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-tertiary: #e9ecef;

  // Text colors
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;

  // Border colors
  --border-color: #dee2e6;

  // Primary colors
  --primary-color: #{$primary-color};
  --primary-color-light: lighten($primary-color, 10%);
  --primary-color-dark: darken($primary-color, 10%);
  --primary-color-red: #{$primary-color-red};
}

// Typography
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 16px;
$line-height-base: 1.5;

// Spacing
$spacing-unit: 8px;
$spacing-small: $spacing-unit;
$spacing-medium: $spacing-unit * 2;
$spacing-large: $spacing-unit * 3;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px; 