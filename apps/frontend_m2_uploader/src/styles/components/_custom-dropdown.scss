.custom-dropdown {
    position: relative;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
    cursor: pointer;
    user-select: none;
    
    .dropdown-selected {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem;
        
        .selected-value {
            color: #495057;
        }

        .dropdown-arrow {
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #343a40;
            transition: transform 0.2s ease-in-out;
        }
    }
    
    .dropdown-options {
        display: none; // Hidden by default
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        max-height: 200px;
        overflow-y: auto;
        z-index: 100;
    }
    
    &.open .dropdown-options {
        display: block;
    }

    &.open .dropdown-arrow {
        transform: rotate(180deg);
    }

    .options-list {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
            padding: 0.75rem 1rem;
            cursor: pointer;
            
            &:hover {
                background-color: #f1f3f5;
            }

            &.selected {
                font-weight: 600;
                background-color: #e9ecef;
            }
        }
    }
} 