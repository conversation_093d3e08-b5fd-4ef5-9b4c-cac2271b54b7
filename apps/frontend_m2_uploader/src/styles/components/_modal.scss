/* 
  Shared styles for modal overlays and content blocks.
  This includes the client modal and the fullscreen image preview modal.
*/

// Generic modal overlay
.modal-overlay {
  &.hidden {
    display: none !important;
  }
}

// Client Add/Edit Modal
#client-modal {
  .modal-content {
    width: 1000px;
    max-width: 800px;
    text-align: left;
  }
  
  .modal-title {
    text-align: center;
    margin-bottom: 2rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    &:has(.image-uploader) {
      align-items: center;
    }
  }

  .full-width-form-group {
    grid-column: 1 / -1;
    margin-bottom: 2rem;
  }

  label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
  }

  .required {
    color: #ee0011;
    margin-left: 0.25rem;
  }

  input,
  textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    font-size: 1rem;
    background-color: #f8f9fa;
  }

  textarea {
    resize: vertical;
    min-height: 80px;
  }

  .image-uploader {
    margin-top: 0.5rem;
    position: relative;
    width: 120px;
    height: 120px;
    .upload-area {
      width: 100%;
      height: 100%;
      border: 2px dashed #d9d9d9;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;
      &:hover {
        border-color: #ee0011;
      }
      .preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
      }
      &.has-image:hover {
        #avatar-preview {
          filter: brightness(70%);
        }
        .preview-overlay {
          opacity: 1;
        }
      }
      #avatar-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 50%;
        &.hidden {
          display: none;
        }
      }
      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #888;
        img {
          width: 32px;
          height: 32px;
          margin-bottom: 8px;
        }
      }
      &.has-image .upload-placeholder {
        display: none;
      }
    }
    #remove-avatar-btn {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #fff;
      border: 1px solid #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      padding: 0;
      &:hover {
        background-color: #f5f5f5;
        transform: scale(1.1);
      }
      img {
        width: 16px;
        height: 16px;
      }
      &.hidden {
        display: none;
      }
    }
  }
}

/* Fullscreen Image Preview Modal */
#image-preview-modal {
    display: flex;
    justify-content: center;
    align-items: center;
    .modal-content {
        max-width: 90vw;
        max-height: 90vh;
        width: auto;
        height: auto;
        border-radius: 8px;
    }
    .close-btn {
        position: absolute;
        top: 20px;
        right: 35px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.3s;
        &:hover {
            color: #ccc;
        }
    }
} 