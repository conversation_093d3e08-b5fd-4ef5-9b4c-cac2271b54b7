// Base styles
@use 'base/reset';
@use 'base/variables' as vars;

// Layout - Now handled by individual pages

// Components
// @use 'components/buttons';
// @use 'components/forms';

// Pages
@use 'pages/dashboard';
@use 'pages/category-manager';
@use 'pages/filter';
@use 'pages/clients';
@use 'pages/upload';
@use 'pages/edit-video';

body {
  font-family: vars.$font-family-base;
  background-color: #F5F5F5;
  margin: 0;
}

// The #app-container will be the main viewport
#app-container {
  width: 100%;
  height: 100vh;
  overflow: hidden; // Pages inside will manage their own scrolling
} 

.hidden {
  display: none;
}