@use '../base/variables' as vars;
@use '../components/modal';
@use '../components/custom-dropdown';

// Inherit page layout styles by referencing category manager styles.
// This is a temporary approach to avoid duplication.
// A better long-term solution would be a shared layout component/style.
@use 'category-manager';

.clients-page {
    // Most styles are inherited from .category-manager-page via @import

    .client-list-container {
      flex-grow: 1;
      overflow-y: auto;
      overflow-x: hidden; // 防止水平滚动
      min-height: 0;
      max-height: 100%; // 确保不超过父容器

      // 自定义滚动条样式（Webkit浏览器）
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }

      // 确保滚动条始终可见（调试用）
      scrollbar-width: thin; // Firefox
      scrollbar-color: #c1c1c1 #f1f1f1; // Firefox
    }

    .client-table {
        width: 100%;
        border-collapse: collapse;
        
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        thead th {
            font-weight: 600;
            font-size: 0.875rem;
            color: #495057;
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
        }

        tbody tr {
            &:last-child {
                td {
                    border-bottom: none;
                }
            }
        }
        
        .profile-picture {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .memo-cell {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .item-actions {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 1rem;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;

            img {
                width: 20px;
                height: 20px;
                opacity: 0.5;
            }

             &:hover img {
                opacity: 1;
            }
        }
    }
}

/* Styles for Fullscreen Image Preview Modal */
#image-preview-modal {
    display: flex;
    justify-content: center;
    align-items: center;
    
    &.hidden {
        display: none !important;
    }

    .modal-content {
        max-width: 90vw;
        max-height: 90vh;
        width: auto;
        height: auto;
        border-radius: 8px;
    }

    .close-btn {
        position: absolute;
        top: 20px;
        right: 35px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.3s;
        
        &:hover {
            color: #ccc;
        }
    }
}

/* Styles for Merge Client Modal */
#merge-client-modal {
    .modal-content {
        /* This will inherit width/max-width from _category-manager's .modal-content */
        text-align: left;
        padding: 2rem;
    }

    .modal-title {
        text-align: center;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
    }
    
    p {
        margin-bottom: 1.5rem;
        line-height: 1.6;
        color: #495057;
        font-size: 0.95rem;
    }

    .form-group {
        margin-bottom: 2rem;
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
    }
    
    .custom-select {
        width: 100%;
        padding: 0.8rem 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        font-size: 1rem;
        background-color: #f8f9fa;
        -webkit-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 1em;
    }
    
    .modal-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .btn-danger {
 
        background-color: #E03131;
        color: white;
        border: 1px solid #E03131;
        border-radius: 0.375rem; // 添加圆角
        cursor: pointer;
        flex-grow: 1;
        max-width: 200px; // 与其他模态框按钮保持一致

        &:hover {
            background-color: #E03131;
            border-color: #E03131;
        }
    }
}

// 北美电话号码输入组件样式
.phone-input-container {
    display: flex;
    align-items: stretch;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: white;
    position: relative; // 确保定位上下文

    // 内部元素样式
    #client-phone {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    &:focus-within {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
}

.country-selector {
    position: relative;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    min-width: 100px;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    display: flex; // 使用flex确保高度一致
    align-items: stretch; // 拉伸子元素填满高度

    .selected-country {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 0.5rem; // 保持与输入框相同的垂直padding
        cursor: pointer;
        user-select: none;
        gap: 0.5rem;
        transition: background-color 0.15s ease-in-out;
        height: 100%; // 确保填满父容器高度
        width: 100px;
        box-sizing: border-box; // 确保padding计算正确

        &:hover {
            background-color: #e9ecef;
        }

        .flag-icon {
            width: 20px;
            height: 15px;
            object-fit: cover;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .country-code {
            font-weight: 500;
            color: #495057;
            font-size: 0.9rem;
        }

        .dropdown-arrow {
            width: 20px;
            height: 20px;
            color: #6c757d;
            transition: transform 0.15s ease-in-out;
        }

        &.open .dropdown-arrow {
            transform: rotate(180deg);
        }
    }

    .country-dropdown {
        position: absolute;
        top: calc(100% + 1px);
        left: -1px;
        right: -1px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: none;
        max-height: 120px; // 减小最大高度
        overflow-y: auto;
        width: 100px; // 固定较小的宽度

        &.open {
            display: block !important;
        }

        .country-option {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem; // 减小内边距
            cursor: pointer;
            gap: 0.5rem; // 减小间距
            transition: background-color 0.15s ease-in-out;

            &:hover {
                background-color: #f8f9fa;
            }

            &:not(:last-child) {
                border-bottom: 1px solid #f1f3f4;
            }

            .flag-icon {
                width: 20px; // 稍微减小国旗尺寸
                height: 15px;
                object-fit: cover;
                border-radius: 2px;
                flex-shrink: 0;
            }

            .country-code {
                color: #6c757d;
                font-size: 0.85rem; // 稍微减小字体
                font-weight: 500;
            }
        }
    }
}

#client-phone {
    flex: 1;
    border: none;
    padding: 0.75rem 1rem; // 保持与选择器相同的垂直padding
    font-size: 1rem;
    outline: none;
    background: transparent;
    height: 100%; // 确保填满父容器高度
    box-sizing: border-box; // 确保padding计算正确

    &::placeholder {
        color: #6c757d;
    }

    &:focus {
        outline: none;
    }
}

// 表单验证错误状态
.phone-input-container.error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

// 通用输入框错误状态
input.error,
textarea.error,
select.error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;

    &:focus {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
}

// 头像上传器错误状态
#avatar-uploader.error {
    border-color: #dc3545 !important;

    .upload-area {
        border-color: #dc3545 !important;
        background-color: rgba(220, 53, 69, 0.05);
    }
}

// 错误消息样式
.field-error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

// 响应式设计
@media (max-width: 768px) {
    .phone-input-container {
        flex-direction: column;

        .country-selector {
            border-right: none;
            border-bottom: 1px solid #dee2e6;
            min-width: auto;

            .selected-country {
                justify-content: center;
                padding: 0.5rem;
            }
        }

        #client-phone {
            padding: 0.75rem;
        }
    }
}

// 响应式设计 - 客户页面特定优化
@media (max-width: 768px) {
    .client-list-container {
        // 在小屏幕上确保表格可以水平滚动
        overflow-x: auto;

        .client-table {
            min-width: 600px; // 确保表格有最小宽度

            th, td {
                padding: 0.75rem 0.5rem; // 减小内边距节省空间
                font-size: 0.875rem; // 稍微减小字体
            }

            .profile-picture {
                width: 32px;
                height: 32px;
            }
        }
    }
}

@media (max-width: 480px) {
    .client-list-container {
        .client-table {
            th, td {
                padding: 0.5rem 0.25rem; // 进一步减小内边距
                font-size: 0.8rem;
            }
        }
    }
}