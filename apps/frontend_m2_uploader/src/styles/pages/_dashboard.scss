// Variables for layout
$header-height: 3rem; // 64px
$analytics-height: 4.5rem; // 96px, 1.5x header height
$controls-height: 4.5rem; // 96px, same as analytics

.dashboard-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  // The background color is now set globally on the body
}

.dashboard-header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0; // Prevent header from shrinking
  width: 100%;
  height: $header-height;
  background-color: #ee0011;
  
  .header-title {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.dashboard-analytics {
  flex-shrink: 0;
  width: 100%;
  height: $analytics-height;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around; // Evenly space items
  padding: 0 1rem; // Add some horizontal padding

  .analytics-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .analytics-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ee0011; // Red color for the value
    line-height: 1.1; // Tighter line-height
  }

  .analytics-label {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.15rem; // Precise small margin
  }

  .analytics-filter-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    text-decoration: none;
    color: inherit;
    cursor: pointer;

    img {
      width: 1.5rem; // Control icon size with rem
      height: 1.5rem;
    }
  }
}

.dashboard-controls {
  flex-shrink: 0;
  width: 100%;
  height: $controls-height;
  background-color: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;

  .controls-left {
    display: flex;
    gap: 1rem;
  }

  .control-btn, .control-btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    img {
      width: 1.25rem;
      height: 1.25rem;
    }
  }

  .control-btn {
    background-color: #ffffff;
    color: #333;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    &.dev-tools-btn {
      background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
      border: 1px solid #ffc107;
      color: #856404;

      &:hover {
        background: linear-gradient(135deg, #ffeaa7 0%, #f8f9fa 100%);
        border-color: #ffb300;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
      }
    }
  }

  .control-btn-primary {
    background-color: #ee0011;
    color: #ffffff;

    &:hover {
      background-color: #c5000f;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
  }
}

.video-list-container {
  flex-grow: 1; // Take up remaining space
  overflow-y: auto; // Enable vertical scrolling
  padding: 1rem;
  background-color: transparent;

  .video-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

// --- Video Item Card (New Design) ---
.video-item {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  }

  .video-cover {
    width: 160px;
    height: 90px;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-icon-overlay {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.3);
      color: white;
      opacity: 0;
      transition: opacity 0.2s;
      
      svg {
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
      }
    }
    
    &:hover .play-icon-overlay {
      opacity: 1;
    }

    .duration-badge {
      position: absolute;
      bottom: 0.5rem;
      right: 0.5rem;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 0.125rem 0.375rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
    }
  }

  .video-details {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 0; // Prevents text overflow issues in flexbox

    .video-title-text {
      font-size: 1.1rem;
      font-weight: 600;
      color: #111;
      margin: 0;
      line-height: 1.3;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .video-meta-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .uploader-info, .video-stats {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #666;
      }

      .uploader-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .stat-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }

      .stat-item {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
      }

      .publish-date {
        margin-left: 1.5rem; // Pushed to the right
      }
    }
  }

  .video-status-container {
    flex-basis: 25%; // Occupies a flexible quarter of space
    display: flex;
    justify-content: flex-start; // Aligns pill to the left of its container
    padding-left: 2%; // Nudge it a bit from the hard 75% line
  }

  .status-pill {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px; // Pill shape
    font-size: 0.875rem;
    font-weight: 500;
    flex-shrink: 0; // Prevent shrinking
    white-space: nowrap;
  }

  .video-actions {
    display: flex;
    gap: 0.5rem;

    .action-btn {
      background: none;
      border: 1px solid #e0e0e0;
      border-radius: 0.375rem;
      padding: 0.5rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #f5f5f5;
      }

      img {
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Status colors
.status-draft { background-color: #f0f0f0; color: #888888; border: 1px solid #888888; }
.status-unpublished { background-color: #f0f0f0; color: #4A4A4A; border: 1px solid #4A4A4A; }
.status-processing { background-color: #fdf6ec; color: #E6A23C; border: 1px solid #E6A23C; }
.status-published { background-color: #f0f9eb; color: #67C23A; border: 1px solid #67C23A; }
.status-failed { background-color: #fef0f0; color: #FF4D4F; border: 1px solid #FF4D4F; }

// --- Responsive adjustments ---
@media (max-width: 768px) {
    .video-item {
        flex-direction: column;
        align-items: stretch;
    }
    .video-details {
        // Adjust info layout for vertical stacking
    }
    .video-actions {
        margin-left: 0;
        margin-top: 0.5rem;
        justify-content: space-between;
    }
}

// --- Responsive adjustments (if any) ---
// The horizontal card layout is quite responsive by default, but we can add adjustments.
@media (max-width: 480px) {
  .video-item {
    flex-direction: column;

    .video-cover {
      width: 100%;
      height: auto;
      aspect-ratio: 16 / 9;
    }
  }
}

// --- Responsive adjustments ---

// For tablets and larger screens
@media (min-width: 768px) {
  .dashboard-header {
    padding: 1.5rem 2rem;
    .create-new-btn {
      span {
        display: inline; // Show text on larger screens
      }
    }
  }

  .dashboard-content {
    padding: 1.5rem 2rem;
  }

  .video-list {
    grid-template-columns: repeat(2, 1fr); // Two columns
  }
}

// For desktops and larger screens
@media (min-width: 1024px) {
  .video-list {
    grid-template-columns: repeat(3, 1fr); // Three columns
  }
}

// For very large screens
@media (min-width: 1440px) {
  .video-list {
    grid-template-columns: repeat(4, 1fr); // Four columns
  }
}

/* New style to hide elements while preserving layout */
.video-actions.is-hidden {
    visibility: hidden;
}

.video-player-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    &.hidden {
        display: none;
    }

    .modal-content {
        position: relative;
        background-color: #000;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 960px;
    }

    .close-btn {
        position: absolute;
        top: -10px;
        right: -10px;
        background: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        font-size: 20px;
        font-weight: bold;
        color: #333;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        padding: 0;
        z-index: 1001;
    }

    video {
        width: 100%;
        height: auto;
        max-height: 80vh;
        display: block;
    }
}

.load-more-container {
    text-align: center;
    padding: 20px 0;
}

.load-more-btn {
    padding: 12px 25px;
    font-size: 1rem;
    font-weight: bold;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s, box-shadow 0.2s;

    &:hover {
        background-color: #f7f7f7;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
        background-color: #eee;
    }
} 