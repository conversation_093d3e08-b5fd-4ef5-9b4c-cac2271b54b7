// _edit-video.scss
// The edit video page shares the exact same layout and components as the upload page.
// Instead of duplicating styles, we can just import the upload page's styles directly.

@use 'upload';

.back-btn {
    margin-right: 20px;
}

.status-container {
    margin-left: 20px;
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    // Default colors
    background-color: #e0e0e0;
    color: #333;

    &.status-draft {
        background-color: #f0ad4e; // Orange
        color: white;
    }
    &.status-published {
        background-color: #5cb85c; // Green
        color: white;
    }
    &.status-pending,
    &.status-processing {
        background-color: #337ab7; // Blue
        color: white;
    }
    &.status-unpublished {
        background-color: #777; // Gray
        color: white;
    }
    &.status-processing-failed {
        background-color: #d9534f; // Red
        color: white;
    }
}