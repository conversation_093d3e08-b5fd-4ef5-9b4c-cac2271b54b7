// Styles for the Filter page
.filter-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f8f9fa;

    .page-header {
        // Assuming common page-header styles are defined elsewhere
        // Specifics for filter page can be added here
        .filter-count-badge {
            background-color: var(--primary-color-red);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }
    }

    .filter-options-container {
        flex-grow: 1;
        overflow-y: auto;
        padding: 16px;
    }

    .filter-group {
        margin-bottom: 24px;
        position: relative; // For positioning the dropdown body

        .filter-label, .filter-label-container {
            display: block;
            margin-bottom: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            color: #212529;
        }

        .filter-label-container {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .btn-new {
                background: none;
                border: none;
                color: var(--primary-color-red);
                font-size: 1.5rem;
                cursor: pointer;
            }
        }

        .filter-select-box {
            display: flex;
            align-items: center;
            background-color: white;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            cursor: pointer;

            .selected-value-display {
                flex-grow: 1;
                color: #212529;
            }

            .arrow-icon {
                cursor: pointer;
                transition: transform 0.2s ease-in-out;
            }

            .arrow-icon.is-rotated {
                transform: rotate(180deg);
            }
        }

        .filter-group-body {
            display: block;
            background-color: white;
            border-radius: 8px;
            margin-top: 4px;
            border: 1px solid #dee2e6;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        &.open {
            .filter-group-body {
                max-height: 300px; // Set a sufficient max-height for the content
            }
            .arrow-icon {
                transform: rotate(180deg);
            }
        }
    }

    .date-range-inputs {
        display: flex;
        gap: 16px;

        .date-input-container {
            flex: 1;

            .date-input-label {
                display: block;
                font-size: 0.8rem;
                color: #868e96;
                margin-bottom: 4px;
            }

            .date-input-wrapper {
                position: relative;
                display: flex;
                align-items: center;

                input[type="text"] {
                    width: 100%;
                    padding: 12px 36px 12px 12px;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    background-color: white;
                    cursor: default;
                }

                .arrow-icon {
                    position: absolute;
                    right: 12px;
                    cursor: pointer;
                    transition: transform 0.2s ease-in-out;
                }
                
                .arrow-icon.is-rotated {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .options-list {
        list-style: none;
        padding: 8px;
        margin: 0;
        max-height: 250px;
        overflow-y: auto;

        li {
            padding: 12px;
            border-radius: 6px;

            &:hover {
                background-color: #f8f9fa;
            }

            label {
                display: flex;
                align-items: center;
                cursor: pointer;
                width: 100%;
                
                input[type="radio"], input[type="checkbox"] {
                    margin-right: 12px;
                }
            }
        }
    }

    .filter-footer {
        display: flex;
        padding: 16px;
        background-color: white;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.05);
        gap: 8px;

        .btn-secondary, .btn-primary {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
        }

        .btn-secondary {
            background-color: #e9ecef;
            color: #495057;
        }

        .btn-primary {
            background-color: var(--primary-color-red);
            color: white;
        }
    }
} 