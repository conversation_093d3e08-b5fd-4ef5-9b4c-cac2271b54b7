@use '../components/modal';
@use '../components/custom-dropdown';
// Inherit page layout styles by referencing category manager styles.
@use 'category-manager';

// Styles for the Upload page
.upload-page {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .upload-form-container {
        flex-grow: 1;
        overflow-y: auto;
        background-color: white;
        padding: 2rem;
        min-height: 0;
    }

    .upload-form {
        max-width: 900px;
        margin: 0 auto;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem 2rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        
        &.full-span {
            grid-column: 1 / -1;
        }

        label {
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .required {
            color: #ee0011;
            margin-left: 0.25rem;
        }

        input, select {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            background-color: #f8f9fa;
        }
        
        select {
             -webkit-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1em;
        }
        
        .input-secondary {
            margin-top: 0.75rem;
        }

        .input-with-btn {
            display: flex;
            gap: 0.5rem;
            
            input {
                flex-grow: 1;
            }

            .btn-translate {
                padding: 0 1.5rem;
                background-color: #e9ecef;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                cursor: pointer;
            }
        }
    }
    
    .label-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .btn-new {
        background-color: #ee0011;
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        cursor: pointer;
        white-space: nowrap;
        font-size: 0.875rem;
        min-width: 140px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
    }
    
    .input-with-btn-right {
        display: flex;
        width: 100%;
        
        input {
            flex-grow: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
        }
        .delete-icon-btn {
            border: 1px solid #dee2e6;
            border-left: none;
            background-color: #f8f9fa;
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
            padding: 0 1rem;
        }
    }

    .file-uploader {
        position: relative; // For positioning the hidden input
        display: flex;
        align-items: center;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        min-height: 80px;

        .file-input {
            display: none;
        }

        .uploader-box {
            width: 50px;
            height: 50px;
            border: 2px dashed #adb5bd;
            border-radius: 0.25rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2rem;
            font-weight: 300;
            color: #adb5bd;
            cursor: pointer;
            flex-shrink: 0;
            transition: background-color 0.2s;

            &:hover {
                background-color: #e9ecef;
            }

            &.hidden {
                display: none;
            }
        }

        .file-details {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: 1rem;
            flex-grow: 1;

            &.hidden {
                display: none;
            }

            .image-preview {
                width: 50px;
                height: 50px;
                object-fit: cover;
                border-radius: 0.25rem;
            }

            .video-default-icon {
                width: 50px;
                height: 50px;
                flex-shrink: 0;
                display: none; // Hidden by default, shown when file is selected
            }

            .file-name {
                font-size: 0.9rem;
                color: #495057;
            }
        }


        .file-uploader-controls {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 1rem;
            padding-left: 1rem;

            .preview-btn {
                background: none;
                border: none;
                color: #868e96;
                font-size: 0.875rem;
                cursor: pointer;
                
                &:disabled {
                    color: #ced4da;
                    cursor: not-allowed;
                }
            }

            .delete-btn {
                background: none;
                border: none;
                cursor: pointer;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 20px;
                    height: 20px;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                }

                &:hover img {
                    opacity: 1;
                }
            }
        }
    }

    .mls-id-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        width: 100%;
        margin-bottom: 0.75rem;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .searchable-input-container {
        position: relative;
        flex-grow: 1;

        .mls-id-input {
            width: 100%;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            background-color: #f8f9fa;

            &:read-only {
                background-color: #e9ecef;
                cursor: default;
            }
        }

        .search-results-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 250px;
            overflow-y: auto;
            z-index: 100;

            &.open {
                display: block;
            }

            ul {
                list-style: none;
                margin: 0;
                padding: 0;

                li {
                    padding: 0.75rem 1rem;
                    cursor: pointer;
                    border-bottom: 1px solid #f1f3f5;

                    &:last-child {
                        border-bottom: none;
                    }
                    
                    &:hover {
                        background-color: #f1f3f5;
                    }

                    .result-addr {
                        font-weight: 500;
                        color: #212529;
                    }

                    .result-meta {
                        font-size: 0.875rem;
                        color: #6c757d;
                        margin-top: 0.25rem;
                    }
                }
            }
        }
    }
    
    .delete-mls-id-btn {
        flex-shrink: 0;
        border: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 1.25rem;
            height: 1.25rem;
        }
    }

    .upload-footer {
        display: flex;
        flex-shrink: 0;
        
        button {
            flex: 1;
            border: none;
            padding: 0.8rem 0;
            font-size: 1.125rem;
            font-weight: 500;
            cursor: pointer;
            color: white;
            text-transform: uppercase;
        }

        .btn-draft {
            background-color: #4CAF50; // Green
            
            &:disabled {
                background-color: #ced4da;
                cursor: not-allowed;
            }
        }

        .btn-publish {
            background-color: #ee0011; // Red
            
            &:disabled {
                background-color: #ced4da;
                cursor: not-allowed;
            }
        }
    }
    
    // Specific styles for the video/image preview modal
    #preview-modal {
        .modal-content {
            position: relative; // For positioning the close button
            max-width: 80vw;
            max-height: 80vh;
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }

        .close-modal-btn {
            position: absolute;
            top: 0.3rem; // 10px
            right: 0.9375rem; // 15px
            font-size: 2rem;
            color: #6c757d;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            line-height: 1;
            z-index: 10; // Ensure it's on top of content
        }

        #preview-content {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 1rem;

            img, video {
                max-width: 100%;
                max-height: calc(80vh - 60px); // a bit of headroom
                border-radius: 0.25rem;
            }
        }
    }
} 