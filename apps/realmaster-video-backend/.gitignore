# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go build
*.out

# Go workspace file
go.work

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Log files and directories
*.log
/logs/

# Build artifacts
/bin/
/dist/

# Vendor directory (if using go mod vendor)
/vendor/

# Test coverage
coverage.out
coverage.html

# Media files (uploads, temp files)
/media/
/temp/
/uploads/
