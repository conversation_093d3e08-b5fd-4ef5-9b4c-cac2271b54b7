# RealMaster 视频后端 API 文档

本文档详细说明了 RealMaster 视频后端服务提供的所有 API 接口。

## 📋 目录
- [通用响应格式](#-通用响应格式)
- [认证管理 (Authentication)](#-认证管理-authentication)
- [开发工具 (Development)](#-开发工具-development)
- [公共API (Public)](#-公共api-public)
- [交互事件 (Interactions)](#-交互事件-interactions)
- [分类管理 (Category)](#-分类管理-category)
- [广告主管理 (Advertiser)](#-广告主管理-advertiser)
- [房源管理 (Main Property)](#-房源管理-main-property)
- [视频管理 (Video)](#-视频管理-video)
- [内部文件上传 (Internal)](#-内部文件上传-internal)
- [后台预览接口 (Admin Preview)](#-后台预览接口-admin-preview)
- [静态文件服务 (Static Files)](#-静态文件服务-static-files)

## 📝 通用响应格式

所有API接口都使用统一的响应格式：

### 成功响应
```json
{
  "ok": 1,
  "msg": "操作成功",
  "data": { /* 具体数据 */ }
}
```

### 错误响应
```json
{
  "ok": 0,
  "err": "错误信息"
}
```

### 分页响应
```json
{
  "ok": 1,
  "data": {
    "items": [ /* 数据列表 */ ],
    "pgn": {
      "totItms": 100,
      "totPgs": 10,
      "currPg": 1,
      "lim": 10
    }
  }
}
```

---

## � 认证管理 (Authentication)
基础路径: `/auth`

### 1. JWT Token 刷新
- **POST** `/refreshtoken`
- **功能**: 使用 refresh token 刷新 JWT token
- **请求体** (`application/json`):
  ```json
  {
    "rft": "refresh_token_here",
    "isweb": true
  }
  ```
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "jwt": "new_jwt_token_here",
    "rft": "new_refresh_token_here",
    "jwtexp": 1672531200
  }
  ```
- **错误响应**:
  - `400 Bad Request`: refresh token 无效或已过期
  - `401 Unauthorized`: 认证失败

### 2. WK 转换为 JWT
- **POST** `/convertjwt`
- **功能**: 将传统的 wk (legacy API key) 转换为 JWT token
- **请求体** (`application/json`):
  ```json
  {
    "wk": "legacy_api_key_here"
  }
  ```
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "jwt": "jwt_token_here",
    "rft": "refresh_token_here",
    "jwtexp": 1672531200
  }
  ```
- **错误响应**:
  - `400 Bad Request`: wk 格式无效
  - `401 Unauthorized`: wk 无效或已过期

---

## 🛠️ 开发工具 (Development)
基础路径: `/dev` (仅在开发模式下可用)

### 1. 获取开发 JWT
- **GET** `/jwt`
- **功能**: 获取用于开发测试的 JWT token (24小时有效期)
- **查询参数**:
  - `user` (string, optional): 用户类型，可选值: `admin`, `user`, `realtor`, 默认为 `default`
- **成功响应** (`200 OK`):
  ```json
  {
    "jwt": "dev_jwt_token_here",
    "expires_in": 86400,
    "user_id": "dev_admin_001",
    "roles": ["user", "admin"]
  }
  ```

---

## 🌐 公共API (Public)
基础路径: `/video/public` (需要 JWT 认证)

### 1. 获取视频 Feed
- **GET** `/feed`
- **功能**: 获取已发布的视频列表，按发布时间倒序排列
- **查询参数**:
  - `page` (integer, optional, default: `1`): 页码
  - `limit` (integer, optional, default: `20`, max: `100`): 每页数量
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "msg": "获取Feed成功",
    "data": {
      "videos": [
        {
          "id": "68714a1cf4abe26417ffcc82",
          "title": {"zh": "视频标题", "en": "Video Title"},
          "description": {"zh": "视频描述"},
          "status": "Published",
          "duration": 120.5,
          "previewThumbUrl": "http://localhost:8080/media/68714a1cf4abe26417ffcc82/thumb.jpg",
          "previewVideoUrl": "http://localhost:8080/media/68714a1cf4abe26417ffcc82/video.m3u8",
          "uploaderId": "user-123",
          "categoryId": "6672c86ba745f44da2cf2a24",
          "tags": ["标签1", "标签2"],
          "propertyIds": ["prop-123"],
          "externalUrl": "http://example.com",
          "clientId": "667d5b8f1c4e8b3b9b4f4d30",
          "stats": {
            "views": 1024,
            "likes": 128,
            "collections": 64,
            "completions": 50,
            "completionRate": "48.83%"
          },
          "publishedAt": "2024-06-27T12:05:00Z"
        }
      ],
      "pagination": {
        "totItms": 100,
        "totPgs": 5,
        "currPg": 1,
        "lim": 20
      }
    }
  }
  ```

### 2. 批量获取用户状态
- **POST** `/states/batch`
- **功能**: 批量获取用户对指定视频的状态（点赞、收藏、观看进度等）
- **请求体** (`application/json`):
  ```json
  {
    "userId": "user_id_here",
    "videoIds": ["video_id_1", "video_id_2"]
  }
  ```
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "msg": "获取用户状态成功",
    "data": [
      {
        "userId": "97d5417e560a186a00000000",
        "videoId": "68714a1cf4abe26417ffcc82",
        "liked": true,
        "favorited": false,
        "blocked": false,
        "progressSeconds": 120
      }
    ]
  }
  ```

### 3. 通过查询参数获取用户状态
- **GET** `/states`
- **功能**: 通过查询参数批量获取用户状态
- **查询参数**:
  - `videoIds` (string, required): 视频ID列表，用逗号分隔，最多100个
- **成功响应** (`200 OK`): 结构同上批量获取接口

### 4. 获取用户收藏
- **GET** `/favorites`
- **功能**: 获取用户收藏的视频列表
- **查询参数**:
  - `page` (integer, optional, default: `1`): 页码
  - `limit` (integer, optional, default: `20`, max: `100`): 每页数量
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "msg": "获取用户收藏成功",
    "data": {
      "favorites": [
        {
          "videoId": "68714a1cf4abe26417ffcc82",
          "progressSeconds": 120
        }
      ],
      "pagination": {
        "totItms": 10,
        "totPgs": 1,
        "currPg": 1,
        "lim": 20
      }
    }
  }
  ```

---

## 🎯 交互事件 (Interactions)
基础路径: `/video/public/interactions` (需要 JWT 认证)

### 1. 创建交互事件
- **POST** `/`
- **功能**: 创建用户与视频的交互事件（点赞、收藏、观看等）
- **请求体** (`application/json`):
  ```json
  {
    "videoId": "68714a1cf4abe26417ffcc82",
    "type": "like",
    "meta": {
      "progSec": 120,
      "compPct": 85.5,
      "totalDurSec": 300,
      "plat": "web",
      "lId": "listing-123",
      "rsn": "不感兴趣"
    }
  }
  ```
- **支持的交互类型**:
  - `view_start`: 开始观看
  - `view_progress`: 观看进度更新 (需要 `progSec` 参数)
  - `view_complete`: 观看完成 (需要 `compPct` 和 `totalDurSec` 参数)
  - `like`: 点赞
  - `unlike`: 取消点赞
  - `favorite`: 收藏
  - `unfavorite`: 取消收藏
  - `share`: 分享 (需要 `plat` 参数)
  - `click_listing_link`: 点击房源链接 (需要 `lId` 参数)
  - `block_video`: 屏蔽视频 (需要 `rsn` 参数)
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "msg": "交互事件创建成功",
    "data": {
      "id": "6871c1fd02c12ed3aec12638",
      "userId": "97d5417e560a186a00000000",
      "videoId": "68714a1cf4abe26417ffcc82",
      "type": "like",
      "meta": {},
      "timestamp": "2025-07-11T21:48:03Z"
    }
  }
  ```

### 2. 获取用户交互历史
- **GET** `/user`
- **功能**: 获取当前用户的交互历史记录
- **查询参数**:
  - `limit` (integer, optional, default: `50`, max: `100`): 返回记录数量
- **成功响应** (`200 OK`):
  ```json
  {
    "ok": 1,
    "msg": "获取用户交互历史成功",
    "data": [
      {
        "id": "6871c1fd02c12ed3aec12638",
        "userId": "97d5417e560a186a00000000",
        "videoId": "68714a1cf4abe26417ffcc82",
        "type": "like",
        "meta": {},
        "timestamp": "2025-07-11T21:48:03Z"
      }
    ]
  }
  ```

### 3. 获取视频交互历史
- **GET** `/video/:videoId`
- **功能**: 获取指定视频的交互历史记录
- **URL 参数**:
  - `videoId` (string, required): 视频ID
- **查询参数**:
  - `limit` (integer, optional, default: `50`, max: `100`): 返回记录数量
- **成功响应** (`200 OK`): 结构同用户交互历史

---

## �🗂️ 分类管理 (Category)
基础路径: `/video/admin/categories`

### 1. 获取分类列表
- **GET** `/`
- **功能**: 获取所有分类的列表，支持分页，并按手动排序字段 `ord` 升序排列。
- **查询参数 (Query Params)**:
  - `page` (integer, optional, default: `1`): 页码。
  - `limit` (integer, optional, default: `10`): 每页数量。
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": {
          "items": [
              {
                  "id": "6672c86ba745f44da2cf2a24",
                  "nm": "旅行风景",
                  "dsc": "记录世界各地的美丽风景",
                  "ord": 1
              }
          ],
          "pgn": {
              "totItms": 1,
              "totPgs": 1,
              "currPg": 1,
              "lim": 10
          }
      }
  }
  ```

### 2. 创建新分类
- **POST** `/`
- **功能**: 创建一个新的视频分类。
- **请求体** (`application/json`):
  ```json
  {
    "name": "美食探店",
    "description": "发现城市中的美味佳肴",
    "order": 10
  }
  ```
- **成功响应** (`201 Created`):
  ```json
  {
      "ok": 1,
      "data": {
          "id": "6672c87fa745f44da2cf2a27",
          "nm": "美食探店",
          "dsc": "发现城市中的美味佳肴",
          "ord": 10
      }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 分类名称为空或过长
  - `409 Conflict`: 分类名称已存在

### 3. 更新分类
- **PUT** `/:id`
- **功能**: 更新指定 ID 的分类信息。
- **URL 参数**:
  - `id` (string, required): 分类的 ObjectID。
- **请求体** (`application/json`):
  ```json
  {
    "name": "美食探店 v2",
    "description": "更新后的描述",
    "order": 5
  }
  ```
- **成功响应** (`200 OK`):
  ```json
  { "ok": 1, "msg": "更新成功" }
  ```
- **错误响应**:
  - `400 Bad Request`: 分类ID格式无效或请求数据无效
  - `404 Not Found`: 分类不存在
  - `409 Conflict`: 分类名称已存在

### 4. 删除分类
- **DELETE** `/:id`
- **功能**: 删除指定 ID 的分类。如果该分类下仍有视频，则会删除失败。
- **URL 参数**:
  - `id` (string, required): 分类的 ObjectID。
- **成功响应** (`200 OK`):
  ```json
  { "ok": 1, "msg": "删除成功" }
  ```
- **错误响应**:
  - `400 Bad Request`: 分类ID格式无效
  - `404 Not Found`: 分类不存在
  - `409 Conflict`: 分类正在被使用，无法删除
  - `403 Forbidden`: 不能删除 'None' 分类

---

## 💼 广告主管理 (Advertiser)
基础路径: `/video/admin/advertisers`

### 1. 获取广告主列表
- **GET** `/`
- **功能**: 获取广告主列表，支持分页和搜索，按创建时间降序排列。
- **查询参数 (Query Params)**:
  - `page` (integer, optional, default: `1`): 页码。
  - `limit` (integer, optional, default: `10`): 每页数量。
  - `name` (string, optional): 按名称模糊搜索。
  - `phone` (string, optional): 按电话精确搜索。
  - `email` (string, optional): 按邮箱精确搜索。
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": {
          "items": [
              {
                  "id": "667d5b8f1c4e8b3b9b4f4d30",
                  "nm": "示例广告主",
                  "em": "<EMAIL>",
                  "ph": "13800138000",
                  "ts": "2024-06-27T12:00:00Z",
                  "mt": "2024-06-27T12:00:00Z"
              }
          ],
          "pgn": { "totItms": 1, "totPgs": 1, "currPg": 1, "lim": 10 }
      }
  }
  ```

### 2. 获取单个广告主详情
- **GET** `/:id`
- **功能**: 根据ID获取单个广告主的详细信息。
- **URL 参数**: `id` (string, required): 广告主的 ObjectID。
- **成功响应** (`200 OK`): (结构同上列表中的单个 `item`)
- **错误响应**:
  - `400 Bad Request`: 广告主ID格式无效
  - `404 Not Found`: 广告主不存在

### 3. 创建广告主
- **POST** `/`
- **功能**: 创建一个新的广告主。名称、邮箱、电话要求唯一。
- **请求体** (`application/json`):
  ```json
  { "name": "新广告主", "email": "<EMAIL>", "phone": "13900139000" }
  ```
- **成功响应** (`201 Created`): (结构同上列表中的单个 `item`)
- **错误响应**:
  - `400 Bad Request`: 请求数据格式无效或邮箱/电话格式无效
  - `409 Conflict`: 广告主名称、邮箱或电话已存在

### 4. 更新广告主
- **PATCH** `/:id`
- **功能**: 更新一个已存在的广告主信息。
- **URL 参数**: `id` (string, required): 广告主的 ObjectID。
- **请求体** (`application/json`, 所有字段均为可选):
  ```json
  { "name": "更新后的广告主", "phone": "13900139001" }
  ```
- **成功响应** (`200 OK`): `{"ok": 1, "msg": "更新成功"}`
- **错误响应**:
  - `400 Bad Request`: 广告主ID格式无效或请求数据无效
  - `404 Not Found`: 广告主不存在
  - `409 Conflict`: 更新后的名称、邮箱或电话与其他广告主冲突

### 5. 删除广告主
- **DELETE** `/:id`
- **功能**: 根据ID删除一个广告主。
- **URL 参数**: `id` (string, required): 广告主的 ObjectID。
- **成功响应** (`200 OK`): `{"ok": 1, "msg": "删除成功"}`
- **错误响应**:
  - `400 Bad Request`: 广告主ID格式无效
  - `404 Not Found`: 广告主不存在
  - `409 Conflict`: 无法删除唯一的广告主

---

## 🏠 房源管理 (Main Property)
基础路径: `/video/admin/properties`

### 1. 关键字搜索房源
- **POST** `/search`
- **功能**: 根据关键字搜索房源信息。
- **请求体** (`application/json`):
  ```json
  { "s": "花园" }
  ```
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": [
          {
              "id": "prop-123",
              "searchAddr": "示例地址 123号",
              "price": "5,000,000",
              "thumbUrl": "https://example.com/thumb.jpg",
              "saleOrRent": "Sale",
              "city": "Toronto",
              "prov": "Ontario",
              "cmty": "Downtown",
              "webUrl": "https://example.com/property/123",
              "bedroom": "2+1",
              "bathroom": "2",
              "parking": "1"
          }
      ]
  }
  ```

### 2. 批量获取房源
- **POST** `/batch-get`
- **功能**: 根据 ID 列表批量获取房源信息。
- **请求体** (`application/json`):
  ```json
  { "ids": ["prop-123", "prop-456"] }
  ```
- **成功响应** (`200 OK`): (结构同上，返回房源数组)

### 3. 获取单个房源
- **GET** `/:id`
- **功能**: 获取指定 ID 的房源详细信息。
- **URL 参数**: `id` (string, required): 房源的 ID。
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": {
          "id": "prop-123",
          "searchAddr": "示例地址 123号",
          "price": "5,000,000",
          "thumbUrl": "https://example.com/thumb.jpg",
          "saleOrRent": "Sale",
          "city": "Toronto",
          "prov": "Ontario",
          "cmty": "Downtown",
          "webUrl": "https://example.com/property/123",
          "bedroom": "2+1",
          "bathroom": "2",
          "parking": "1"
      }
  }
  ```
- **错误响应**:
  - `404 Not Found`: 房源不存在
  - `400 Bad Request`: 房源ID格式无效
  - `500 Internal Server Error`: 外部API调用失败

---

## 🎬 视频管理 (Video)
基础路径: `/video/admin/videos`

### 1. 获取视频列表
- **GET** `/`
- **功能**: 获取视频列表，支持分页和多种条件过滤，按创建时间 (`createdAt`) 降序排列。
- **查询参数 (Query Params)**:
  - `page` (integer, optional, default: `1`): 页码。
  - `limit` (integer, optional, default: `20`): 每页数量。
  - `categoryId` (string, optional): 按分类的 ObjectID 过滤。
  - `clientId` (string, optional): 按客户的 ObjectID 过滤。
  - `status` (string, optional): 按状态过滤。可多次使用此参数以选择多个状态 (e.g., `?status=Published&status=Unpublished`)。
    - 可选值: `"Draft"`, `"Pending"`, `"Processing"`, `"Published"`, `"ProcessingFailed"`, `"Unpublished"`
  - `from` (string, optional): 创建时间的起始点 (RFC3339 格式, e.g., `"2024-06-01T00:00:00Z"`)。
  - `to` (string, optional): 创建时间的结束点 (RFC3339 格式, e.g., `"2024-06-30T23:59:59Z"`)。
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": {
          "items": [
              {
                  "id": "667d64f01c4e8b3b9b4f4d31",
                  "title": { "zh": "已发布的视频", "en": "Published Video" },
                  "status": "Published",
                  "previewThumbUrl": "media/667d64f01c4e8b3b9b4f4d31/video-123_thumbnail.jpg",
                  "previewVideoUrl": "media/667d64f01c4e8b3b9b4f4d31/video-123_360p.mp4",
                  "uploaderId": "user-123",
                  "categoryId": "6672c86ba745f44da2cf2a24",
                  "tags": ["演示", "高清"],
                  "propertyIds": ["prop-123"],
                  "externalUrl": "http://example.com",
                  "clientId": "667d5b8f1c4e8b3b9b4f4d30",
                  "stats": {
                      "views": 1024,
                      "likes": 128,
                      "collections": 64,
                      "completions": 50,
                      "completionRate": "48.83%"
                  },
                  "processingError": "",
                  "createdAt": "2024-06-27T12:00:00Z",
                  "updatedAt": "2024-06-27T13:00:00Z",
                  "publishedAt": "2024-06-27T12:05:00Z"
              },
              {
                  "id": "667e8a4f1c4e8b3b9b4f4d32",
                  "title": { "zh": "草稿视频", "en": "Draft Video" },
                  "status": "Draft",
                  "previewThumbUrl": "/video/admin/temp-content/thumb-random123.jpg",
                  "previewVideoUrl": "/video/admin/temp-content/video-random456.mp4",
                  "uploaderId": "user-123",
                  "createdAt": "2024-06-28T10:00:00Z",
                  "updatedAt": "2024-06-28T10:00:00Z",
                  "publishedAt": null
              }
          ],
          "pgn": {
              "totItms": 1,
              "totPgs": 1,
              "currPg": 1,
              "lim": 20
          }
      }
  }
  ```

### 2. 获取视频统计数据
- **GET** `/stats`
- **功能**: 根据与视频列表完全相同的筛选条件，获取视频的聚合统计数据。
- **查询参数 (Query Params)**:
  - **完全同上** (`/` 接口)，支持 `categoryId`, `clientId`, `status`, `from`, `to`。
- **成功响应** (`200 OK`):
  ```json
  {
      "ok": 1,
      "data": {
          "totalVideos": 125,
          "totalViews": 25480,
          "totalLikes": 3120,
          "totalCollections": 1850,
          "overallCompletionRate": "75.50%"
      }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 查询参数格式无效（如无效的分类ID、客户ID或时间格式）
  - `500 Internal Server Error`: 统计数据聚合查询失败

### 3. 创建视频草稿
- **POST** `/`
- **功能**: 创建一个视频记录（草稿状态），并可选择同步上传封面和视频文件。
- **请求体** (`multipart/form-data`):
  - `thumbnail` (file, optional): 封面图文件。
  - `video` (file, optional): 视频文件。
  - `titleZh` (string, required): 中文标题。
  - `titleEn` (string, optional): 英文标题。
  - `descriptionZh` (string, optional): 中文描述。
  - `descriptionEn` (string, optional): 英文描述。
  - `uploaderId` (string, required): 上传者ID。
  - `categoryId` (string, optional): 分类的 ObjectID。
  - `clientId` (string, optional): 广告主的 ObjectID。
  - `publishNow` (boolean, optional): 是否立即发布。如果为 `true`，则创建后视频状态直接变为 `Pending`。
- **成功响应** (`201 Created`): (返回完整的视频对象，结构同"获取视频列表"中的单个 `item`)
- **错误响应**:
  - `400 Bad Request`: 请求数据格式无效或必填字段缺失
  - `404 Not Found`: 指定的分类ID或客户ID不存在

### 4. 发布视频
- **POST** `/:id/publish`
- **功能**: 将指定 ID 的草稿视频状态更新为 "Pending" (待处理)，以触发后台转码。
- **URL 参数**: `id` (string, required): 视频的 ObjectID。
- **成功响应** (`202 Accepted`):
  ```json
  { "ok": 1, "msg": "视频已提交，等待处理" }
  ```
- **错误响应**:
  - `400 Bad Request`: 视频ID格式无效
  - `404 Not Found`: 视频不存在
  - `409 Conflict`: 视频状态不允许发布（如已发布或正在处理中）
  
### 5. 更新视频统计
- **PATCH** `/:id/stats`
- **功能**: 原子性地增加视频的统计数据。
- **URL 参数**: `id` (string, required): 视频的 ObjectID。
- **请求体** (`application/json`):
  ```json
  {
      "views": 1,
      "likes": 0,
      "collections": 0,
      "completions": 0
  }
  ```
- **成功响应** (`200 OK`):
  ```json
  { "ok": 1, "data": { "message": "统计数据更新成功" } }
  ```
- **错误响应**:
  - `400 Bad Request`: 视频ID格式无效或统计数据格式无效
  - `404 Not Found`: 视频不存在

### 6. 更新视频信息
- **PATCH** `/:id`
- **功能**: 更新视频的元数据，可选地替换文件或改变视频状态。这是一个非常灵活的接口。
- **URL 参数**: `id` (string, required): 视频的 ObjectID。
- **请求体** (`multipart/form-data`):
  - `metadata` (string, optional): 一个包含要更新的元数据的 JSON 字符串。
  - `action` (string, optional): 执行状态变更操作。可选值为: `"publish"`, `"unpublish"`。
  - `video` (file, optional): 新的视频文件。仅在视频为 `Draft` 或 `ProcessingFailed` 状态时才能上传。
  - `thumbnail` (file, optional): 新的封面图文件。可以随时替换。
- **`metadata` JSON 结构**:
  ```json
  {
      "title": { "zh": "新中文标题", "en": "New English Title" },
      "description": { "zh": "新中文描述" },
      "tags": ["新标签1", "新标签2"],
      "propertyIds": ["prop-789"],
      "externalUrl": "http://new-external-link.com",
      "categoryId": "6672c86ba745f44da2cf2a24",
      "clientId": "667d5b8f1c4e8b3b9b4f4d30"
  }
  ```
- **Action 行为**:
  - `publish`: 尝试将 `Draft`, `ProcessingFailed`, 或 `Unpublished` 状态的视频发布 (状态变为 `Pending`)。
  - `unpublish`: 尝试将 `Published` 状态的视频下架 (状态变为 `Unpublished`)。
- **成功响应** (`200 OK`): 返回完整的视频对象，结构同"获取视频列表"中的单个 `item`。
- **错误响应**:
  - `409 Conflict`: 当尝试修改一个正在处理中 (`Pending` 或 `Processing`) 的视频时。
  - `404 Not Found`: 视频 ID 不存在。

### 7. 删除视频
- **DELETE** `/:id`
- **功能**: 删除一个视频及其关联的所有文件。
- **URL 参数**: `id` (string, required): 视频的 ObjectID。
- **成功响应** (`200 OK`):
  ```json
  { "ok": 1, "msg": "视频删除成功" }
  ```
- **错误响应**:
  - `400 Bad Request`: 视频ID格式无效
  - `404 Not Found`: 视频不存在
  - `409 Conflict`: 无法删除正在处理中的视频

### 8. 获取单个视频详情
- **GET** `/:id`
- **功能**: 根据视频的 `ObjectID` 获取其完整的详细信息。
- **URL 参数**:
  - `id` (string, required): 视频的 `ObjectID`。
- **成功响应** (`200 OK`):
  - 返回一个 `APIResponse`，其 `data` 字段包含一个完整的视频对象。这个对象的结构与"获取视频列表"中 `items` 数组里的单个对象结构相同。
- **示例成功响应** (`200 OK`):

```json
{
  "ok": 1,
  "data": {
    "id": "667d5f051c4e8b3b9b4f4d33",
    "title": {
      "zh": "这是一个中文标题",
      "en": "This is an English Title"
    },
    "status": "Published",
    "duration": 120.5,
    "previewThumbUrl": "/media/667d5f051c4e8b3b9b4f4d33/thumbnail.jpg",
    "previewVideoUrl": "/media/667d5f051c4e8b3b9b4f4d33/manifest.mpd",
    "uploaderId": "user-123",
    "categoryId": "6672c86ba745f44da2cf2a24",
    "tags": ["房产", "演示"],
    "propertyIds": ["prop-456", "prop-789"],
    "externalUrl": "http://example.com/property/123",
    "clientId": "667d5b8f1c4e8b3b9b4f4d30",
    "stats": {
      "views": 1024,
      "likes": 128,
      "collections": 64,
      "completions": 50,
      "completionRate": "4.88%"
    },
    "processingError": "",
    "createdAt": "2024-06-27T14:30:00Z",
    "updatedAt": "2024-06-27T14:35:00Z",
    "publishedAt": "2024-06-27T14:32:00Z"
  }
}
```
- **错误响应**:
  - `400 Bad Request`: 视频ID格式无效
  - `404 Not Found`: 视频不存在

---

## ⬆️ 内部文件上传 (Internal)
基础路径: `/upload` (无 `/video/admin` 前缀)

### 1. 上传视频文件
- **POST** `/video/:videoID/:fileName`
- **功能**: **(内部接口)** 由后台 `video-worker` 调用，用于将转码和切片完成后的视频文件（如 `.m3u8`, `.ts`, `.mpd`, `.m4s`）上传到最终存储位置。
- **URL 参数**:
  - `videoID` (string, required): 视频的 ObjectID。
  - `fileName` (string, required): 要保存的文件名。
- **请求体** (`multipart/form-data`): 包含文件内容的表单。
- **成功响应** (`201 Created`):
  ```json
  {
    "ok": 1,
    "data": {
        "message": "文件上传成功",
        "path": "/var/www/rm_video_media/666f72646572/my_video_1080p.mp4"
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 缺少videoID、fileName参数或文件
  - `500 Internal Server Error`: 创建目录失败、创建文件失败或保存文件失败

---

## 🔒 后台预览接口 (Admin Preview)
基础路径: `/video/admin/videos`

### 1. 获取临时文件内容
- **GET** `/temp-content/:filename`
- **功能**: **(后台专用)** 此接口为后台管理页面提供**草稿状态 (Draft/ProcessingFailed)** 的视频和封面预览。
- **重要**:
  - 此接口通过安全的文件名清理机制防止目录遍历攻击。
  - 它是被 `GET /videos` 接口返回的 `preview...` URL 触发的。当浏览器渲染一个 `src` 指向此接口的 `<img>` 或 `<video>` 标签时，会自动向此接口发起请求。
  - 前端只需使用"获取视频列表"接口返回的 `preview...` 字段值，并与主域名拼接成完整URL即可。
- **URL 参数**:
  - `filename` (string, required): 要获取的临时文件的名称。文件名会被自动清理以确保安全性。
- **成功响应**:
  - **不是 JSON！** 响应体是文件的**原始内容**。
  - HTTP 状态码为 `200 OK`。
  - 会包含正确的 `Content-Type` 和文件头信息。
- **错误响应** (`application/json`):
  - `404 Not Found`: 如果在临时目录中找不到该文件。
  - `400 Bad Request`: 如果提供的 `filename` 格式不合法。
  ```json
  { "ok": 0, "err": "文件未找到" }
  ```

---

## 📁 静态文件服务 (Static Files)

### 1. 媒体文件访问
- **GET** `/media/{path}`
- **功能**: 访问已发布的视频文件和封面图片
- **URL 参数**:
  - `path` (string, required): 媒体文件的相对路径，使用L1/L2分层存储结构，格式为 `{board}/{L1}/{L2}/{videoID}/{filename}`
- **示例**:
  - `/media/EDM/2025-28/abc123/667d64f01c4e8b3b9b4f4d31/thumbnail.jpg` - 访问视频封面
  - `/media/EDM/2025-28/abc123/667d64f01c4e8b3b9b4f4d31/stream.m3u8` - 访问HLS播放列表
  - `/media/EDM/2025-28/abc123/667d64f01c4e8b3b9b4f4d31/segment_001.ts` - 访问视频片段
- **成功响应**: 直接返回文件内容，HTTP 状态码为 `200 OK`
- **错误响应**:
  - `404 Not Found`: 文件不存在

### 2. 临时文件访问
- **GET** `/temp/{path}`
- **功能**: 访问临时上传的文件（如草稿状态的视频和封面）
- **URL 参数**:
  - `path` (string, required): 临时文件的相对路径，使用L1/L2分层存储结构
- **示例**:
  - `/temp/EDM/2025-28/abc123/thumb-random123.jpg` - 访问临时封面
  - `/temp/EDM/2025-28/abc123/video-random456.mp4` - 访问临时视频
- **成功响应**: 直接返回文件内容，HTTP 状态码为 `200 OK`
- **错误响应**:
  - `404 Not Found`: 文件不存在

---

## 📊 API 总览

### 端点统计
- **认证管理**: 2个端点 (JWT刷新, WK转换)
- **开发工具**: 1个端点 (开发JWT)
- **公共API**: 4个端点 (Feed, 用户状态, 用户收藏)
- **交互事件**: 3个端点 (创建交互, 用户历史, 视频历史)
- **分类管理**: 4个端点 (GET, POST, PUT, DELETE)
- **广告主管理**: 5个端点 (GET列表, GET单个, POST, PATCH, DELETE)
- **房源管理**: 3个端点 (POST搜索, POST批量获取, GET单个)
- **视频管理**: 8个端点 (GET列表, GET统计, POST创建, GET单个, POST发布, PATCH统计, PATCH更新, DELETE)
- **文件上传**: 1个内部端点 (POST上传)
- **预览服务**: 1个端点 (GET临时内容)
- **静态文件**: 2个端点 (GET媒体文件, GET临时文件)

**总计**: 34个API端点

### 认证与授权
- ✅ **JWT认证**: 使用goauth库实现完整的JWT认证系统
- ✅ **Token刷新**: 支持refresh token自动刷新机制
- ✅ **Legacy支持**: 支持wk (legacy API key) 转换为JWT
- ✅ **角色权限**: 支持用户角色和权限管理
- 🔒 **受保护路由**: 所有管理后台API (`/video/admin/*`) 和公共API (`/video/public/*`) 都需要JWT认证
- 🛡️ **内部API**: 文件上传API (`/upload/*`) 限制为内部服务访问

### 错误处理
- 所有API都遵循统一的错误响应格式
- HTTP状态码正确映射业务错误类型
- 提供中文和英文错误信息以支持国际化

### 数据模型
#### 用户状态 (video_states)
```json
{
  "_id": {
    "uid": "ObjectId", // 用户ID
    "vid": "ObjectId"  // 视频ID
  },
  "liked": false,      // 是否点赞
  "faved": false,      // 是否收藏
  "blkd": false,       // 是否屏蔽
  "progSec": 0,        // 观看进度（秒）
  "_ts": "ISODate",    // 创建时间戳（gomongo自动管理）
  "_mt": "ISODate"     // 修改时间戳（gomongo自动管理）
}
```

#### 交互事件 (video_interactions)
```json
{
  "_id": "ObjectId",
  "uid": "ObjectId",   // 用户ID
  "vid": "ObjectId",   // 视频ID
  "tp": "like",        // 交互类型
  "meta": {},          // 元数据
  "_ts": "ISODate"     // 时间戳（gomongo自动管理）
}
```

#### 视频统计 (videos.stats)
```json
{
  "vws": 1024,         // 观看次数
  "lks": 128,          // 点赞数
  "cltsCnt": 64,       // 收藏数
  "cplCnt": 50,        // 完成观看数
  "cplRt": "48.83%"    // 完成率
}
```

### 性能考虑
- 视频列表API支持分页，默认每页20条记录
- 统计API使用MongoDB聚合管道优化查询性能
- 静态文件服务支持HTTP Range请求，优化视频播放体验
- 用户状态使用复合主键 `{uid, vid}` 确保唯一性和查询性能
- 交互事件支持批量操作和索引优化
- 视频统计使用原子性更新保证数据一致性
- 文件存储使用L1/L2分层结构，优化文件系统性能

### 开发环境支持
- 🛠️ **开发JWT**: 提供24小时有效期的开发测试token
- 🔄 **热重载**: 开发模式下支持配置和代码热重载
- 📊 **调试工具**: 提供详细的日志和错误信息
- 🌐 **CORS支持**: 开发环境下允许跨域请求
- 📁 **临时文件服务**: 支持L1/L2分层存储的临时文件访问

### 生产环境特性
- 🔒 **安全认证**: 完整的JWT认证和授权机制
- 📈 **性能优化**: 数据库索引和查询优化
- 🛡️ **错误处理**: 统一的错误响应格式和日志记录
- 📱 **移动端支持**: 支持React Native WebView集成
- 🔄 **事务支持**: 关键操作使用数据库事务保证一致性

---

## 🧪 API 测试

### 快速测试命令

#### 1. 获取开发JWT
```bash
curl "http://localhost:8080/dev/jwt?user=admin"
```

#### 2. 测试Feed API
```bash
JWT=$(curl -s "http://localhost:8080/dev/jwt?user=admin" | grep -o '"jwt":"[^"]*"' | cut -d'"' -f4)
curl -H "Authorization: Bearer $JWT" "http://localhost:8080/video/public/feed"
```

#### 3. 创建交互事件
```bash
JWT=$(curl -s "http://localhost:8080/dev/jwt?user=admin" | grep -o '"jwt":"[^"]*"' | cut -d'"' -f4)
curl -X POST -H "Authorization: Bearer $JWT" -H "Content-Type: application/json" \
  -d '{"videoId":"68714a1cf4abe26417ffcc82","type":"like"}' \
  "http://localhost:8080/video/public/interactions"
```

#### 4. 获取用户状态
```bash
JWT=$(curl -s "http://localhost:8080/dev/jwt?user=admin" | grep -o '"jwt":"[^"]*"' | cut -d'"' -f4)
curl -H "Authorization: Bearer $JWT" \
  "http://localhost:8080/video/public/states?videoIds=68714a1cf4abe26417ffcc82"
```

### Postman 集合
项目提供了完整的Postman测试集合：
- `postman/M1-Video-Backend-API.postman_collection.json`
- `postman/M1-Video-Backend-Environment.postman_environment.json`

导入这些文件到Postman即可进行完整的API测试。

---

## 📝 更新日志

### v2.0.0 (2025-07-12)
- ✅ 添加完整的JWT认证系统
- ✅ 实现公共API (Feed, 用户状态, 收藏)
- ✅ 实现交互事件系统
- ✅ 优化用户状态数据模型（移除st字段）
- ✅ 添加开发工具支持（开发JWT）
- ✅ 完善错误处理和日志记录
- ✅ 支持不存在视频的交互事件处理
- ✅ 使用gomongo自动时间戳管理
- ✅ 实现L1/L2分层文件存储结构

### v1.0.0 (2024-06-27)
- ✅ 基础管理后台API
- ✅ 视频管理和文件上传
- ✅ 分类和广告主管理
- ✅ 房源集成
- ✅ 静态文件服务
