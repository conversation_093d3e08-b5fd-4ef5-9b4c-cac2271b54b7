# RealMaster 视频后端系统

<div align="center">

![Go Version](https://img.shields.io/badge/Go-1.21+-00ADD8?style=for-the-badge&logo=go)
![MongoDB](https://img.shields.io/badge/MongoDB-5.0+-47A248?style=for-the-badge&logo=mongodb)
![License](https://img.shields.io/badge/License-MIT-blue?style=for-the-badge)
![Build Status](https://img.shields.io/badge/Build-Passing-success?style=for-the-badge)

**企业级视频内容管理平台 | 专为房地产行业设计**

</div>

---

## 📖 概述

RealMaster视频后端系统是一个**功能完整的企业级视频内容管理平台**，专为房地产行业设计。系统采用现代化的微服务架构，提供从视频上传、转码、存储到播放的完整解决方案，并深度集成房源信息系统，支持用户行为分析和个性化推荐。

### 🏗️ 系统架构

- **API服务器**: 提供RESTful API，处理视频元数据管理、用户认证、业务逻辑
- **Worker服务**: 异步处理视频转码、切片、文件管理等耗时任务
- **存储系统**: L1/L2分层文件存储，支持HLS/DASH自适应流媒体
- **数据库**: MongoDB集群，支持事务和高并发访问

## ✨ 核心特性

### 🎬 视频管理
- **完整生命周期**: 从上传到发布的全流程管理
- **多格式支持**: 支持主流视频格式，自动转码为HLS/DASH
- **多分辨率**: 自动生成360p/720p/1080p多分辨率版本
- **状态跟踪**: 实时跟踪视频处理状态和进度

### 👥 用户系统
- **JWT认证**: 标准JWT认证，支持Token刷新
- **角色权限**: 基于角色的权限管理系统
- **用户状态**: 完整的用户观看状态和交互记录
- **行为分析**: 详细的用户行为数据收集和分析

### 🏠 业务集成
- **房源关联**: 视频与房源信息的深度集成
- **广告主管理**: 多广告主的内容管理和权限控制
- **分类系统**: 灵活的分类管理和标签系统
- **统计分析**: 实时的播放量、点赞、收藏等统计

### 🔧 技术特性
- **高性能**: 支持高并发访问和大文件处理
- **可扩展**: 模块化设计，易于水平扩展
- **容错性**: 完善的错误处理和恢复机制
- **监控友好**: 详细的日志记录和性能指标

---

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Go** | 1.21+ | 主要开发语言 |
| **MongoDB** | 5.0+ | 主数据库，建议使用副本集 |
| **FFmpeg** | 4.4+ | 视频转码工具 |
| **Shaka Packager** | 2.6+ | DASH打包工具 |
| **操作系统** | Linux/macOS | 推荐Ubuntu 20.04+ |

### 🔧 依赖安装

#### 1. 安装FFmpeg
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# macOS
brew install ffmpeg

# 验证安装
ffmpeg -version
```

#### 2. 安装Shaka Packager
```bash
# 下载最新版本
wget https://github.com/shaka-project/shaka-packager/releases/download/v2.6.1/packager-linux-x64
chmod +x packager-linux-x64
sudo mv packager-linux-x64 /usr/local/bin/packager

# 验证安装
packager --version
```

### 🚀 本地开发

#### 1. 克隆项目
```bash
git clone <your-repository-url>
cd apps/realmaster-video-backend
```

#### 2. 安装Go依赖
```bash
go mod tidy
```

#### 3. 配置环境
```bash
# 复制配置模板
cp config.toml.example config.toml

# 编辑配置文件
vim config.toml
```

#### 4. 启动MongoDB
```bash
# 使用Docker启动MongoDB
docker run -d --name mongodb \
  -p 27017:27017 \
  -v mongodb_data:/data/db \
  mongo:5.0

# 或使用现有MongoDB实例
```

#### 5. 运行服务
```bash
# 终端1: 启动API服务器
RMBASE_FILE_CFG=config.toml go run ./cmd/server/main.go

# 终端2: 启动视频处理Worker
RMBASE_FILE_CFG=config.toml go run ./cmd/video-worker/main.go
```

#### 6. 验证服务
```bash
# 检查API服务器
curl http://localhost:8080/health

# 获取开发JWT Token
curl http://localhost:8080/dev/jwt?user=admin
```

---

## 📚 API使用指南

### 🔑 认证
```bash
# 获取开发JWT Token
JWT=$(curl -s "http://localhost:8080/dev/jwt?user=admin" | jq -r '.jwt')

# 使用Token访问API
curl -H "Authorization: Bearer $JWT" "http://localhost:8080/video/public/feed"
```

### 🎬 视频管理示例

#### 创建视频
```bash
curl -X POST "http://localhost:8080/video/admin/videos" \
  -H "Authorization: Bearer $JWT" \
  -H "Content-Type: multipart/form-data" \
  -F 'titleZh=我的旅行视频' \
  -F 'titleEn=My Travel Video' \
  -F 'descriptionZh=记录美好时光' \
  -F 'uploaderId=user-123' \
  -F 'categoryId=6672c86ba745f44da2cf2a24' \
  -F 'clientId=6672c889a745f44da2cf2a25' \
  -F 'video=@path/to/video.mp4' \
  -F 'thumbnail=@path/to/thumbnail.jpg'
```

#### 获取视频列表
```bash
curl -H "Authorization: Bearer $JWT" \
  "http://localhost:8080/video/admin/videos?page=1&limit=20&status=Published"
```

#### 创建用户交互
```bash
curl -X POST "http://localhost:8080/video/public/interactions" \
  -H "Authorization: Bearer $JWT" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": "68714a1cf4abe26417ffcc82",
    "type": "like"
  }'
```

### 📖 完整API文档
详细的API文档请参考：[API.md](./API.md)

### 🧪 测试工具
项目提供了Postman测试集合：
- `postman/M1-Video-Backend-API.postman_collection.json`
- `postman/M1-Video-Backend-Environment.postman_environment.json`

---

## ⚙️ 配置管理

### 📝 配置文件结构

配置文件采用TOML格式，支持环境变量覆盖：

```toml
# config.toml
[server]
port = "8080"
read_timeout = "30s"
write_timeout = "30s"
idle_timeout = "120s"

[database]
uri = "mongodb://localhost:27017"
database = "realmaster_video"
timeout = 10

[auth]
jwt_secret = "your-jwt-secret"
jwt_expiration = 3600
refresh_secret = "your-refresh-secret"
refresh_expiration = 604800

[media]
storage_dir = "/var/www/rm_video_media"
base_url = "http://localhost:8080"

[temp]
storage_dir = "/var/www/tmp/rm_video_uploads"
cleanup_interval = "1h"

[external]
realmaster_api_url = "https://www.realmaster.com/1.5/props/autocompleteGetNext"
```

### 🔧 环境配置

| 环境 | 配置文件 | 说明 |
|------|----------|------|
| **开发** | `config.toml` | 本地开发环境 |
| **测试** | `config.test.toml` | 测试环境配置 |
| **生产** | `config.prod.toml` | 生产环境配置 |

### 📊 关键配置项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `server.port` | string | `8080` | API服务器端口 |
| `database.uri` | string | - | MongoDB连接字符串 |
| `auth.jwt_secret` | string | - | JWT签名密钥 |
| `media.storage_dir` | string | - | 媒体文件存储目录 |
| `temp.storage_dir` | string | - | 临时文件存储目录 |

---

## 📁 项目结构

```text
apps/realmaster-video-backend/
├── cmd/                          # 应用程序入口点
│   ├── server/                   # HTTP API服务器
│   │   └── main.go
│   └── video-worker/             # 视频处理Worker
│       └── main.go
├── internal/                     # 内部包（不对外暴露）
│   ├── config/                   # 配置管理
│   ├── database/                 # 数据库连接和事务
│   ├── middleware/               # HTTP中间件
│   ├── server/                   # 服务器配置和路由
│   ├── common/                   # 通用工具和响应格式
│   └── domain/                   # 业务领域模块
│       ├── category/             # 分类管理
│       ├── advertiser/           # 广告主管理
│       ├── main_property/        # 房源管理
│       ├── video/                # 视频管理
│       ├── state/                # 用户状态管理
│       ├── interaction/          # 用户交互管理
│       └── public/               # 公共API
├── configs/                      # 配置文件
│   ├── config.toml              # 开发环境配置
│   ├── config.test.toml         # 测试环境配置
│   └── config.prod.toml         # 生产环境配置
├── media/                        # 媒体文件存储
├── temp/                         # 临时文件存储
├── logs/                         # 日志文件
├── postman/                      # Postman测试集合
├── docs/                         # 项目文档
│   ├── API.md                   # API文档
│   └── SYSTEM_ANALYSIS.md       # 系统分析文档
├── scripts/                      # 部署和维护脚本
├── docker/                       # Docker相关文件
├── go.mod                        # Go模块定义
├── go.sum                        # Go模块校验
└── README.md                     # 项目说明文档
```

### 🏗️ 架构设计

- **分层架构**: Handler → Service → Repository
- **领域驱动**: 按业务领域组织代码
- **依赖注入**: 通过接口实现松耦合
- **事务支持**: 跨模块的数据一致性保证

---

## 🚀 生产部署

### 🐳 Podman部署（推荐）

#### 1. 安装Podman
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install podman

# RHEL/CentOS/Fedora
sudo dnf install podman

# 验证安装
podman --version
```

#### 2. 编译和构建镜像

##### 使用构建脚本（推荐）
```bash
# 使用提供的构建脚本
chmod +x scripts/build-and-package.sh
./scripts/build-and-package.sh

# 或者指定镜像标签
IMAGE_TAG=v1.0.0 ./scripts/build-and-package.sh
```

##### 手动构建
```bash
# 首先编译二进制文件
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o server ./cmd/api/main.go
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o worker ./cmd/video-worker/main.go

# 构建API服务器镜像
podman build -f Dockerfile.api -t realmaster-video-api:latest .

# 构建Worker镜像
podman build -f Dockerfile.worker -t realmaster-video-worker:latest .

# 清理临时二进制文件
rm -f server worker
```

#### 3. 创建Pod和网络
```bash
# 创建专用网络
podman network create realmaster-network

# 创建Pod（类似Docker Compose的服务组）
podman pod create --name realmaster-pod \
  --network realmaster-network \
  -p 8080:8080 \
  -p 27017:27017 \
  -p 80:80 \
  -p 443:443
```

#### 4. 创建存储卷
```bash
# 创建持久化存储卷
podman volume create mongodb_data
podman volume create media_storage
podman volume create temp_storage
```

#### 5. 启动服务容器
```bash
# 启动MongoDB
podman run -d --name mongodb \
  --pod realmaster-pod \
  --restart always \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  -v mongodb_data:/data/db \
  mongo:5.0

# 启动API服务器
podman run -d --name api \
  --pod realmaster-pod \
  --restart always \
  -e RMBASE_FILE_CFG=/app/config.prod.toml \
  -v ./configs/config.prod.toml:/app/config.prod.toml:ro \
  -v media_storage:/var/www/rm_video_media \
  -v temp_storage:/var/www/tmp/rm_video_uploads \
  realmaster-video-api:latest

# 启动Worker服务
podman run -d --name worker \
  --pod realmaster-pod \
  --restart always \
  -e RMBASE_FILE_CFG=/app/config.prod.toml \
  -v ./configs/config.prod.toml:/app/config.prod.toml:ro \
  -v media_storage:/var/www/rm_video_media \
  -v temp_storage:/var/www/tmp/rm_video_uploads \
  realmaster-video-worker:latest

# 启动Nginx（可选）
podman run -d --name nginx \
  --pod realmaster-pod \
  --restart always \
  -v ./nginx.conf:/etc/nginx/nginx.conf:ro \
  -v media_storage:/var/www/rm_video_media:ro \
  nginx:alpine
```

#### 6. 使用Podman Compose（推荐）

##### 准备配置文件
```bash
# 复制Compose配置模板
cp podman-compose.yml.example podman-compose.yml

# 编辑配置文件（修改密码、端口等）
vim podman-compose.yml

# 确保配置文件存在
cp config.toml config.prod.toml
vim config.prod.toml  # 修改生产环境配置
```

##### 一键部署
```bash
# 安装podman-compose
pip3 install podman-compose

# 使用构建脚本构建镜像
./scripts/build-and-package.sh

# 启动所有服务
podman-compose up -d

# 查看服务状态
podman-compose ps

# 查看日志
podman-compose logs -f

# 停止所有服务
podman-compose down
```

##### 镜像导出和导入（用于离线部署）
```bash
# 导出镜像
podman save realmaster-video-api:latest -o api-image.tar
podman save realmaster-video-worker:latest -o worker-image.tar

# 在目标服务器上导入镜像
podman load -i api-image.tar
podman load -i worker-image.tar

# 然后使用podman-compose部署
podman-compose up -d
```

### 🔧 手动部署

#### 1. 系统准备
```bash
# 创建用户
sudo useradd -m -s /bin/bash realmaster
sudo usermod -aG sudo realmaster

# 创建目录
sudo mkdir -p /var/www/rm_video_media
sudo mkdir -p /var/www/tmp/rm_video_uploads
sudo mkdir -p /var/log/realmaster-video
sudo chown -R realmaster:realmaster /var/www/rm_video_media
sudo chown -R realmaster:realmaster /var/www/tmp/rm_video_uploads
sudo chown -R realmaster:realmaster /var/log/realmaster-video
```

#### 2. 编译部署
```bash
# 编译二进制文件
CGO_ENABLED=0 GOOS=linux go build -o bin/api ./cmd/server/main.go
CGO_ENABLED=0 GOOS=linux go build -o bin/worker ./cmd/video-worker/main.go

# 部署到服务器
scp bin/* user@server:/opt/realmaster-video/
scp configs/config.prod.toml user@server:/opt/realmaster-video/
```

#### 3. 配置Systemd服务

##### 使用Podman的Systemd集成
```bash
# 生成Systemd服务文件（用户级别）
mkdir -p ~/.config/systemd/user

# 为Pod生成服务文件
podman generate systemd --new --name realmaster-pod \
  --files --restart-policy=always

# 移动服务文件到正确位置
mv *.service ~/.config/systemd/user/

# 启用用户级别的systemd服务
systemctl --user daemon-reload
systemctl --user enable pod-realmaster-pod.service
systemctl --user start pod-realmaster-pod.service

# 启用用户服务在系统启动时自动启动
sudo loginctl enable-linger $(whoami)
```

##### 传统二进制部署的Systemd服务
```ini
# /etc/systemd/system/realmaster-video-api.service
[Unit]
Description=RealMaster Video API Server
After=network.target mongodb.service

[Service]
Type=simple
User=realmaster
WorkingDirectory=/opt/realmaster-video
Environment=RMBASE_FILE_CFG=/opt/realmaster-video/config.prod.toml
ExecStart=/opt/realmaster-video/api
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```ini
# /etc/systemd/system/realmaster-video-worker.service
[Unit]
Description=RealMaster Video Worker
After=network.target mongodb.service

[Service]
Type=simple
User=realmaster
WorkingDirectory=/opt/realmaster-video
Environment=RMBASE_FILE_CFG=/opt/realmaster-video/config.prod.toml
ExecStart=/opt/realmaster-video/worker
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

#### 4. 启动和管理服务

##### Podman服务管理
```bash
# 查看Pod状态
podman pod ps

# 查看容器状态
podman ps -a

# 查看日志
podman logs api
podman logs worker
podman logs mongodb

# 重启服务
systemctl --user restart pod-realmaster-pod.service

# 查看服务状态
systemctl --user status pod-realmaster-pod.service
```

##### 传统服务管理
```bash
sudo systemctl daemon-reload
sudo systemctl enable realmaster-video-api
sudo systemctl enable realmaster-video-worker
sudo systemctl start realmaster-video-api
sudo systemctl start realmaster-video-worker
```

### 🔒 生产环境配置调整

#### 1. 安全配置
```toml
# config.prod.toml
[auth]
jwt_secret = "your-super-secure-jwt-secret-256-bits"
refresh_secret = "your-super-secure-refresh-secret-256-bits"

[database]
uri = "*********************************************************************************"
timeout = 30

[server]
read_timeout = "30s"
write_timeout = "30s"
idle_timeout = "120s"
```

#### 2. 性能优化
- **MongoDB**: 使用副本集，启用事务支持
- **文件存储**: 使用SSD存储，配置合适的文件权限
- **内存**: 建议API服务器2GB+，Worker服务器4GB+
- **CPU**: 建议4核心以上，Worker需要更多CPU资源

#### 3. 监控配置
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 配置日志轮转
sudo vim /etc/logrotate.d/realmaster-video
```

### 🌐 Nginx反向代理配置

```nginx
# /etc/nginx/sites-available/realmaster-video
server {
    listen 80;
    server_name your-domain.com;

    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 上传文件大小限制
        client_max_body_size 1G;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # 静态文件服务
    location /media/ {
        alias /var/www/rm_video_media/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # 支持Range请求（视频播放）
        add_header Accept-Ranges bytes;
    }

    # 临时文件（仅开发环境）
    location /temp/ {
        alias /var/www/tmp/rm_video_uploads/;
        expires 1h;
    }
}
```

---

## ✅ 测试

### 🧪 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/domain/video/...
go test ./internal/domain/category/...

# 运行测试并生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

### 🔄 集成测试
```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
go test -tags=integration ./tests/...

# 清理测试环境
docker-compose -f docker-compose.test.yml down
```

### 📊 性能测试
```bash
# 使用Apache Bench测试API性能
ab -n 1000 -c 10 http://localhost:8080/video/public/feed

# 使用wrk测试高并发
wrk -t12 -c400 -d30s http://localhost:8080/video/public/feed
```

---

## � TODO与改进计划

### 🚀 短期改进 (v2.1)

#### 🔧 性能优化
- [ ] **Redis缓存集成**
  - 用户状态缓存
  - 视频元数据缓存
  - API响应缓存
- [ ] **数据库优化**
  - 添加复合索引优化查询
  - 实现读写分离
  - 优化聚合查询性能
- [ ] **文件处理优化**
  - 并行视频转码
  - 增量文件上传
  - 断点续传支持

#### 🛡️ 安全加固
- [ ] **API安全**
  - 实现API限流（Rate Limiting）
  - 添加请求签名验证
  - IP白名单/黑名单
- [ ] **数据安全**
  - 敏感数据加密存储
  - 文件访问权限控制
  - 审计日志记录

#### 📊 监控告警
- [ ] **系统监控**
  - Prometheus + Grafana集成
  - 健康检查端点
  - 性能指标收集
- [ ] **日志管理**
  - ELK Stack集成
  - 结构化日志输出
  - 错误告警机制

### 🌟 中期规划 (v3.0)

#### 🤖 智能化功能
- [ ] **AI/ML集成**
  - 基于用户行为的推荐算法
  - 视频内容自动标签
  - 用户画像分析
- [ ] **自动化运维**
  - 自动扩缩容
  - 故障自愈
  - 性能自优化

#### 🌐 微服务架构
- [ ] **服务拆分**
  - 用户服务独立
  - 视频处理服务独立
  - 统计分析服务独立
- [ ] **服务治理**
  - 服务发现与注册
  - 负载均衡
  - 熔断降级

#### 📱 多端支持
- [ ] **移动端优化**
  - React Native SDK
  - 离线播放支持
  - 推送通知
- [ ] **Web端增强**
  - WebRTC直播支持
  - PWA支持
  - 实时弹幕

### 🔮 长期愿景 (v4.0+)

#### 🌍 国际化
- [ ] **多语言支持**
  - 完整的i18n框架
  - 多时区支持
  - 本地化内容管理

#### 🏢 企业级功能
- [ ] **多租户架构**
  - 租户数据隔离
  - 资源配额管理
  - 计费系统集成
- [ ] **高可用性**
  - 多地域部署
  - 灾备恢复
  - 零停机更新


---

## 🙋 常见问题

### 🔧 安装问题

**Q: 启动时出现 `executable file not found in $PATH` 错误？**
```bash
# 检查FFmpeg安装
which ffmpeg
ffmpeg -version

# 检查Shaka Packager安装
which packager
packager --version

# 如果未安装，请参考环境要求部分
```

**Q: MongoDB连接失败？**
```bash
# 检查MongoDB服务状态
sudo systemctl status mongod

# 检查连接字符串
mongo "mongodb://localhost:27017/realmaster_video"

# 检查防火墙设置
sudo ufw status
```

### 🎬 视频处理问题

**Q: 视频状态一直是 `Pending`？**
- 检查video-worker服务是否运行
- 查看worker日志：`tail -f logs/worker.log`
- 确认MongoDB连接正常
- 检查临时文件目录权限

**Q: 视频处理失败，状态变为 `ProcessingFailed`？**
- 检查FFmpeg和Shaka Packager是否正确安装
- 确认视频文件格式支持
- 检查磁盘空间是否充足
- 查看详细错误日志

### 🚀 部署问题

**Q: Podman部署时容器启动失败？**
```bash
# 查看容器日志
podman logs api
podman logs worker
podman logs mongodb

# 检查Pod状态
podman pod ps
podman ps -a

# 检查配置文件挂载
podman exec -it api cat /app/config.prod.toml

# 检查文件权限
podman exec -it api ls -la /var/www/

# 检查网络连接
podman network ls
podman network inspect realmaster-network
```

**Q: Podman权限问题？**
```bash
# 检查用户命名空间
podman unshare cat /proc/self/uid_map

# 检查存储配置
podman info --debug

# 重置Podman配置（谨慎使用）
podman system reset

# 检查SELinux状态（如果启用）
getenforce
setsebool -P container_manage_cgroup true
```

**Q: Nginx代理配置问题？**
```bash
# 检查Nginx配置语法
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

---

## 📞 支持与联系

### 🐛 问题反馈
- **GitHub Issues**: [提交Bug报告](https://github.com/your-org/realmaster-video-backend/issues)
- **功能请求**: [提交功能建议](https://github.com/your-org/realmaster-video-backend/discussions)

### � 文档资源
- **API文档**: [API.md](./API.md)
- **系统分析**: [SYSTEM_ANALYSIS.md](./SYSTEM_ANALYSIS.md)
- **部署指南**: [DEPLOYMENT.md](./docs/DEPLOYMENT.md)

### 👥 社区
- **技术讨论**: [GitHub Discussions](https://github.com/your-org/realmaster-video-backend/discussions)
- **更新日志**: [CHANGELOG.md](./CHANGELOG.md)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**

Made with ❤️ by RealMaster Team

</div>

