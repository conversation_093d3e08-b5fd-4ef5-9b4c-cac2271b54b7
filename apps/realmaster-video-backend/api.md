<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# api

```go
import "realmaster-video-backend/cmd/api"
```

## Index



# video\-worker

```go
import "realmaster-video-backend/cmd/video-worker"
```

## Index



# config

```go
import "realmaster-video-backend/internal/config"
```

## Index

- [type Config](<#Config>)
  - [func LoadConfig\(\) \(\*Config, error\)](<#LoadConfig>)


<a name="Config"></a>
## type [Config](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/config/config.go#L11-L48>)

Config 应用程序配置结构体

```go
type Config struct {
    Server struct {
        Port     int    `mapstructure:"port"`
        Host     string `mapstructure:"host"`
        DraftDir string `mapstructure:"draft_dir"`
    }   `mapstructure:"server"`

    // golog配置
    Golog struct {
        Dir     string `mapstructure:"dir"`
        Level   string `mapstructure:"level"`
        Verbose string `mapstructure:"verbose"`
        Info    string `mapstructure:"info"`
        Error   string `mapstructure:"error"`
        Format  string `mapstructure:"format"`
    }   `mapstructure:"golog"`

    Media struct {
        ServerURL  string `mapstructure:"server_url"`
        StorageDir string `mapstructure:"storage_dir"`
    }   `mapstructure:"media"`

    Auth struct {
        JWTSecret         string `mapstructure:"jwtSecret"`
        DevMode           bool   `mapstructure:"devMode"`
        DevJWTExpireHours int    `mapstructure:"devJWTExpireHours"`
    }   `mapstructure:"auth"`

    Transaction struct {
        Support bool `mapstructure:"support"`
    }   `mapstructure:"transaction"`

    // 保留用于兼容性，但gomongo会直接从TOML读取数据库配置
    MongoDB struct {
        URI      string `mapstructure:"uri"`
        Database string `mapstructure:"database"`
    }   `mapstructure:"mongodb"`
}
```

<a name="LoadConfig"></a>
### func [LoadConfig](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/config/config.go#L51>)

```go
func LoadConfig() (*Config, error)
```

LoadConfig 从配置文件或环境变量加载配置

# media

```go
import "realmaster-video-backend/internal/media"
```

## Index

- [func HasAudioStream\(ctx context.Context, filePath string\) \(bool, error\)](<#HasAudioStream>)
- [type FFProbeFormat](<#FFProbeFormat>)
- [type FFProbeOutput](<#FFProbeOutput>)
  - [func Probe\(ctx context.Context, filePath string\) \(\*FFProbeOutput, error\)](<#Probe>)
- [type FFProbeStream](<#FFProbeStream>)
- [type Metadata](<#Metadata>)
  - [func GetMetadata\(ctx context.Context, filePath string\) \(\*Metadata, error\)](<#GetMetadata>)
- [type PackageResult](<#PackageResult>)
  - [func Package\(ctx context.Context, transcodeResult \*TranscodeResult\) \(\*PackageResult, error\)](<#Package>)
- [type TranscodeResult](<#TranscodeResult>)
  - [func Transcode\(ctx context.Context, inputPath string\) \(\*TranscodeResult, error\)](<#Transcode>)
- [type TranscodingProfile](<#TranscodingProfile>)
  - [func DefaultProfiles\(\) \[\]TranscodingProfile](<#DefaultProfiles>)


<a name="HasAudioStream"></a>
## func [HasAudioStream](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L76>)

```go
func HasAudioStream(ctx context.Context, filePath string) (bool, error)
```

HasAudioStream uses ffprobe to check if the file has at least one audio stream.

<a name="FFProbeFormat"></a>
## type [FFProbeFormat](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L21-L23>)

FFProbeFormat 定义了 ffprobe JSON 输出中 'format' 对象的结构

```go
type FFProbeFormat struct {
    DurationStr string `json:"duration"`
}
```

<a name="FFProbeOutput"></a>
## type [FFProbeOutput](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L26-L29>)

FFProbeOutput 是 ffprobe 命令完整 JSON 输出的顶层结构

```go
type FFProbeOutput struct {
    Streams []FFProbeStream `json:"streams"`
    Format  FFProbeFormat   `json:"format"`
}
```

<a name="Probe"></a>
### func [Probe](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L92>)

```go
func Probe(ctx context.Context, filePath string) (*FFProbeOutput, error)
```

Probe runs ffprobe on a file and returns the parsed JSON output.

<a name="FFProbeStream"></a>
## type [FFProbeStream](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L14-L18>)

FFProbeStream 定义了 ffprobe JSON 输出中 'streams' 数组的元素结构

```go
type FFProbeStream struct {
    CodecType string `json:"codec_type"`
    Width     int    `json:"width"`
    Height    int    `json:"height"`
}
```

<a name="Metadata"></a>
## type [Metadata](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L32-L37>)

Metadata 是我们希望从 ffprobe 中提取并使用的核心视频元数据

```go
type Metadata struct {
    Duration        time.Duration
    DurationSeconds float64
    Width           int
    Height          int
}
```

<a name="GetMetadata"></a>
### func [GetMetadata](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffprobe.go#L41>)

```go
func GetMetadata(ctx context.Context, filePath string) (*Metadata, error)
```

GetMetadata 使用 ffprobe 命令行工具来提取视频的元数据。 它需要系统中已安装 ffprobe。

<a name="PackageResult"></a>
## type [PackageResult](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/shaka_packager.go#L16-L18>)

PackageResult 包含打包操作的产物

```go
type PackageResult struct {
    HLSManifestPath string
}
```

<a name="Package"></a>
### func [Package](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/shaka_packager.go#L21>)

```go
func Package(ctx context.Context, transcodeResult *TranscodeResult) (*PackageResult, error)
```

Package 使用 Shaka Packager 将转码后的文件打包成 HLS 格式

<a name="TranscodeResult"></a>
## type [TranscodeResult](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffmpeg.go#L36-L41>)

TranscodeResult 包含转码后的文件信息

```go
type TranscodeResult struct {
    AudioOutputPath   string            // 单独的音频文件路径
    VideoOutputs      map[string]string // map[profileName] -> video-only outputPath
    PackagingDir      string            // 用于存放打包后内容的新目录
    ManifestsBaseName string            // MPD和M3U8清单文件的基础名称
}
```

<a name="Transcode"></a>
### func [Transcode](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffmpeg.go#L44>)

```go
func Transcode(ctx context.Context, inputPath string) (*TranscodeResult, error)
```

Transcode 使用 ffmpeg 将输入视频文件转码为单独的音频和多个视频流

<a name="TranscodingProfile"></a>
## type [TranscodingProfile](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffmpeg.go#L17-L23>)

TranscodingProfile 定义了单个转码流的参数

```go
type TranscodingProfile struct {
    Name         string // e.g., "720p"
    Width        int
    Height       int
    VideoBitrate string // e.g., "2000k"
    AudioBitrate string // e.g., "128k"
}
```

<a name="DefaultProfiles"></a>
### func [DefaultProfiles](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/media/ffmpeg.go#L26>)

```go
func DefaultProfiles() []TranscodingProfile
```

DefaultProfiles 返回一个默认的转码配置列表

# server

```go
import "realmaster-video-backend/internal/server"
```

## Index

- [func RegisterAdvertiserRoutes\(router gin.IRouter, handler \*advertiser.Handler\)](<#RegisterAdvertiserRoutes>)
- [func RegisterCategoryRoutes\(router gin.IRouter, handler \*category.Handler\)](<#RegisterCategoryRoutes>)
- [func RegisterMainPropertyRoutes\(router gin.IRouter, propertyHandler \*main\_property.Handler\)](<#RegisterMainPropertyRoutes>)
- [func RegisterVideoRoutes\(router gin.IRouter, handler \*video.Handler\)](<#RegisterVideoRoutes>)


<a name="RegisterAdvertiserRoutes"></a>
## func [RegisterAdvertiserRoutes](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/server/routes.go#L25>)

```go
func RegisterAdvertiserRoutes(router gin.IRouter, handler *advertiser.Handler)
```

RegisterAdvertiserRoutes 注册广告主相关的路由

<a name="RegisterCategoryRoutes"></a>
## func [RegisterCategoryRoutes](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/server/routes.go#L13>)

```go
func RegisterCategoryRoutes(router gin.IRouter, handler *category.Handler)
```

RegisterCategoryRoutes 注册分类相关的路由

<a name="RegisterMainPropertyRoutes"></a>
## func [RegisterMainPropertyRoutes](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/server/routes.go#L37>)

```go
func RegisterMainPropertyRoutes(router gin.IRouter, propertyHandler *main_property.Handler)
```

RegisterMainPropertyRoutes 注册房源相关的路由

<a name="RegisterVideoRoutes"></a>
## func [RegisterVideoRoutes](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/server/routes.go#L47>)

```go
func RegisterVideoRoutes(router gin.IRouter, handler *video.Handler)
```

RegisterVideoRoutes registers routes for the video domain.

# testutil

```go
import "realmaster-video-backend/internal/testutil"
```

## Index

- [func AdvertiserFixture\(\) map\[string\]interface\{\}](<#AdvertiserFixture>)
- [func CategoryFixture\(\) map\[string\]interface\{\}](<#CategoryFixture>)
- [func CreateVideoRequestFixture\(\) map\[string\]interface\{\}](<#CreateVideoRequestFixture>)
- [func RandomBool\(\) bool](<#RandomBool>)
- [func RandomInt\(min, max int\) int](<#RandomInt>)
- [func RandomObjectID\(\) string](<#RandomObjectID>)
- [func RandomString\(length int\) string](<#RandomString>)
- [func VideoFilterFixture\(\) map\[string\]interface\{\}](<#VideoFilterFixture>)
- [func VideoFixture\(\) map\[string\]interface\{\}](<#VideoFixture>)
- [func VideoFixtureWithStatus\(status string\) map\[string\]interface\{\}](<#VideoFixtureWithStatus>)
- [type TestSuite](<#TestSuite>)
  - [func SetupTestSuite\(t \*testing.T\) \*TestSuite](<#SetupTestSuite>)
  - [func \(ts \*TestSuite\) CleanupDatabase\(\)](<#TestSuite.CleanupDatabase>)
  - [func \(ts \*TestSuite\) CreateTempDirs\(\)](<#TestSuite.CreateTempDirs>)


<a name="AdvertiserFixture"></a>
## func [AdvertiserFixture](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L66>)

```go
func AdvertiserFixture() map[string]interface{}
```

AdvertiserFixture 创建测试用的广告主数据

<a name="CategoryFixture"></a>
## func [CategoryFixture](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L57>)

```go
func CategoryFixture() map[string]interface{}
```

CategoryFixture 创建测试用的分类数据

<a name="CreateVideoRequestFixture"></a>
## func [CreateVideoRequestFixture](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L79>)

```go
func CreateVideoRequestFixture() map[string]interface{}
```

CreateVideoRequestFixture 创建测试用的视频创建请求

<a name="RandomBool"></a>
## func [RandomBool](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L139>)

```go
func RandomBool() bool
```

RandomBool 生成随机布尔值

<a name="RandomInt"></a>
## func [RandomInt](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L134>)

```go
func RandomInt(min, max int) int
```

RandomInt 生成指定范围内的随机整数

<a name="RandomObjectID"></a>
## func [RandomObjectID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L119>)

```go
func RandomObjectID() string
```

RandomObjectID 生成随机的ObjectID字符串

<a name="RandomString"></a>
## func [RandomString](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L124>)

```go
func RandomString(length int) string
```

RandomString 生成指定长度的随机字符串

<a name="VideoFilterFixture"></a>
## func [VideoFilterFixture](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L101>)

```go
func VideoFilterFixture() map[string]interface{}
```

VideoFilterFixture 创建测试用的视频过滤器

<a name="VideoFixture"></a>
## func [VideoFixture](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L14>)

```go
func VideoFixture() map[string]interface{}
```

VideoFixture 创建测试用的视频数据 注意：这里不导入video包，避免循环依赖 使用interface\{\}和map\[string\]interface\{\}来构建测试数据

<a name="VideoFixtureWithStatus"></a>
## func [VideoFixtureWithStatus](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/fixtures.go#L46>)

```go
func VideoFixtureWithStatus(status string) map[string]interface{}
```

VideoFixtureWithStatus 创建指定状态的视频数据

<a name="TestSuite"></a>
## type [TestSuite](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/setup.go#L14-L17>)

TestSuite 测试套件，管理测试环境的生命周期

```go
type TestSuite struct {
    Config *config.Config
    T      *testing.T
}
```

<a name="SetupTestSuite"></a>
### func [SetupTestSuite](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/setup.go#L24>)

```go
func SetupTestSuite(t *testing.T) *TestSuite
```

SetupTestSuite 初始化测试环境 这个函数会： 1. 加载测试配置 2. 初始化日志系统 3. 初始化数据库连接

<a name="TestSuite.CleanupDatabase"></a>
### func \(\*TestSuite\) [CleanupDatabase](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/setup.go#L101>)

```go
func (ts *TestSuite) CleanupDatabase()
```

CleanupDatabase 清理测试数据库 在每个测试后调用，确保测试隔离

<a name="TestSuite.CreateTempDirs"></a>
### func \(\*TestSuite\) [CreateTempDirs](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/testutil/setup.go#L129>)

```go
func (ts *TestSuite) CreateTempDirs()
```

CreateTempDirs 创建测试需要的临时目录

# uploader

```go
import "realmaster-video-backend/internal/uploader"
```

## Index

- [type GoUploadService](<#GoUploadService>)
  - [func NewGoUploadService\(site string\) \(\*GoUploadService, error\)](<#NewGoUploadService>)
  - [func \(s \*GoUploadService\) CompleteChunkedVideoUpload\(ctx context.Context, uploadID string, expectedChunks int\) \(\*goupload.UploadResult, error\)](<#GoUploadService.CompleteChunkedVideoUpload>)
  - [func \(s \*GoUploadService\) DeleteFile\(ctx context.Context, entryName, relativePath string\) error](<#GoUploadService.DeleteFile>)
  - [func \(s \*GoUploadService\) DeleteThumbnailDraft\(ctx context.Context, gouploadPath string\) error](<#GoUploadService.DeleteThumbnailDraft>)
  - [func \(s \*GoUploadService\) DeleteThumbnailFinal\(ctx context.Context, gouploadPath string\) error](<#GoUploadService.DeleteThumbnailFinal>)
  - [func \(s \*GoUploadService\) DeleteVideoDraft\(ctx context.Context, gouploadPath string\) error](<#GoUploadService.DeleteVideoDraft>)
  - [func \(s \*GoUploadService\) DeleteVideoFinal\(ctx context.Context, gouploadPath string\) error](<#GoUploadService.DeleteVideoFinal>)
  - [func \(s \*GoUploadService\) GetDraftThumbnailURL\(prefix, gouploadPath, baseURL string\) string](<#GoUploadService.GetDraftThumbnailURL>)
  - [func \(s \*GoUploadService\) GetDraftVideoURL\(prefix, gouploadPath, baseURL string\) string](<#GoUploadService.GetDraftVideoURL>)
  - [func \(s \*GoUploadService\) GetFinalThumbnailURL\(prefix, gouploadPath, baseURL string\) string](<#GoUploadService.GetFinalThumbnailURL>)
  - [func \(s \*GoUploadService\) GetFinalVideoURL\(prefix, gouploadPath, baseURL string\) string](<#GoUploadService.GetFinalVideoURL>)
  - [func \(s \*GoUploadService\) GetPreviewURL\(prefix, gouploadPath, baseURL string\) string](<#GoUploadService.GetPreviewURL>)
  - [func \(s \*GoUploadService\) InitiateChunkedVideoUpload\(ctx context.Context, userID, filename string, totalSize int64\) \(string, error\)](<#GoUploadService.InitiateChunkedVideoUpload>)
  - [func \(s \*GoUploadService\) RenameThumbnailFinal\(ctx context.Context, oldGouploadPath, newGouploadPath string\) error](<#GoUploadService.RenameThumbnailFinal>)
  - [func \(s \*GoUploadService\) RenameVideoDirectory\(ctx context.Context, oldGouploadPath, newGouploadPath string\) error](<#GoUploadService.RenameVideoDirectory>)
  - [func \(s \*GoUploadService\) RenameVideoFinal\(ctx context.Context, oldGouploadPath, newGouploadPath string\) error](<#GoUploadService.RenameVideoFinal>)
  - [func \(s \*GoUploadService\) UpdateThumbnailDraft\(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UpdateThumbnailDraft>)
  - [func \(s \*GoUploadService\) UpdateThumbnailFinal\(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UpdateThumbnailFinal>)
  - [func \(s \*GoUploadService\) UploadChunk\(ctx context.Context, uploadID string, chunkNumber int, chunkReader io.Reader\) error](<#GoUploadService.UploadChunk>)
  - [func \(s \*GoUploadService\) UploadDirectory\(ctx context.Context, entryName, userID, directoryName, localDirPath string\) \(\*goupload.DirectoryUploadResult, error\)](<#GoUploadService.UploadDirectory>)
  - [func \(s \*GoUploadService\) UploadDirectoryToFinal\(ctx context.Context, userID, directoryName, localDirPath string\) \(\*goupload.DirectoryUploadResult, error\)](<#GoUploadService.UploadDirectoryToFinal>)
  - [func \(s \*GoUploadService\) UploadThumbnail\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadThumbnail>)
  - [func \(s \*GoUploadService\) UploadThumbnailDraft\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadThumbnailDraft>)
  - [func \(s \*GoUploadService\) UploadThumbnailFinal\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadThumbnailFinal>)
  - [func \(s \*GoUploadService\) UploadVideo\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadVideo>)
  - [func \(s \*GoUploadService\) UploadVideoDraft\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadVideoDraft>)
  - [func \(s \*GoUploadService\) UploadVideoFinal\(ctx context.Context, userID string, reader io.Reader, filename string, size int64\) \(\*goupload.UploadResult, error\)](<#GoUploadService.UploadVideoFinal>)
- [type UploadResult](<#UploadResult>)
- [type Uploader](<#Uploader>)


<a name="GoUploadService"></a>
## type [GoUploadService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L53-L61>)

GoUploadService 封装 goupload 功能

```go
type GoUploadService struct {
    // contains filtered or unexported fields
}
```

<a name="NewGoUploadService"></a>
### func [NewGoUploadService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L64>)

```go
func NewGoUploadService(site string) (*GoUploadService, error)
```

NewGoUploadService 创建新的 goupload 服务实例

<a name="GoUploadService.CompleteChunkedVideoUpload"></a>
### func \(\*GoUploadService\) [CompleteChunkedVideoUpload](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L240>)

```go
func (s *GoUploadService) CompleteChunkedVideoUpload(ctx context.Context, uploadID string, expectedChunks int) (*goupload.UploadResult, error)
```

CompleteChunkedVideoUpload 完成分块视频上传

<a name="GoUploadService.DeleteFile"></a>
### func \(\*GoUploadService\) [DeleteFile](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L274>)

```go
func (s *GoUploadService) DeleteFile(ctx context.Context, entryName, relativePath string) error
```

DeleteFile 删除文件

<a name="GoUploadService.DeleteThumbnailDraft"></a>
### func \(\*GoUploadService\) [DeleteThumbnailDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L514>)

```go
func (s *GoUploadService) DeleteThumbnailDraft(ctx context.Context, gouploadPath string) error
```

DeleteThumbnailDraft 删除草稿缩略图文件

<a name="GoUploadService.DeleteThumbnailFinal"></a>
### func \(\*GoUploadService\) [DeleteThumbnailFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L524>)

```go
func (s *GoUploadService) DeleteThumbnailFinal(ctx context.Context, gouploadPath string) error
```

DeleteThumbnailFinal 删除最终缩略图文件

<a name="GoUploadService.DeleteVideoDraft"></a>
### func \(\*GoUploadService\) [DeleteVideoDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L509>)

```go
func (s *GoUploadService) DeleteVideoDraft(ctx context.Context, gouploadPath string) error
```

DeleteVideoDraft 删除草稿视频文件

<a name="GoUploadService.DeleteVideoFinal"></a>
### func \(\*GoUploadService\) [DeleteVideoFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L519>)

```go
func (s *GoUploadService) DeleteVideoFinal(ctx context.Context, gouploadPath string) error
```

DeleteVideoFinal 删除最终视频文件

<a name="GoUploadService.GetDraftThumbnailURL"></a>
### func \(\*GoUploadService\) [GetDraftThumbnailURL](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L554>)

```go
func (s *GoUploadService) GetDraftThumbnailURL(prefix, gouploadPath, baseURL string) string
```

GetDraftThumbnailURL 获取草稿缩略图预览URL 注意：现在需要传入prefix参数，从UploadResult中获取

<a name="GoUploadService.GetDraftVideoURL"></a>
### func \(\*GoUploadService\) [GetDraftVideoURL](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L548>)

```go
func (s *GoUploadService) GetDraftVideoURL(prefix, gouploadPath, baseURL string) string
```

GetDraftVideoURL 获取草稿视频预览URL 注意：现在需要传入prefix参数，从UploadResult中获取

<a name="GoUploadService.GetFinalThumbnailURL"></a>
### func \(\*GoUploadService\) [GetFinalThumbnailURL](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L566>)

```go
func (s *GoUploadService) GetFinalThumbnailURL(prefix, gouploadPath, baseURL string) string
```

GetFinalThumbnailURL 获取最终缩略图预览URL 注意：现在需要传入prefix参数，从UploadResult中获取

<a name="GoUploadService.GetFinalVideoURL"></a>
### func \(\*GoUploadService\) [GetFinalVideoURL](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L560>)

```go
func (s *GoUploadService) GetFinalVideoURL(prefix, gouploadPath, baseURL string) string
```

GetFinalVideoURL 获取最终视频预览URL 注意：现在需要传入prefix参数，从UploadResult中获取

<a name="GoUploadService.GetPreviewURL"></a>
### func \(\*GoUploadService\) [GetPreviewURL](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L532>)

```go
func (s *GoUploadService) GetPreviewURL(prefix, gouploadPath, baseURL string) string
```

GetPreviewURL 根据goupload路径和prefix生成预览URL 注意：现在使用UploadResult中的prefix字段，而不是硬编码

<a name="GoUploadService.InitiateChunkedVideoUpload"></a>
### func \(\*GoUploadService\) [InitiateChunkedVideoUpload](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L184>)

```go
func (s *GoUploadService) InitiateChunkedVideoUpload(ctx context.Context, userID, filename string, totalSize int64) (string, error)
```

InitiateChunkedVideoUpload 初始化分块视频上传

<a name="GoUploadService.RenameThumbnailFinal"></a>
### func \(\*GoUploadService\) [RenameThumbnailFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L579>)

```go
func (s *GoUploadService) RenameThumbnailFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error
```

RenameThumbnailFinal 重命名最终缩略图文件（用于下架/重新发布）

<a name="GoUploadService.RenameVideoDirectory"></a>
### func \(\*GoUploadService\) [RenameVideoDirectory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L585>)

```go
func (s *GoUploadService) RenameVideoDirectory(ctx context.Context, oldGouploadPath, newGouploadPath string) error
```

RenameVideoDirectory 重命名视频目录（包含所有m3u8文件和视频片段） 这个方法用于视频下架时重命名整个视频处理后的目录

<a name="GoUploadService.RenameVideoFinal"></a>
### func \(\*GoUploadService\) [RenameVideoFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L574>)

```go
func (s *GoUploadService) RenameVideoFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error
```

RenameVideoFinal 重命名最终视频文件（用于下架/重新发布） 通过复制文件到新路径，然后删除旧文件来实现重命名

<a name="GoUploadService.UpdateThumbnailDraft"></a>
### func \(\*GoUploadService\) [UpdateThumbnailDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L775>)

```go
func (s *GoUploadService) UpdateThumbnailDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string) (*goupload.UploadResult, error)
```

UpdateThumbnailDraft 更新草稿缩略图（上传新文件后删除旧文件）

<a name="GoUploadService.UpdateThumbnailFinal"></a>
### func \(\*GoUploadService\) [UpdateThumbnailFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L811>)

```go
func (s *GoUploadService) UpdateThumbnailFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string) (*goupload.UploadResult, error)
```

UpdateThumbnailFinal 更新最终缩略图（上传新文件后删除旧文件）

<a name="GoUploadService.UploadChunk"></a>
### func \(\*GoUploadService\) [UploadChunk](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L220>)

```go
func (s *GoUploadService) UploadChunk(ctx context.Context, uploadID string, chunkNumber int, chunkReader io.Reader) error
```

UploadChunk 上传文件分块

<a name="GoUploadService.UploadDirectory"></a>
### func \(\*GoUploadService\) [UploadDirectory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L429>)

```go
func (s *GoUploadService) UploadDirectory(ctx context.Context, entryName, userID, directoryName, localDirPath string) (*goupload.DirectoryUploadResult, error)
```

UploadDirectory 上传整个目录到最终位置（供Worker使用）

<a name="GoUploadService.UploadDirectoryToFinal"></a>
### func \(\*GoUploadService\) [UploadDirectoryToFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L424>)

```go
func (s *GoUploadService) UploadDirectoryToFinal(ctx context.Context, userID, directoryName, localDirPath string) (*goupload.DirectoryUploadResult, error)
```

UploadDirectoryToFinal 上传整个目录到最终位置（供Worker使用）

<a name="GoUploadService.UploadThumbnail"></a>
### func \(\*GoUploadService\) [UploadThumbnail](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L147>)

```go
func (s *GoUploadService) UploadThumbnail(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadThumbnail 上传缩略图文件

<a name="GoUploadService.UploadThumbnailDraft"></a>
### func \(\*GoUploadService\) [UploadThumbnailDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L142>)

```go
func (s *GoUploadService) UploadThumbnailDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadThumbnailDraft 上传缩略图文件到草稿目录（别名方法）

<a name="GoUploadService.UploadThumbnailFinal"></a>
### func \(\*GoUploadService\) [UploadThumbnailFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L385>)

```go
func (s *GoUploadService) UploadThumbnailFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadThumbnailFinal 上传缩略图文件到最终目录

<a name="GoUploadService.UploadVideo"></a>
### func \(\*GoUploadService\) [UploadVideo](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L100>)

```go
func (s *GoUploadService) UploadVideo(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadVideo 上传视频文件

<a name="GoUploadService.UploadVideoDraft"></a>
### func \(\*GoUploadService\) [UploadVideoDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L137>)

```go
func (s *GoUploadService) UploadVideoDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadVideoDraft 上传视频文件到草稿目录（别名方法）

<a name="GoUploadService.UploadVideoFinal"></a>
### func \(\*GoUploadService\) [UploadVideoFinal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/goupload_service.go#L348>)

```go
func (s *GoUploadService) UploadVideoFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error)
```

UploadVideoFinal 上传视频文件到最终目录

<a name="UploadResult"></a>
## type [UploadResult](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/uploader.go#L8-L15>)

UploadResult 包含单个文件上传后的信息

```go
type UploadResult struct {
    // E.g., "my_video_1080p.mp4"
    FileName string
    // E.g., "http://files.example.com/videos/video123/my_video_1080p.mp4"
    URL string
    // E.g., "/videos/video123/my_video_1080p.mp4"
    Path string
}
```

<a name="Uploader"></a>
## type [Uploader](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/uploader/uploader.go#L19-L29>)

Uploader 定义了上传器的通用接口 这允许我们轻松地替换不同的实现 \(e.g., HTTP, S3, GCS\)

```go
type Uploader interface {
    // UploadDirectory 遍历一个本地目录，并将其中所有文件上传到目标服务器的指定子目录中
    // sourcePath: 本地要上传的文件夹路径 (e.g., "/tmp/..._packaged")
    // targetSubDir: 在远程服务器上创建的子目录，通常是视频ID，用于隔离文件
    UploadDirectory(ctx context.Context, sourcePath string, targetSubDir string) ([]UploadResult, error)

    // UploadFile 上传单个文件
    // sourceFilePath: 本地要上传的单个文件的路径
    // targetSubDir: 远程子目录
    UploadFile(ctx context.Context, sourceFilePath string, targetSubDir string) (*UploadResult, error)
}
```

# advertiser

```go
import "realmaster-video-backend/internal/domain/advertiser"
```

## Index

- [Variables](<#variables>)
- [type Advertiser](<#Advertiser>)
- [type CreateAdvertiserRequest](<#CreateAdvertiserRequest>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service, cfg \*config.Config\) \*Handler](<#NewHandler>)
  - [func \(h \*Handler\) Create\(c \*gin.Context\)](<#Handler.Create>)
  - [func \(h \*Handler\) Delete\(c \*gin.Context\)](<#Handler.Delete>)
  - [func \(h \*Handler\) GetByID\(c \*gin.Context\)](<#Handler.GetByID>)
  - [func \(h \*Handler\) List\(c \*gin.Context\)](<#Handler.List>)
  - [func \(h \*Handler\) Update\(c \*gin.Context\)](<#Handler.Update>)
- [type ListAdvertisersRequest](<#ListAdvertisersRequest>)
- [type ListAdvertisersResponseData](<#ListAdvertisersResponseData>)
- [type MergeDeleteAdvertiserRequest](<#MergeDeleteAdvertiserRequest>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type Service](<#Service>)
  - [func NewService\(repo Repository, videoRepo video.Repository, cfg \*config.Config\) Service](<#NewService>)
- [type UpdateAdvertiserRequest](<#UpdateAdvertiserRequest>)


## Variables

<a name="ErrAdvertiserNotFound"></a>

```go
var (
    ErrAdvertiserNotFound       = errors.New("广告主不存在")
    ErrInvalidAdvertiserID      = errors.New("无效的广告主ID")
    ErrEmailExists              = errors.New("电子邮箱已被使用")
    ErrPhoneExists              = errors.New("电话号码已被使用")
    ErrNameExists               = errors.New("广告主名称已被使用")
    ErrInvalidEmailFormat       = errors.New("无效的电子邮箱格式")
    ErrInvalidPhoneFormat       = errors.New("无效的电话号码格式")
    ErrCannotDeleteLast         = errors.New("无法删除唯一的广告主")
    ErrMergeToSelf              = errors.New("不能将广告主合并到其自身")
    ErrTargetAdvertiserNotFound = errors.New("目标广告主不存在")
)
```

<a name="Advertiser"></a>
## type [Advertiser](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L10-L21>)

Advertiser 表示广告主信息

```go
type Advertiser struct {
    ID        primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`              // 广告主ID
    Name      string             `bson:"nm" json:"nm"`                                   // 广告主名称
    AvatarURL string             `bson:"avatarUrl,omitempty" json:"avatarUrl,omitempty"` // 头像URL
    Phone     string             `bson:"ph" json:"ph"`                                   // 电话号码
    Email     string             `bson:"em" json:"em"`                                   // 电子邮箱
    MAppUID   string             `bson:"mUid" json:"mUid"`                               // 主App用户ID
    Remark    string             `bson:"rem" json:"rem"`                                 // 备注信息

}
```

<a name="CreateAdvertiserRequest"></a>
## type [CreateAdvertiserRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L24-L30>)

CreateAdvertiserRequest 创建广告主的请求结构

```go
type CreateAdvertiserRequest struct {
    Name    string `json:"nm" binding:"required"`        // 广告主名称
    Phone   string `json:"ph"`                           // 电话号码
    Email   string `json:"em" binding:"omitempty,email"` // 电子邮箱
    MAppUID string `json:"mUid"`                         // 主App用户ID
    Remark  string `json:"rem"`                          // 备注信息
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L17-L20>)

Handler 处理广告主相关的 HTTP 请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L23>)

```go
func NewHandler(service Service, cfg *config.Config) *Handler
```

NewHandler 创建一个新的广告主处理器实例

<a name="Handler.Create"></a>
### func \(\*Handler\) [Create](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L115>)

```go
func (h *Handler) Create(c *gin.Context)
```

Create 创建一个新广告主 @Summary 创建广告主 @Description 创建一个新的广告主，支持上传头像 @Tags advertisers @Accept multipart/form\-data @Produce json @Param avatar formData file false "广告主头像文件" @Param nm formData string true "广告主名称" @Param ph formData string false "电话号码" @Param em formData string false "电子邮箱" @Param mUid formData string false "主App用户ID" @Param rem formData string false "备注信息" @Success 201 \{object\} common.APIResponse\{data=Advertiser\} @Failure 400 \{object\} common.APIResponse @Failure 409 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/advertisers \[post\]

<a name="Handler.Delete"></a>
### func \(\*Handler\) [Delete](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L279>)

```go
func (h *Handler) Delete(c *gin.Context)
```

Delete 删除一个广告主，并将其视频合并到另一个广告主 @Summary 合并并删除广告主 @Description 将一个广告主的所有视频转移到另一个广告主后，删除该广告主。如果系统中只有一个广告主，则此操作会失败。 @Tags advertisers @Accept json @Produce json @Param id path string true "要删除的广告主ID" @Param mergeRequest body MergeDeleteAdvertiserRequest true "合并目标" @Success 200 \{object\} common.APIResponse @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/advertisers/\{id\} \[delete\]

<a name="Handler.GetByID"></a>
### func \(\*Handler\) [GetByID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L78>)

```go
func (h *Handler) GetByID(c *gin.Context)
```

GetByID 获取单个广告主详情 @Summary 获取单个广告主详情 @Description 根据ID获取单个广告主的详细信息 @Tags advertisers @Accept json @Produce json @Param id path string true "广告主ID" @Success 200 \{object\} common.APIResponse\{data=Advertiser\} @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/advertisers/\{id\} \[get\]

<a name="Handler.List"></a>
### func \(\*Handler\) [List](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L50>)

```go
func (h *Handler) List(c *gin.Context)
```

List 获取广告主列表 @Summary 获取广告主列表 @Description 获取广告主列表，支持分页和模糊搜索 @Tags advertisers @Accept json @Produce json @Param page query int false "页码" default\(1\) @Param limit query int false "每页数量" default\(10\) @Param name query string false "按名称模糊搜索" @Param phone query string false "按电话精确搜索" @Param email query string false "按邮箱精确搜索" @Success 200 \{object\} common.APIResponse\{data=ListAdvertisersResponseData\} @Failure 400 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/advertisers \[get\]

<a name="Handler.Update"></a>
### func \(\*Handler\) [Update](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/handler.go#L191>)

```go
func (h *Handler) Update(c *gin.Context)
```

Update 更新一个广告主 @Summary 更新广告主 @Description 更新一个已存在的广告主信息，支持部分更新和头像上传 @Tags advertisers @Accept multipart/form\-data @Produce json @Param id path string true "广告主ID" @Param avatar formData file false "新的广告主头像文件" @Param nm formData string false "广告主名称" @Param ph formData string false "电话号码" @Param em formData string false "电子邮箱" @Param mUid formData string false "主App用户ID" @Param rem formData string false "备注信息" @Success 200 \{object\} common.APIResponse @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 409 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/advertisers/\{id\} \[patch\]

<a name="ListAdvertisersRequest"></a>
## type [ListAdvertisersRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L51-L57>)

ListAdvertisersRequest 定义了查询广告主列表的请求参数

```go
type ListAdvertisersRequest struct {
    Name  string `form:"nm"`    // 按名称模糊查询
    Phone string `form:"ph"`    // 按电话精确查询
    Email string `form:"em"`    // 按邮箱精确查询
    Page  int64  `form:"page"`  // 页码
    Limit int64  `form:"limit"` // 每页数量
}
```

<a name="ListAdvertisersResponseData"></a>
## type [ListAdvertisersResponseData](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L43-L46>)

ListAdvertisersResponseData 是广告主列表响应中 'data' 字段的结构

```go
type ListAdvertisersResponseData struct {
    Items      []Advertiser       `json:"items"`
    Pagination *common.Pagination `json:"pgn"`
}
```

<a name="MergeDeleteAdvertiserRequest"></a>
## type [MergeDeleteAdvertiserRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L60-L62>)

MergeDeleteAdvertiserRequest 定义了合并删除广告主的请求体

```go
type MergeDeleteAdvertiserRequest struct {
    TargetAdvertiserID string `json:"target_advertiser_id" binding:"required"` // 视频要合并到的目标广告主ID
}
```

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/repository.go#L17-L29>)

Repository 定义了广告主的数据访问接口

```go
type Repository interface {
    Find(ctx context.Context, filter primitive.M, page, limit int64) ([]Advertiser, error)
    FindByID(ctx context.Context, id string) (*Advertiser, error)
    Count(ctx context.Context, filter primitive.M) (int64, error)
    Create(ctx context.Context, advertiser *Advertiser) error
    Update(ctx context.Context, id string, updateData primitive.M) error
    Delete(ctx context.Context, id string) error
    ExistsByID(ctx context.Context, id string) (bool, error)
    ExistsByEmail(ctx context.Context, email string) (bool, error)
    ExistsByPhone(ctx context.Context, phone string) (bool, error)
    ExistsByEmailAndNotID(ctx context.Context, email, id string) (bool, error)
    ExistsByPhoneAndNotID(ctx context.Context, phone, id string) (bool, error)
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/repository.go#L36>)

```go
func NewRepository() Repository
```

NewRepository 创建一个新的广告主仓库实例

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/service.go#L40-L46>)

Service 定义了广告主相关的业务逻辑接口

```go
type Service interface {
    List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error)
    GetByID(ctx context.Context, id string) (*Advertiser, error)
    Create(ctx context.Context, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error)
    Update(ctx context.Context, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error
    Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/service.go#L55>)

```go
func NewService(repo Repository, videoRepo video.Repository, cfg *config.Config) Service
```

NewService 创建一个新的广告主服务实例

<a name="UpdateAdvertiserRequest"></a>
## type [UpdateAdvertiserRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/advertiser/model.go#L33-L40>)

UpdateAdvertiserRequest 更新广告主的请求结构

```go
type UpdateAdvertiserRequest struct {
    Name      *string `json:"nm,omitempty"`                           // 广告主名称
    AvatarURL *string `json:"avatarUrl,omitempty"`                    // 头像URL
    Phone     *string `json:"ph,omitempty"`                           // 电话号码
    Email     *string `json:"em,omitempty" binding:"omitempty,email"` // 电子邮箱
    MAppUID   *string `json:"mUid,omitempty"`                         // 主App用户ID
    Remark    *string `json:"rem,omitempty"`                          // 备注信息
}
```

# category

```go
import "realmaster-video-backend/internal/domain/category"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [type Category](<#Category>)
  - [func NewCategory\(name string, order int\) \*Category](<#NewCategory>)
  - [func \(c \*Category\) TableName\(\) string](<#Category.TableName>)
- [type CreateCategoryRequest](<#CreateCategoryRequest>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service\) \*Handler](<#NewHandler>)
  - [func \(h \*Handler\) CreateCategory\(c \*gin.Context\)](<#Handler.CreateCategory>)
  - [func \(h \*Handler\) DeleteCategory\(c \*gin.Context\)](<#Handler.DeleteCategory>)
  - [func \(h \*Handler\) ListCategories\(c \*gin.Context\)](<#Handler.ListCategories>)
  - [func \(h \*Handler\) UpdateCategory\(c \*gin.Context\)](<#Handler.UpdateCategory>)
- [type ListCategoriesRequest](<#ListCategoriesRequest>)
- [type ListCategoriesResponseData](<#ListCategoriesResponseData>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type Service](<#Service>)
  - [func NewService\(repo Repository, videoRepo video.Repository\) Service](<#NewService>)
- [type UpdateCategoryRequest](<#UpdateCategoryRequest>)


## Constants

<a name="NoneCategoryName"></a>

```go
const (
    NoneCategoryName = "None"
)
```

## Variables

<a name="ErrCategoryNotFound"></a>

```go
var (
    ErrCategoryNotFound         = errors.New("分类不存在")
    ErrInvalidCategoryID        = errors.New("无效的分类ID")
    ErrCategoryNameExists       = errors.New("分类名已存在")
    ErrCannotDeleteNoneCategory = errors.New("不能删除 'None' 分类")
    ErrCategoryNameRequired     = errors.New("分类名称不能为空")
    ErrCategoryNameTooLong      = errors.New("分类名称不能超过50个字符")
    ErrUpdatePayloadRequired    = errors.New("没有提供需要更新的信息")
)
```

<a name="Category"></a>
## type [Category](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L10-L17>)

Category 表示视频分类模型

```go
type Category struct {
    ID    primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
    Name  string             `bson:"nm" json:"name"`
    Order int                `bson:"ord" json:"order"`
}
```

<a name="NewCategory"></a>
### func [NewCategory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L44>)

```go
func NewCategory(name string, order int) *Category
```

NewCategory 创建一个新的分类实例

<a name="Category.TableName"></a>
### func \(\*Category\) [TableName](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L53>)

```go
func (c *Category) TableName() string
```

TableName 返回集合名称

<a name="CreateCategoryRequest"></a>
## type [CreateCategoryRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L20-L23>)

CreateCategoryRequest 创建分类的请求结构体

```go
type CreateCategoryRequest struct {
    Name  string `json:"name" binding:"required"` // 使用binding:"required"让Gin进行基础的必填校验
    Order *int   `json:"order"`                   // 使用指针类型，以便区分 "未提供" 和 "值为0" 的情况
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L15-L17>)

Handler 处理分类相关的 HTTP 请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L20>)

```go
func NewHandler(service Service) *Handler
```

NewHandler 创建一个新的分类处理器实例

<a name="Handler.CreateCategory"></a>
### func \(\*Handler\) [CreateCategory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L70>)

```go
func (h *Handler) CreateCategory(c *gin.Context)
```

CreateCategory 创建新的分类 @Summary 创建新的分类 @Description 创建一个新的视频分类 @Tags categories @Accept json @Produce json @Param category body CreateCategoryRequest true "分类信息" @Success 201 \{object\} common.APIResponse\{data=Category\} @Failure 400 \{object\} common.APIResponse @Failure 409 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/categories \[post\]

<a name="Handler.DeleteCategory"></a>
### func \(\*Handler\) [DeleteCategory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L155>)

```go
func (h *Handler) DeleteCategory(c *gin.Context)
```

DeleteCategory 删除指定ID的分类 @Summary 删除分类 @Description 删除指定ID的视频分类 @Tags categories @Accept json @Produce json @Param id path string true "分类ID" @Success 200 \{object\} common.APIResponse @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 409 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/categories/\{id\} \[delete\]

<a name="Handler.ListCategories"></a>
### func \(\*Handler\) [ListCategories](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L42>)

```go
func (h *Handler) ListCategories(c *gin.Context)
```

ListCategories 获取所有分类列表 @Summary 获取所有分类列表 @Description 获取所有视频分类，按排序字段升序排列 @Tags categories @Accept json @Produce json @Param page query int false "页码" default\(1\) @Param limit query int false "每页数量" default\(10\) @Success 200 \{object\} common.APIResponse\{data=ListCategoriesResponseData\} @Failure 400 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/categories \[get\]

<a name="Handler.UpdateCategory"></a>
### func \(\*Handler\) [UpdateCategory](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/handler.go#L114>)

```go
func (h *Handler) UpdateCategory(c *gin.Context)
```

UpdateCategory 更新指定ID的分类 @Summary 更新分类 @Description 更新指定ID的视频分类 @Tags categories @Accept json @Produce json @Param id path string true "分类ID" @Param category body UpdateCategoryRequest true "要更新的分类信息" @Success 200 \{object\} common.APIResponse @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 409 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /api/categories/\{id\} \[put\]

<a name="ListCategoriesRequest"></a>
## type [ListCategoriesRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L38-L41>)

ListCategoriesRequest 定义了查询分类列表的请求参数

```go
type ListCategoriesRequest struct {
    Page  int64 `form:"page"`  // 页码
    Limit int64 `form:"limit"` // 每页数量
}
```

<a name="ListCategoriesResponseData"></a>
## type [ListCategoriesResponseData](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L32-L35>)

ListCategoriesResponseData 是分类列表响应中 'data' 字段的结构

```go
type ListCategoriesResponseData struct {
    Items      []Category         `json:"items"`
    Pagination *common.Pagination `json:"pgn"`
}
```

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/repository.go#L18-L29>)

Repository 定义了分类数据访问接口

```go
type Repository interface {
    FindAll(ctx context.Context, page, limit int64) ([]Category, error)
    FindByID(ctx context.Context, id string) (*Category, error)
    FindByName(ctx context.Context, name string) (*Category, error)
    Create(ctx context.Context, category *Category) error
    Delete(ctx context.Context, id string) error
    ExistsByID(ctx context.Context, id string) (bool, error)
    IsInUse(ctx context.Context, id string) (bool, error)
    Update(ctx context.Context, id string, updateData primitive.M) error
    ExistsByNameAndNotID(ctx context.Context, name, id string) (bool, error)
    Count(ctx context.Context, filter primitive.M) (int64, error)
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/repository.go#L38>)

```go
func NewRepository() Repository
```

NewRepository 创建一个新的分类仓库实例

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/service.go#L29-L35>)

Service 定义分类相关的业务逻辑接口

```go
type Service interface {
    Create(ctx context.Context, req CreateCategoryRequest) (*Category, error)
    List(ctx context.Context, req ListCategoriesRequest) (*ListCategoriesResponseData, error)
    Delete(ctx context.Context, id string) error
    Update(ctx context.Context, id string, req UpdateCategoryRequest) error
    EnsureNoneCategoryExists(ctx context.Context) error
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/service.go#L44>)

```go
func NewService(repo Repository, videoRepo video.Repository) Service
```

NewService 创建一个新的分类服务实例

<a name="UpdateCategoryRequest"></a>
## type [UpdateCategoryRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/category/model.go#L26-L29>)

UpdateCategoryRequest 更新分类的请求结构体

```go
type UpdateCategoryRequest struct {
    Name  *string `json:"name"`  // 使用指针，允许部分更新
    Order *int    `json:"order"` // 使用指针，允许部分更新
}
```

# common

```go
import "realmaster-video-backend/internal/domain/common"
```

## Index

- [Variables](<#variables>)
- [func CleanupEmptyDirectories\(dirPath string, rootDir string\)](<#CleanupEmptyDirectories>)
- [func IsValidEmail\(email string\) bool](<#IsValidEmail>)
- [func IsValidObjectID\(id string\) bool](<#IsValidObjectID>)
- [func IsValidPhone\(phone string\) bool](<#IsValidPhone>)
- [func SendAppError\(c \*gin.Context, appErr \*AppError\)](<#SendAppError>)
- [func SendError\(c \*gin.Context, status int, errMessage, devMessage string\)](<#SendError>)
- [func SendSuccess\(c \*gin.Context, status int, data interface\{\}, msg ...string\)](<#SendSuccess>)
- [type APIResponse](<#APIResponse>)
- [type AppError](<#AppError>)
  - [func NewConflictError\(code, message string, err error\) \*AppError](<#NewConflictError>)
  - [func NewInternalError\(code, message string, err error\) \*AppError](<#NewInternalError>)
  - [func NewNotFoundError\(code, message string, err error\) \*AppError](<#NewNotFoundError>)
  - [func NewValidationError\(code, message string, err error\) \*AppError](<#NewValidationError>)
  - [func \(e \*AppError\) Error\(\) string](<#AppError.Error>)
  - [func \(e \*AppError\) Unwrap\(\) error](<#AppError.Unwrap>)
- [type ErrorType](<#ErrorType>)
- [type Pagination](<#Pagination>)


## Variables

<a name="ErrInvalidRequest"></a>通用错误类型

```go
var (
    // 验证错误
    ErrInvalidRequest      = errors.New("无效的请求参数")
    ErrInvalidID           = errors.New("无效的ID格式")
    ErrInvalidCategoryID   = errors.New("无效的分类ID格式")
    ErrInvalidClientID     = errors.New("无效的客户ID格式")
    ErrInvalidAdvertiserID = errors.New("无效的广告主ID格式")
    ErrInvalidTimeFormat   = errors.New("无效的时间格式")
    ErrInvalidFileFormat   = errors.New("无效的文件格式")

    // 资源错误
    ErrResourceNotFound = errors.New("资源未找到")
    ErrResourceExists   = errors.New("资源已存在")
    ErrResourceInUse    = errors.New("资源正在使用中")

    // 权限错误
    ErrUnauthorized = errors.New("未授权访问")
    ErrForbidden    = errors.New("禁止访问")

    // 业务逻辑错误
    ErrInvalidOperation = errors.New("无效的操作")
    ErrOperationFailed  = errors.New("操作失败")
    ErrInvalidStatus    = errors.New("无效的状态")

    // 文件操作错误
    ErrFileNotFound     = errors.New("文件未找到")
    ErrFileUploadFailed = errors.New("文件上传失败")
    ErrFileSaveFailed   = errors.New("文件保存失败")
    ErrFileDeleteFailed = errors.New("文件删除失败")

    // 数据库错误
    ErrDatabaseOperation = errors.New("数据库操作失败")
    ErrDuplicateKey      = errors.New("重复的键值")

    // 外部服务错误
    ErrExternalService = errors.New("外部服务错误")
    ErrNetworkTimeout  = errors.New("网络超时")
)
```

<a name="CleanupEmptyDirectories"></a>
## func [CleanupEmptyDirectories](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/utils.go#L20>)

```go
func CleanupEmptyDirectories(dirPath string, rootDir string)
```

CleanupEmptyDirectories 递归清理空目录 从给定路径开始向上清理，直到遇到非空目录或到达根目录

<a name="IsValidEmail"></a>
## func [IsValidEmail](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/validation.go#L8>)

```go
func IsValidEmail(email string) bool
```

IsValidEmail 检查邮箱格式是否有效

<a name="IsValidObjectID"></a>
## func [IsValidObjectID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/utils.go#L13>)

```go
func IsValidObjectID(id string) bool
```

IsValidObjectID 检查字符串是否为有效的MongoDB ObjectID

<a name="IsValidPhone"></a>
## func [IsValidPhone](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/validation.go#L15>)

```go
func IsValidPhone(phone string) bool
```

IsValidPhone 检查电话号码格式是否为10位数字

<a name="SendAppError"></a>
## func [SendAppError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/response.go#L33>)

```go
func SendAppError(c *gin.Context, appErr *AppError)
```

SendAppError 发送应用程序错误响应

<a name="SendError"></a>
## func [SendError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/response.go#L24>)

```go
func SendError(c *gin.Context, status int, errMessage, devMessage string)
```

SendError sends a standardized error response and logs the detailed error.

<a name="SendSuccess"></a>
## func [SendSuccess](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/response.go#L11>)

```go
func SendSuccess(c *gin.Context, status int, data interface{}, msg ...string)
```

SendSuccess sends a standardized success response.

<a name="APIResponse"></a>
## type [APIResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/model.go#L4-L9>)

APIResponse 是所有API响应的基础结构

```go
type APIResponse struct {
    OK   int         `json:"ok"`
    Msg  string      `json:"msg,omitempty"`
    Err  string      `json:"err,omitempty"`
    Data interface{} `json:"data,omitempty"`
}
```

<a name="AppError"></a>
## type [AppError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L59-L65>)

AppError 应用程序错误结构

```go
type AppError struct {
    Type    ErrorType `json:"type"`
    Code    string    `json:"code"`
    Message string    `json:"message"`
    Details string    `json:"details,omitempty"`
    Err     error     `json:"-"`
}
```

<a name="NewConflictError"></a>
### func [NewConflictError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L99>)

```go
func NewConflictError(code, message string, err error) *AppError
```

NewConflictError 创建冲突错误

<a name="NewInternalError"></a>
### func [NewInternalError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L109>)

```go
func NewInternalError(code, message string, err error) *AppError
```

NewInternalError 创建内部错误

<a name="NewNotFoundError"></a>
### func [NewNotFoundError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L89>)

```go
func NewNotFoundError(code, message string, err error) *AppError
```

NewNotFoundError 创建资源未找到错误

<a name="NewValidationError"></a>
### func [NewValidationError](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L79>)

```go
func NewValidationError(code, message string, err error) *AppError
```

NewValidationError 创建验证错误

<a name="AppError.Error"></a>
### func \(\*AppError\) [Error](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L67>)

```go
func (e *AppError) Error() string
```



<a name="AppError.Unwrap"></a>
### func \(\*AppError\) [Unwrap](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L74>)

```go
func (e *AppError) Unwrap() error
```



<a name="ErrorType"></a>
## type [ErrorType](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/errors.go#L46>)

ErrorType 错误类型枚举

```go
type ErrorType string
```

<a name="ErrorTypeValidation"></a>

```go
const (
    ErrorTypeValidation   ErrorType = "validation"
    ErrorTypeNotFound     ErrorType = "not_found"
    ErrorTypeConflict     ErrorType = "conflict"
    ErrorTypeInternal     ErrorType = "internal"
    ErrorTypeExternal     ErrorType = "external"
    ErrorTypeUnauthorized ErrorType = "unauthorized"
    ErrorTypeForbidden    ErrorType = "forbidden"
)
```

<a name="Pagination"></a>
## type [Pagination](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/common/model.go#L12-L17>)

Pagination 包含了标准的分页信息

```go
type Pagination struct {
    TotalItems  int64 `json:"totItms"`
    TotalPages  int64 `json:"totPgs"`
    CurrentPage int64 `json:"currPg"`
    Limit       int64 `json:"lim"`
}
```

# interaction

```go
import "realmaster-video-backend/internal/domain/interaction"
```

## Index

- [type BlockVideoMeta](<#BlockVideoMeta>)
- [type ClickListingLinkMeta](<#ClickListingLinkMeta>)
- [type CreateInteractionRequest](<#CreateInteractionRequest>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service\) \*Handler](<#NewHandler>)
  - [func \(h \*Handler\) CreateInteraction\(c \*gin.Context\)](<#Handler.CreateInteraction>)
  - [func \(h \*Handler\) GetUserInteractions\(c \*gin.Context\)](<#Handler.GetUserInteractions>)
  - [func \(h \*Handler\) GetVideoInteractions\(c \*gin.Context\)](<#Handler.GetVideoInteractions>)
- [type Interaction](<#Interaction>)
- [type InteractionResponse](<#InteractionResponse>)
- [type InteractionType](<#InteractionType>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type Service](<#Service>)
  - [func NewService\(interactionRepo Repository, stateRepo state.Repository, videoRepo video.Repository, txManager \*database.TransactionManager\) Service](<#NewService>)
- [type ShareMeta](<#ShareMeta>)
- [type ViewCompleteMeta](<#ViewCompleteMeta>)
- [type ViewProgressMeta](<#ViewProgressMeta>)


<a name="BlockVideoMeta"></a>
## type [BlockVideoMeta](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L62-L64>)

BlockVideoMeta block\_video 事件的元数据

```go
type BlockVideoMeta struct {
    Reason string `json:"rsn" bson:"rsn"` // 屏蔽原因
}
```

<a name="ClickListingLinkMeta"></a>
## type [ClickListingLinkMeta](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L57-L59>)

ClickListingLinkMeta click\_listing\_link 事件的元数据

```go
type ClickListingLinkMeta struct {
    ListingID primitive.ObjectID `json:"lId" bson:"lId"` // 房源ID
}
```

<a name="CreateInteractionRequest"></a>
## type [CreateInteractionRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L67-L71>)

CreateInteractionRequest 创建交互事件的请求

```go
type CreateInteractionRequest struct {
    VideoID string                 `json:"videoId" binding:"required"`
    Type    InteractionType        `json:"type" binding:"required"`
    Meta    map[string]interface{} `json:"meta,omitempty"`
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/handler.go#L15-L17>)

Handler 处理交互相关的HTTP请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/handler.go#L20>)

```go
func NewHandler(service Service) *Handler
```

NewHandler 创建新的交互处理器

<a name="Handler.CreateInteraction"></a>
### func \(\*Handler\) [CreateInteraction](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/handler.go#L28>)

```go
func (h *Handler) CreateInteraction(c *gin.Context)
```

CreateInteraction 创建交互事件 POST /video/public/interactions

<a name="Handler.GetUserInteractions"></a>
### func \(\*Handler\) [GetUserInteractions](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/handler.go#L83>)

```go
func (h *Handler) GetUserInteractions(c *gin.Context)
```

GetUserInteractions 获取用户交互历史 GET /video/public/interactions/user

<a name="Handler.GetVideoInteractions"></a>
### func \(\*Handler\) [GetVideoInteractions](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/handler.go#L123>)

```go
func (h *Handler) GetVideoInteractions(c *gin.Context)
```

GetVideoInteractions 获取视频交互历史 GET /video/public/interactions/video/:videoId

<a name="Interaction"></a>
## type [Interaction](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L31-L38>)

Interaction 视频交互事件日志模型

```go
type Interaction struct {
    ID        primitive.ObjectID     `bson:"_id,omitempty" json:"id,omitempty"`
    UID       primitive.ObjectID     `bson:"uid" json:"userId"`                    // 用户ID
    VID       primitive.ObjectID     `bson:"vid" json:"videoId"`                   // 视频ID
    Type      InteractionType        `bson:"tp" json:"type"`                       // 交互类型
    Meta      map[string]interface{} `bson:"meta,omitempty" json:"meta,omitempty"` // 元数据
    Timestamp time.Time              `bson:"_ts" json:"timestamp"`                 // 时间戳
}
```

<a name="InteractionResponse"></a>
## type [InteractionResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L74-L81>)

InteractionResponse 交互事件的响应

```go
type InteractionResponse struct {
    ID        primitive.ObjectID     `json:"id"`
    UserID    primitive.ObjectID     `json:"userId"`
    VideoID   primitive.ObjectID     `json:"videoId"`
    Type      InteractionType        `json:"type"`
    Meta      map[string]interface{} `json:"meta,omitempty"`
    Timestamp time.Time              `json:"timestamp"`
}
```

<a name="InteractionType"></a>
## type [InteractionType](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L10>)

InteractionType 定义交互类型的枚举

```go
type InteractionType string
```

<a name="TypeViewStart"></a>

```go
const (
    // 观看相关事件
    TypeViewStart    InteractionType = "view_start"
    TypeViewProgress InteractionType = "view_progress"
    TypeViewComplete InteractionType = "view_complete"

    // 用户行为事件
    TypeLike       InteractionType = "like"
    TypeUnlike     InteractionType = "unlike"
    TypeFavorite   InteractionType = "favorite"
    TypeUnfavorite InteractionType = "unfavorite"
    TypeShare      InteractionType = "share"

    // 其他事件
    TypeClickListingLink InteractionType = "click_listing_link"
    TypeBlockVideo       InteractionType = "block_video"
)
```

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/repository.go#L17-L22>)

Repository 定义交互事件的数据访问接口

```go
type Repository interface {
    Create(ctx context.Context, interaction *Interaction) error
    FindByUser(ctx context.Context, userID primitive.ObjectID, limit int64) ([]Interaction, error)
    FindByVideo(ctx context.Context, videoID primitive.ObjectID, limit int64) ([]Interaction, error)
    CountByType(ctx context.Context, videoID primitive.ObjectID, interactionType InteractionType) (int64, error)
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/repository.go#L29>)

```go
func NewRepository() Repository
```

NewRepository 创建新的交互事件仓库实例

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/service.go#L17-L21>)

Service 定义交互服务接口

```go
type Service interface {
    CreateInteraction(ctx context.Context, userID string, req CreateInteractionRequest) (*InteractionResponse, error)
    GetUserInteractions(ctx context.Context, userID string, limit int) ([]InteractionResponse, error)
    GetVideoInteractions(ctx context.Context, videoID string, limit int) ([]InteractionResponse, error)
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/service.go#L31>)

```go
func NewService(interactionRepo Repository, stateRepo state.Repository, videoRepo video.Repository, txManager *database.TransactionManager) Service
```

NewService 创建新的交互服务实例

<a name="ShareMeta"></a>
## type [ShareMeta](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L52-L54>)

ShareMeta share 事件的元数据

```go
type ShareMeta struct {
    Platform string `json:"plat" bson:"plat"` // 分享平台
}
```

<a name="ViewCompleteMeta"></a>
## type [ViewCompleteMeta](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L46-L49>)

ViewCompleteMeta view\_complete 事件的元数据

```go
type ViewCompleteMeta struct {
    CompletionPercent    float64 `json:"compPct" bson:"compPct"`         // 完成百分比
    TotalDurationSeconds int     `json:"totalDurSec" bson:"totalDurSec"` // 总时长（秒）
}
```

<a name="ViewProgressMeta"></a>
## type [ViewProgressMeta](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/interaction/model.go#L41-L43>)

ViewProgressMeta view\_progress 事件的元数据

```go
type ViewProgressMeta struct {
    ProgressSeconds int `json:"progSec" bson:"progSec"` // 观看进度（秒）
}
```

# main\_property

```go
import "realmaster-video-backend/internal/domain/main_property"
```

## Index

- [Variables](<#variables>)
- [type BatchGetRequest](<#BatchGetRequest>)
- [type ExternalAPIResponse](<#ExternalAPIResponse>)
- [type ExternalProperty](<#ExternalProperty>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service\) \*Handler](<#NewHandler>)
  - [func \(h \*Handler\) GetByID\(c \*gin.Context\)](<#Handler.GetByID>)
  - [func \(h \*Handler\) GetByIDs\(c \*gin.Context\)](<#Handler.GetByIDs>)
  - [func \(h \*Handler\) SearchByKeyword\(c \*gin.Context\)](<#Handler.SearchByKeyword>)
- [type MainProperty](<#MainProperty>)
  - [func Transform\(extProp \*ExternalProperty\) \*MainProperty](<#Transform>)
- [type NullableFloat](<#NullableFloat>)
  - [func \(nf NullableFloat\) String\(\) string](<#NullableFloat.String>)
  - [func \(nf \*NullableFloat\) UnmarshalJSON\(data \[\]byte\) error](<#NullableFloat.UnmarshalJSON>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type SearchByKeywordRequest](<#SearchByKeywordRequest>)
- [type Service](<#Service>)
  - [func NewService\(repo Repository\) Service](<#NewService>)


## Variables

<a name="ErrPropertyNotFound"></a>

```go
var (
    ErrPropertyNotFound    = errors.New("房源未找到")
    ErrInvalidPropertyID   = errors.New("无效的房源ID格式")
    ErrKeywordRequired     = errors.New("搜索关键词不能为空")
    ErrPropertyIDsRequired = errors.New("房源ID列表不能为空")
)
```

<a name="BatchGetRequest"></a>
## type [BatchGetRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L182-L184>)

BatchGetRequest 定义了通过ID批量获取多个房源的请求体

```go
type BatchGetRequest struct {
    IDs []string `json:"ids" binding:"required"`
}
```

<a name="ExternalAPIResponse"></a>
## type [ExternalAPIResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L55-L58>)

ExternalAPIResponse 用于解码外部API响应的顶层结构

```go
type ExternalAPIResponse struct {
    Ok   int                `json:"ok"`
    List []ExternalProperty `json:"l"`
}
```

<a name="ExternalProperty"></a>
## type [ExternalProperty](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L62-L88>)

ExternalProperty 用于解码来自外部API列表中的房源对象 只包含我们需要提取的字段

```go
type ExternalProperty struct {
    ID         string `json:"_id"`
    SearchAddr string `json:"searchAddr"`
    Price      string `json:"priceValStrRed"`
    ThumbURL   string `json:"thumbUrl"`
    SaleOrRent string `json:"saleOrRent"`
    City       string `json:"city_en"`
    Province   string `json:"prov_en"`
    Community  string `json:"cmty"`
    WebURL     string `json:"webUrl"`

    // 用于计算卧室的字段
    RmBdr  string        `json:"rmbdrm"`
    Bdrms  NullableFloat `json:"bdrms"`
    TBdrms NullableFloat `json:"tbdrms"`
    BrPlus NullableFloat `json:"br_plus"`

    // 用于计算卫生间的字段
    RmBthrm NullableFloat `json:"rmbthrm"`
    TBthrms NullableFloat `json:"tbthrms"`
    Bthrms  NullableFloat `json:"bthrms"`

    // 用于计算停车位的字段
    RmGr NullableFloat `json:"rmgr"`
    TGr  NullableFloat `json:"tgr"`
    Gr   NullableFloat `json:"gr"`
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/handler.go#L12-L14>)

Handler 处理房源相关的HTTP请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/handler.go#L17>)

```go
func NewHandler(service Service) *Handler
```

NewHandler 创建一个新的房源处理器实例

<a name="Handler.GetByID"></a>
### func \(\*Handler\) [GetByID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/handler.go#L95>)

```go
func (h *Handler) GetByID(c *gin.Context)
```

GetByID 处理通过ID获取单个房源的请求 @Summary 通过ID获取单个房源 @Description 根据ID获取单个房源的信息 @Tags properties @Accept json @Produce json @Param id path string true "房源ID" @Success 200 \{object\} common.APIResponse\{data=MainProperty\} @Failure 400 \{object\} common.APIResponse @Failure 404 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /video/admin/properties/\{id\} \[get\]

<a name="Handler.GetByIDs"></a>
### func \(\*Handler\) [GetByIDs](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/handler.go#L63>)

```go
func (h *Handler) GetByIDs(c *gin.Context)
```

GetByIDs 处理通过ID批量获取房源的请求 @Summary 通过ID批量获取多个房源 @Description 根据提供的ID列表获取房源列表 @Tags properties @Accept json @Produce json @Param ids body BatchGetRequest true "房源ID列表" @Success 200 \{object\} common.APIResponse\{data=\[\]MainProperty\} @Failure 400 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /video/admin/properties/batch\-get \[post\]

<a name="Handler.SearchByKeyword"></a>
### func \(\*Handler\) [SearchByKeyword](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/handler.go#L32>)

```go
func (h *Handler) SearchByKeyword(c *gin.Context)
```

SearchByKeyword 处理通过关键词搜索房源的请求 @Summary 通过关键词搜索房源 @Description 使用请求体中的关键词搜索房源 @Tags properties @Accept json @Produce json @Param search body SearchByKeywordRequest true "搜索关键词" @Success 200 \{object\} common.APIResponse\{data=\[\]MainProperty\} @Failure 400 \{object\} common.APIResponse @Failure 500 \{object\} common.APIResponse @Router /video/admin/properties/search \[post\]

<a name="MainProperty"></a>
## type [MainProperty](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L91-L104>)

MainProperty 是我们应用内部使用的、干净的房源结构

```go
type MainProperty struct {
    ID         string `json:"id"`
    SearchAddr string `json:"searchAddr"`
    Price      string `json:"price"`
    ThumbURL   string `json:"thumbUrl"`
    SaleOrRent string `json:"saleOrRent"`
    City       string `json:"city"`
    Province   string `json:"prov"`
    Community  string `json:"cmty"`
    WebURL     string `json:"webUrl"`
    Bedroom    string `json:"bedroom"`
    Bathroom   string `json:"bathroom"`
    Parking    string `json:"parking"`
}
```

<a name="Transform"></a>
### func [Transform](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L107>)

```go
func Transform(extProp *ExternalProperty) *MainProperty
```

Transform 将外部房源数据转换为内部房源模型，并执行计算逻辑

<a name="NullableFloat"></a>
## type [NullableFloat](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L9-L12>)

NullableFloat可以处理来自JSON的数字或"N/A"字符串。

```go
type NullableFloat struct {
    Value float64
    Valid bool // 如果值是数字而不是"N/A"，则为true
}
```

<a name="NullableFloat.String"></a>
### func \(NullableFloat\) [String](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L46>)

```go
func (nf NullableFloat) String() string
```

String返回数字的字符串表示形式，如果无效则返回空字符串。

<a name="NullableFloat.UnmarshalJSON"></a>
### func \(\*NullableFloat\) [UnmarshalJSON](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L15>)

```go
func (nf *NullableFloat) UnmarshalJSON(data []byte) error
```

UnmarshalJSON为NullableFloat实现自定义解组逻辑。

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/repository.go#L20-L24>)

Repository 定义了房源数据的访问接口

```go
type Repository interface {
    SearchByQuery(ctx context.Context, query string) ([]MainProperty, error)
    SearchByID(ctx context.Context, id string) (*MainProperty, error)
    SearchByIDs(ctx context.Context, ids []string) ([]MainProperty, error)
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/repository.go#L31>)

```go
func NewRepository() Repository
```

NewRepository 创建一个新的房源仓库实例

<a name="SearchByKeywordRequest"></a>
## type [SearchByKeywordRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/model.go#L177-L179>)

SearchByKeywordRequest 定义了通过关键词搜索房源的请求体

```go
type SearchByKeywordRequest struct {
    Keyword string `json:"s" binding:"required"`
}
```

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/service.go#L16-L20>)

Service 定义了房源相关的业务逻辑接口

```go
type Service interface {
    SearchByKeyword(ctx context.Context, keyword string) ([]MainProperty, error)
    GetByID(ctx context.Context, id string) (*MainProperty, error)
    GetByIDs(ctx context.Context, ids []string) ([]MainProperty, error)
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/main_property/service.go#L27>)

```go
func NewService(repo Repository) Service
```

NewService 创建一个新的房源服务实例

# public

```go
import "realmaster-video-backend/internal/domain/public"
```

## Index

- [type FeedResponse](<#FeedResponse>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service\) \*Handler](<#NewHandler>)
  - [func \(h \*Handler\) GetFeed\(c \*gin.Context\)](<#Handler.GetFeed>)
  - [func \(h \*Handler\) GetUserFavorites\(c \*gin.Context\)](<#Handler.GetUserFavorites>)
  - [func \(h \*Handler\) GetUserStates\(c \*gin.Context\)](<#Handler.GetUserStates>)
  - [func \(h \*Handler\) GetUserStatesQuery\(c \*gin.Context\)](<#Handler.GetUserStatesQuery>)
- [type PaginationResponse](<#PaginationResponse>)
- [type Service](<#Service>)
  - [func NewService\(videoRepo video.Repository, stateRepo state.Repository, cfg \*config.Config\) Service](<#NewService>)
- [type UserFavoritesResponse](<#UserFavoritesResponse>)


<a name="FeedResponse"></a>
## type [FeedResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/service.go#L42-L45>)

FeedResponse Feed接口的响应结构

```go
type FeedResponse struct {
    Videos     []video.VideoResponse `json:"videos"`
    Pagination PaginationResponse    `json:"pagination"`
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L17-L19>)

Handler 处理公共API相关的HTTP请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L22>)

```go
func NewHandler(service Service) *Handler
```

NewHandler 创建新的公共API处理器

<a name="Handler.GetFeed"></a>
### func \(\*Handler\) [GetFeed](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L30>)

```go
func (h *Handler) GetFeed(c *gin.Context)
```

GetFeed 获取视频Feed GET /video/public/feed

<a name="Handler.GetUserFavorites"></a>
### func \(\*Handler\) [GetUserFavorites](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L192>)

```go
func (h *Handler) GetUserFavorites(c *gin.Context)
```

GetUserFavorites 获取用户收藏 GET /video/public/favorites

<a name="Handler.GetUserStates"></a>
### func \(\*Handler\) [GetUserStates](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L77>)

```go
func (h *Handler) GetUserStates(c *gin.Context)
```

GetUserStates 批量获取用户状态 POST /video/public/states/batch

<a name="Handler.GetUserStatesQuery"></a>
### func \(\*Handler\) [GetUserStatesQuery](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/handler.go#L144>)

```go
func (h *Handler) GetUserStatesQuery(c *gin.Context)
```

GetUserStatesQuery 通过查询参数批量获取用户状态 GET /video/public/states?videoIds=id1,id2,id3

<a name="PaginationResponse"></a>
## type [PaginationResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/service.go#L54-L59>)

PaginationResponse 分页响应结构

```go
type PaginationResponse struct {
    TotalItems   int64 `json:"totalItems"`
    TotalPages   int64 `json:"totalPages"`
    CurrentPage  int64 `json:"currentPage"`
    ItemsPerPage int64 `json:"itemsPerPage"`
}
```

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/service.go#L20-L24>)

Service 定义公共API服务接口

```go
type Service interface {
    GetFeed(ctx context.Context, userID string, page, limit int) (*FeedResponse, error)
    GetUserStates(ctx context.Context, userID string, videoIDs []string) ([]state.StateResponse, error)
    GetUserFavorites(ctx context.Context, userID string, page, limit int) (*UserFavoritesResponse, error)
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/service.go#L33>)

```go
func NewService(videoRepo video.Repository, stateRepo state.Repository, cfg *config.Config) Service
```

NewService 创建新的公共API服务实例

<a name="UserFavoritesResponse"></a>
## type [UserFavoritesResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/public/service.go#L48-L51>)

UserFavoritesResponse 用户收藏响应结构

```go
type UserFavoritesResponse struct {
    Favorites  []state.FavoriteVideoResponse `json:"favorites"`
    Pagination PaginationResponse            `json:"pagination"`
}
```

# state

```go
import "realmaster-video-backend/internal/domain/state"
```

## Index

- [type BatchStateQuery](<#BatchStateQuery>)
- [type FavoriteVideoResponse](<#FavoriteVideoResponse>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type State](<#State>)
  - [func NewState\(userID, videoID primitive.ObjectID\) \*State](<#NewState>)
- [type StateID](<#StateID>)
- [type StateResponse](<#StateResponse>)
- [type UserFavoritesQuery](<#UserFavoritesQuery>)


<a name="BatchStateQuery"></a>
## type [BatchStateQuery](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L42-L45>)

BatchStateQuery 批量查询状态的请求

```go
type BatchStateQuery struct {
    UserID   string   `json:"userId" binding:"required"`
    VideoIDs []string `json:"videoIds" binding:"required"`
}
```

<a name="FavoriteVideoResponse"></a>
## type [FavoriteVideoResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L67-L72>)

FavoriteVideoResponse 收藏视频响应

```go
type FavoriteVideoResponse struct {
    VideoID         primitive.ObjectID `json:"videoId"`
    ProgressSeconds int                `json:"progressSeconds"`
}
```

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/repository.go#L17-L23>)

Repository 定义状态的数据访问接口

```go
type Repository interface {
    Upsert(ctx context.Context, state *State) error
    FindByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) (*State, error)
    FindBatchByUser(ctx context.Context, userID primitive.ObjectID, videoIDs []primitive.ObjectID) ([]State, error)
    FindUserFavorites(ctx context.Context, userID primitive.ObjectID, page, limit int64) ([]State, int64, error)
    DeleteByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) error
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/repository.go#L30>)

```go
func NewRepository() Repository
```

NewRepository 创建新的状态仓库实例

<a name="State"></a>
## type [State](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L16-L24>)

State 用户视频状态快照模型

```go
type State struct {
    ID              StateID `bson:"_id" json:"id"`                  // 复合主键
    Liked           bool    `bson:"liked" json:"liked"`             // 是否点赞 (默认: false)
    Favorited       bool    `bson:"faved" json:"favorited"`         // 是否收藏 (默认: false)
    Blocked         bool    `bson:"blkd" json:"blocked"`            // 是否屏蔽 (默认: false)
    ProgressSeconds int     `bson:"progSec" json:"progressSeconds"` // 观看进度（秒） (默认: 0)

}
```

<a name="NewState"></a>
### func [NewState](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L27>)

```go
func NewState(userID, videoID primitive.ObjectID) *State
```

NewState 创建一个具有默认值的新State

<a name="StateID"></a>
## type [StateID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L10-L13>)

StateID 复合主键结构

```go
type StateID struct {
    UserID  primitive.ObjectID `bson:"uid" json:"userId"`  // 用户ID
    VideoID primitive.ObjectID `bson:"vid" json:"videoId"` // 视频ID
}
```

<a name="StateResponse"></a>
## type [StateResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L48-L57>)

StateResponse 状态响应

```go
type StateResponse struct {
    UserID          primitive.ObjectID `json:"userId"`
    VideoID         primitive.ObjectID `json:"videoId"`
    Liked           bool               `json:"liked"`
    Favorited       bool               `json:"favorited"`
    Blocked         bool               `json:"blocked"`
    ProgressSeconds int                `json:"progressSeconds"`
}
```

<a name="UserFavoritesQuery"></a>
## type [UserFavoritesQuery](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/state/model.go#L60-L64>)

UserFavoritesQuery 用户收藏查询

```go
type UserFavoritesQuery struct {
    UserID string `json:"userId" binding:"required"`
    Page   int    `json:"page,omitempty"`
    Limit  int    `json:"limit,omitempty"`
}
```

# video

```go
import "realmaster-video-backend/internal/domain/video"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [type CreateVideoRequest](<#CreateVideoRequest>)
- [type Handler](<#Handler>)
  - [func NewHandler\(service Service, draftDir string\) \(\*Handler, error\)](<#NewHandler>)
  - [func \(h \*Handler\) CompleteChunkedUpload\(c \*gin.Context\)](<#Handler.CompleteChunkedUpload>)
  - [func \(h \*Handler\) CreateDraft\(c \*gin.Context\)](<#Handler.CreateDraft>)
  - [func \(h \*Handler\) DeleteVideo\(c \*gin.Context\)](<#Handler.DeleteVideo>)
  - [func \(h \*Handler\) FindVideos\(c \*gin.Context\)](<#Handler.FindVideos>)
  - [func \(h \*Handler\) GetByID\(c \*gin.Context\)](<#Handler.GetByID>)
  - [func \(h \*Handler\) GetVideoStats\(c \*gin.Context\)](<#Handler.GetVideoStats>)
  - [func \(h \*Handler\) InitiateChunkedUpload\(c \*gin.Context\)](<#Handler.InitiateChunkedUpload>)
  - [func \(h \*Handler\) PublishVideo\(c \*gin.Context\)](<#Handler.PublishVideo>)
  - [func \(h \*Handler\) UpdateVideo\(c \*gin.Context\)](<#Handler.UpdateVideo>)
  - [func \(h \*Handler\) UpdateVideoStats\(c \*gin.Context\)](<#Handler.UpdateVideoStats>)
  - [func \(h \*Handler\) UploadChunk\(c \*gin.Context\)](<#Handler.UploadChunk>)
  - [func \(h \*Handler\) UploadThumbnail\(c \*gin.Context\)](<#Handler.UploadThumbnail>)
- [type MultilingualString](<#MultilingualString>)
- [type Repository](<#Repository>)
  - [func NewRepository\(\) Repository](<#NewRepository>)
- [type Service](<#Service>)
  - [func NewService\(repo Repository, cfg \*config.Config\) Service](<#NewService>)
- [type UpdateStatsRequest](<#UpdateStatsRequest>)
- [type UpdateVideoRequest](<#UpdateVideoRequest>)
- [type Video](<#Video>)
- [type VideoFilter](<#VideoFilter>)
- [type VideoResponse](<#VideoResponse>)
- [type VideoStats](<#VideoStats>)
- [type VideoStatsSummary](<#VideoStatsSummary>)


## Constants

<a name="StatusDraft"></a>视频处理状态常量

```go
const (
    StatusDraft            = "Draft"
    StatusPending          = "Pending"
    StatusProcessing       = "Processing"
    StatusPublished        = "Published"
    StatusProcessingFailed = "ProcessingFailed"
    StatusUnpublished      = "Unpublished"
)
```

## Variables

<a name="ErrVideoAlreadyPublished"></a>

```go
var (
    ErrVideoAlreadyPublished     = errors.New("视频已被发布")
    ErrInvalidVideoStatus        = errors.New("视频状态不正确，无法执行操作")
    ErrVideoInProcessing         = errors.New("无法修改正在处理中的视频")
    ErrCannotDeleteProcessing    = errors.New("无法删除正在处理中的视频，请稍后再试")
    ErrInvalidVideoID            = errors.New("无效的视频ID格式")
    ErrVideoFileRequired         = errors.New("视频文件不能为空")
    ErrThumbnailRequired         = errors.New("封面图片不能为空")
    ErrInvalidRepublishOperation = errors.New("无效的操作: 'republish' 动作必须与新上传的视频文件一起使用")
)
```

<a name="ErrVideoNotFound"></a>

```go
var (
    ErrVideoNotFound = errors.New("视频未找到")
)
```

<a name="CreateVideoRequest"></a>
## type [CreateVideoRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L102-L114>)

CreateVideoRequest 定义了创建新视频草稿的结构体 Handler 会从 metadata JSON 字段中解析此结构

```go
type CreateVideoRequest struct {
    Title                  MultilingualString `json:"title"`
    Description            MultilingualString `json:"description"`
    UploaderID             string             `json:"uploaderId"`
    CategoryID             string             `json:"categoryId"`
    Tags                   []string           `json:"tags"`
    PropertyIDs            []string           `json:"propertyIds"`
    ExternalURL            string             `json:"externalUrl"`
    ClientID               string             `json:"clientId"`
    DraftVideoGouploadPath string             `json:"-"` // goupload返回的相对路径
    DraftThumbGouploadPath string             `json:"-"` // goupload返回的相对路径
    PublishNow             bool               `json:"-"` // 由 handler 手动填充，不来自JSON
}
```

<a name="Handler"></a>
## type [Handler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L20-L24>)

Handler 处理视频相关的HTTP请求

```go
type Handler struct {
    // contains filtered or unexported fields
}
```

<a name="NewHandler"></a>
### func [NewHandler](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L28>)

```go
func NewHandler(service Service, draftDir string) (*Handler, error)
```

NewHandler 创建一个新的视频处理器实例 需要一个用于存储草稿文件的目录路径

<a name="Handler.CompleteChunkedUpload"></a>
### func \(\*Handler\) [CompleteChunkedUpload](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L665>)

```go
func (h *Handler) CompleteChunkedUpload(c *gin.Context)
```

CompleteChunkedUpload 完成分块上传

<a name="Handler.CreateDraft"></a>
### func \(\*Handler\) [CreateDraft](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L51>)

```go
func (h *Handler) CreateDraft(c *gin.Context)
```

CreateDraft 处理新视频草稿的创建，支持传统上传和分块上传两种模式

<a name="Handler.DeleteVideo"></a>
### func \(\*Handler\) [DeleteVideo](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L461>)

```go
func (h *Handler) DeleteVideo(c *gin.Context)
```

DeleteVideo handles the request to delete a video.

<a name="Handler.FindVideos"></a>
### func \(\*Handler\) [FindVideos](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L288>)

```go
func (h *Handler) FindVideos(c *gin.Context)
```

FindVideos handles the request to get a list of videos with filters.

<a name="Handler.GetByID"></a>
### func \(\*Handler\) [GetByID](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L435>)

```go
func (h *Handler) GetByID(c *gin.Context)
```

GetByID handles the request to get a single video by its ID. @Summary Get video by ID @Description Retrieves detailed information for a single video. @Tags videos @Accept json @Produce json @Param id path string true "Video ID" @Success 200 \{object\} common.APIResponse\{data=VideoResponse\} @Failure 400 \{object\} common.APIResponse "Invalid ID format" @Failure 404 \{object\} common.APIResponse "Video not found" @Failure 500 \{object\} common.APIResponse "Internal server error" @Router /api/video/admin/videos/\{id\} \[get\]

<a name="Handler.GetVideoStats"></a>
### func \(\*Handler\) [GetVideoStats](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L365>)

```go
func (h *Handler) GetVideoStats(c *gin.Context)
```

GetVideoStats handles the request to get aggregated statistics for videos based on filters.

<a name="Handler.InitiateChunkedUpload"></a>
### func \(\*Handler\) [InitiateChunkedUpload](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L596>)

```go
func (h *Handler) InitiateChunkedUpload(c *gin.Context)
```

InitiateChunkedUpload 初始化分块上传

<a name="Handler.PublishVideo"></a>
### func \(\*Handler\) [PublishVideo](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L235>)

```go
func (h *Handler) PublishVideo(c *gin.Context)
```

PublishVideo 处理发布视频的请求

<a name="Handler.UpdateVideo"></a>
### func \(\*Handler\) [UpdateVideo](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L482>)

```go
func (h *Handler) UpdateVideo(c *gin.Context)
```

UpdateVideo handles PATCH requests to update a video.

<a name="Handler.UpdateVideoStats"></a>
### func \(\*Handler\) [UpdateVideoStats](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L260>)

```go
func (h *Handler) UpdateVideoStats(c *gin.Context)
```

UpdateVideoStats 处理更新视频统计数据的请求

<a name="Handler.UploadChunk"></a>
### func \(\*Handler\) [UploadChunk](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L632>)

```go
func (h *Handler) UploadChunk(c *gin.Context)
```

UploadChunk 上传文件分块

<a name="Handler.UploadThumbnail"></a>
### func \(\*Handler\) [UploadThumbnail](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/handler.go#L192>)

```go
func (h *Handler) UploadThumbnail(c *gin.Context)
```

UploadThumbnail 处理单独的缩略图上传

<a name="MultilingualString"></a>
## type [MultilingualString](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L22-L25>)

MultilingualString 定义了支持多语言的字符串结构

```go
type MultilingualString struct {
    Zh  string `bson:"zh" json:"zh"`
    En  string `bson:"en,omitempty" json:"en,omitempty"`
}
```

<a name="Repository"></a>
## type [Repository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/repository.go#L23-L36>)

Repository 定义了视频的数据访问接口

```go
type Repository interface {
    Create(ctx context.Context, video *Video) error
    FindByID(ctx context.Context, id string) (*Video, error)
    Update(ctx context.Context, video *Video) error
    FindOneAndUpdateToProcessing(ctx context.Context) (*Video, error)
    UpdateCategory(ctx context.Context, oldCategoryID, newCategoryID primitive.ObjectID) error
    UpdateAdvertiser(ctx context.Context, oldAdvertiserID, newAdvertiserID primitive.ObjectID) (int64, error)
    IncrementStats(ctx context.Context, videoID string, views, likes, collections, completions int64) error
    Find(ctx context.Context, filter VideoFilter) ([]Video, int64, error)
    DeleteByID(ctx context.Context, id primitive.ObjectID) error
    UpdateSelective(ctx context.Context, id primitive.ObjectID, updateData bson.M) error
    UpdateSelectiveWithStatusCheck(ctx context.Context, id primitive.ObjectID, expectedStatus string, updateData bson.M) error
    GetAggregatedStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error)
}
```

<a name="NewRepository"></a>
### func [NewRepository](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/repository.go#L43>)

```go
func NewRepository() Repository
```

NewRepository 创建一个新的视频仓库实例

<a name="Service"></a>
## type [Service](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/service.go#L36-L46>)

Service 定义了视频相关的业务逻辑接口

```go
type Service interface {
    CreateDraft(ctx context.Context, req CreateVideoRequest) (*Video, error)
    PublishVideo(ctx context.Context, videoID string) error
    GetVideoByID(ctx context.Context, videoID string) (*VideoResponse, error)
    UpdateStats(ctx context.Context, videoID string, req UpdateStatsRequest) error
    FindVideos(ctx context.Context, filter VideoFilter) ([]VideoResponse, *common.Pagination, error)
    GetVideoStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error)
    DeleteVideo(ctx context.Context, id string) error
    UpdateVideo(ctx context.Context, req UpdateVideoRequest) (*Video, error)
}
```

<a name="NewService"></a>
### func [NewService](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/service.go#L55>)

```go
func NewService(repo Repository, cfg *config.Config) Service
```

NewService 创建一个新的视频服务实例

<a name="UpdateStatsRequest"></a>
## type [UpdateStatsRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/service.go#L80-L85>)

UpdateStatsRequest 定义了更新视频统计数据的请求结构 使用指针类型以支持部分更新，客户端可以只发送需要增加的字段

```go
type UpdateStatsRequest struct {
    Views       *int64 `json:"views"`
    Likes       *int64 `json:"likes"`
    Collections *int64 `json:"collections"`
    Completions *int64 `json:"completions"`
}
```

<a name="UpdateVideoRequest"></a>
## type [UpdateVideoRequest](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L131-L140>)

UpdateVideoRequest defines the structure for updating a video. It supports both metadata and file updates.

```go
type UpdateVideoRequest struct {
    ID                 primitive.ObjectID
    Metadata           map[string]interface{}
    Action             string                // e.g., "publish", "unpublish", "save_as_draft"
    DraftVideoFile     *multipart.FileHeader // For direct multipart uploads
    DraftThumbnailFile *multipart.FileHeader // For direct multipart uploads
    // Fields for post-chunked upload
    DraftVideoGouploadPath string
    DraftThumbGouploadPath string
}
```

<a name="Video"></a>
## type [Video](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L37-L63>)

Video 是数据库中的核心视频模型

```go
type Video struct {
    ID          primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
    Title       MultilingualString `bson:"tl" json:"title"`
    Description MultilingualString `bson:"dsc,omitempty" json:"description,omitempty"`

    // === goupload路径字段 - 统一文件管理 ===
    DraftVideoGouploadPath string `bson:"draftVideoGouploadPath,omitempty" json:"-"`
    DraftThumbGouploadPath string `bson:"draftThumbGouploadPath,omitempty" json:"-"`
    FinalVideoGouploadPath string `bson:"finalVideoGouploadPath,omitempty" json:"-"`
    FinalThumbGouploadPath string `bson:"finalThumbGouploadPath,omitempty" json:"-"`
    ManifestsBaseName      string `bson:"manifestsBaseName,omitempty" json:"-"` // Base name for manifest and mp4 files

    Duration        float64            `bson:"dur" json:"duration"`
    UploaderID      string             `bson:"uldId" json:"uploaderId"`
    CategoryID      primitive.ObjectID `bson:"catId,omitempty" json:"categoryId,omitempty"`
    Tags            []string           `bson:"tags,omitempty" json:"tags,omitempty"`
    Status          string             `bson:"st" json:"status"`
    PropertyIDs     []string           `bson:"prop,omitempty" json:"propertyIds,omitempty"`
    ExternalURL     string             `bson:"ExtUrl,omitempty" json:"externalUrl,omitempty"`
    ClientID        primitive.ObjectID `bson:"ClntId,omitempty" json:"clientId,omitempty"`
    Stats           VideoStats         `bson:"stat" json:"stats"`
    ProcessingError string             `bson:"procErr,omitempty" json:"processingError,omitempty"`
    // 移除手动时间戳字段，使用gomongo自动管理的_ts和_mt
    // CreatedAt       time.Time          `bson:"ts" json:"createdAt"`
    // UpdatedAt       time.Time          `bson:"mt" json:"updatedAt"`
    PublishedAt *time.Time `bson:"pts,omitempty" json:"publishedAt,omitempty"`
}
```

<a name="VideoFilter"></a>
## type [VideoFilter](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L118-L127>)

VideoFilter defines the criteria for filtering videos. Pointers are used to distinguish between zero\-value and non\-provided fields.

```go
type VideoFilter struct {
    CategoryID          *primitive.ObjectID
    ClientID            *primitive.ObjectID
    FilterForNullClient bool     // New flag to specifically filter for null ClientID
    Status              []string // 可接受多个状态
    FromDate            *time.Time
    ToDate              *time.Time
    Page                int
    Limit               int
}
```

<a name="VideoResponse"></a>
## type [VideoResponse](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L67-L87>)

VideoResponse is the DTO \(Data Transfer Object\) for API responses. It defines the structure of video data sent to the client, keeping the DB model clean.

```go
type VideoResponse struct {
    ID              primitive.ObjectID `json:"id,omitempty"`
    Title           MultilingualString `json:"title"`
    Description     MultilingualString `json:"description,omitempty"`
    Status          string             `json:"status"`
    Duration        float64            `json:"duration"`
    PreviewThumbUrl string             `json:"previewThumbUrl,omitempty"`
    PreviewVideoUrl string             `json:"previewVideoUrl,omitempty"`
    UploaderID      string             `json:"uploaderId"`
    CategoryID      primitive.ObjectID `json:"categoryId,omitempty"`
    Tags            []string           `json:"tags,omitempty"`
    PropertyIDs     []string           `json:"propertyIds,omitempty"`
    ExternalURL     string             `json:"externalUrl,omitempty"`
    ClientID        primitive.ObjectID `json:"clientId,omitempty"`
    Stats           VideoStats         `json:"stats"`
    ProcessingError string             `json:"processingError,omitempty"`
    // 移除手动时间戳字段，API响应中可以从gomongo的_ts和_mt获取
    // CreatedAt       time.Time          `json:"createdAt"`
    // UpdatedAt       time.Time          `json:"updatedAt"`
    PublishedAt *time.Time `json:"publishedAt,omitempty"`
}
```

<a name="VideoStats"></a>
## type [VideoStats](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L28-L34>)

VideoStats 包含视频的统计数据

```go
type VideoStats struct {
    Views          int64  `bson:"vws" json:"views"`
    Likes          int64  `bson:"lks" json:"likes"`
    Collections    int64  `bson:"cltsCnt" json:"collections"`
    Completions    int64  `bson:"cplCnt" json:"completions"`
    CompletionRate string `bson:"cplRt" json:"completionRate"`
}
```

<a name="VideoStatsSummary"></a>
## type [VideoStatsSummary](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/domain/video/model.go#L90-L96>)

VideoStatsSummary is the DTO for the aggregated video statistics API response.

```go
type VideoStatsSummary struct {
    TotalVideos           int64  `json:"totalVideos"`
    TotalViews            int64  `json:"totalViews"`
    TotalLikes            int64  `json:"totalLikes"`
    TotalCollections      int64  `json:"totalCollections"`
    OverallCompletionRate string `json:"overallCompletionRate"`
}
```

# database

```go
import "realmaster-video-backend/internal/platform/database"
```

## Index

- [func CreateIndexes\(ctx context.Context\)](<#CreateIndexes>)
- [func GetCollection\(dbName, collectionName string\) \*gomongo.MongoCollection](<#GetCollection>)
- [func GetSessionFromContext\(ctx context.Context\) mongo.Session](<#GetSessionFromContext>)
- [func InitMongoDB\(\) error](<#InitMongoDB>)
- [func IsInTransaction\(ctx context.Context\) bool](<#IsInTransaction>)
- [type TransactionManager](<#TransactionManager>)
  - [func NewTransactionManager\(dbName string, transactionSupport bool\) \*TransactionManager](<#NewTransactionManager>)
  - [func \(tm \*TransactionManager\) IsTransactionSupported\(\) bool](<#TransactionManager.IsTransactionSupported>)
  - [func \(tm \*TransactionManager\) WithTransaction\(ctx context.Context, fn func\(ctx context.Context\) error\) error](<#TransactionManager.WithTransaction>)


<a name="CreateIndexes"></a>
## func [CreateIndexes](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/indexes.go#L14>)

```go
func CreateIndexes(ctx context.Context)
```

CreateIndexes 使用gomongo创建索引

<a name="GetCollection"></a>
## func [GetCollection](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/mongo.go#L24>)

```go
func GetCollection(dbName, collectionName string) *gomongo.MongoCollection
```

GetCollection 获取指定数据库和集合的实例

<a name="GetSessionFromContext"></a>
## func [GetSessionFromContext](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L103>)

```go
func GetSessionFromContext(ctx context.Context) mongo.Session
```

GetSessionFromContext 从context中获取MongoDB会话 如果context不是SessionContext，返回nil

<a name="InitMongoDB"></a>
## func [InitMongoDB](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/mongo.go#L13>)

```go
func InitMongoDB() error
```

InitMongoDB 初始化gomongo数据库连接 gomongo会自动从config.toml读取数据库配置

<a name="IsInTransaction"></a>
## func [IsInTransaction](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L96>)

```go
func IsInTransaction(ctx context.Context) bool
```

IsInTransaction 检查给定的context是否是事务上下文 这个函数可以帮助repository层确认是否在事务中执行

<a name="TransactionManager"></a>
## type [TransactionManager](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L13-L16>)

TransactionManager 处理事务的环境适配

```go
type TransactionManager struct {
    // contains filtered or unexported fields
}
```

<a name="NewTransactionManager"></a>
### func [NewTransactionManager](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L19>)

```go
func NewTransactionManager(dbName string, transactionSupport bool) *TransactionManager
```

NewTransactionManager 创建事务管理器

<a name="TransactionManager.IsTransactionSupported"></a>
### func \(\*TransactionManager\) [IsTransactionSupported](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L90>)

```go
func (tm *TransactionManager) IsTransactionSupported() bool
```

IsTransactionSupported 返回当前环境是否支持事务

<a name="TransactionManager.WithTransaction"></a>
### func \(\*TransactionManager\) [WithTransaction](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/database/transaction.go#L36>)

```go
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error
```

WithTransaction 根据环境配置决定是否使用事务

重大发现：gomongo完全支持事务！ 1. gomongo.MongoCollection.GetClient\(\) 返回底层 \*mongo.Client 2. MongoDB驱动完全支持事务，包括单实例事务 3. MongoDB 8.0.9完全支持单实例事务 4. 可以通过gomongo实现真正的事务管理

重要提醒：使用事务时，repository层必须使用传入的SessionContext， 而不是原始的context，否则操作不会在事务中执行！

# logger

```go
import "realmaster-video-backend/internal/platform/logger"
```

## Index

- [func Sync\(\) error](<#Sync>)
- [type LoggerWrapper](<#LoggerWrapper>)
  - [func NewLogger\(\) \(\*LoggerWrapper, error\)](<#NewLogger>)
  - [func \(l \*LoggerWrapper\) Debug\(msg string, fields ...interface\{\}\)](<#LoggerWrapper.Debug>)
  - [func \(l \*LoggerWrapper\) Error\(msg string, fields ...interface\{\}\)](<#LoggerWrapper.Error>)
  - [func \(l \*LoggerWrapper\) Fatal\(msg string, fields ...interface\{\}\)](<#LoggerWrapper.Fatal>)
  - [func \(l \*LoggerWrapper\) Info\(msg string, fields ...interface\{\}\)](<#LoggerWrapper.Info>)
  - [func \(l \*LoggerWrapper\) Warn\(msg string, fields ...interface\{\}\)](<#LoggerWrapper.Warn>)
- [type ZapField](<#ZapField>)
  - [func Any\(key string, value interface\{\}\) ZapField](<#Any>)
  - [func Bool\(key string, value bool\) ZapField](<#Bool>)
  - [func Duration\(key string, value interface\{\}\) ZapField](<#Duration>)
  - [func Error\(err error\) ZapField](<#Error>)
  - [func Float64\(key string, value float64\) ZapField](<#Float64>)
  - [func Int\(key string, value int\) ZapField](<#Int>)
  - [func Int64\(key string, value int64\) ZapField](<#Int64>)
  - [func String\(key, value string\) ZapField](<#String>)


<a name="Sync"></a>
## func [Sync](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L38>)

```go
func Sync() error
```

Sync 同步日志缓冲区 \- golog会自动处理

<a name="LoggerWrapper"></a>
## type [LoggerWrapper](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L10>)

LoggerWrapper 包装golog以提供兼容的接口

```go
type LoggerWrapper struct{}
```

<a name="Log"></a>

```go
var (
    // Log 是全局日志记录器实例，提供与zap兼容的接口
    Log *LoggerWrapper
)
```

<a name="NewLogger"></a>
### func [NewLogger](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L19>)

```go
func NewLogger() (*LoggerWrapper, error)
```

NewLogger 初始化golog并返回包装器 golog会自动从config.toml读取\[golog\]配置，无需传入参数

<a name="LoggerWrapper.Debug"></a>
### func \(\*LoggerWrapper\) [Debug](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L46>)

```go
func (l *LoggerWrapper) Debug(msg string, fields ...interface{})
```

Debug 记录调试级别日志

<a name="LoggerWrapper.Error"></a>
### func \(\*LoggerWrapper\) [Error](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L64>)

```go
func (l *LoggerWrapper) Error(msg string, fields ...interface{})
```

Error 记录错误级别日志

<a name="LoggerWrapper.Fatal"></a>
### func \(\*LoggerWrapper\) [Fatal](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L70>)

```go
func (l *LoggerWrapper) Fatal(msg string, fields ...interface{})
```

Fatal 记录致命错误级别日志

<a name="LoggerWrapper.Info"></a>
### func \(\*LoggerWrapper\) [Info](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L52>)

```go
func (l *LoggerWrapper) Info(msg string, fields ...interface{})
```

Info 记录信息级别日志

<a name="LoggerWrapper.Warn"></a>
### func \(\*LoggerWrapper\) [Warn](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L58>)

```go
func (l *LoggerWrapper) Warn(msg string, fields ...interface{})
```

Warn 记录警告级别日志

<a name="ZapField"></a>
## type [ZapField](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/logger.go#L78-L81>)

ZapField 表示一个zap字段

```go
type ZapField struct {
    Key   string
    Value interface{}
}
```

<a name="Any"></a>
### func [Any](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L44>)

```go
func Any(key string, value interface{}) ZapField
```

Any 创建任意类型字段 \- zap兼容

<a name="Bool"></a>
### func [Bool](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L29>)

```go
func Bool(key string, value bool) ZapField
```

Bool 创建布尔字段 \- zap兼容

<a name="Duration"></a>
### func [Duration](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L39>)

```go
func Duration(key string, value interface{}) ZapField
```

Duration 创建时间间隔字段 \- zap兼容

<a name="Error"></a>
### func [Error](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L21>)

```go
func Error(err error) ZapField
```

Error 创建错误字段 \- zap兼容

<a name="Float64"></a>
### func [Float64](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L34>)

```go
func Float64(key string, value float64) ZapField
```

Float64 创建浮点数字段 \- zap兼容

<a name="Int"></a>
### func [Int](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L11>)

```go
func Int(key string, value int) ZapField
```

Int 创建整数字段 \- zap兼容

<a name="Int64"></a>
### func [Int64](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L16>)

```go
func Int64(key string, value int64) ZapField
```

Int64 创建64位整数字段 \- zap兼容

<a name="String"></a>
### func [String](<https://github.com/real-rm/govideodemo/blob/main/apps/realmaster-video-backend/internal/platform/logger/zap.go#L6>)

```go
func String(key, value string) ZapField
```

String 创建字符串字段 \- zap兼容

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
