package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/real-rm/goauth/middleware"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/category"
	"realmaster-video-backend/internal/domain/interaction"
	"realmaster-video-backend/internal/domain/main_property"
	"realmaster-video-backend/internal/domain/public"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/server"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}

	// 2. 初始化日志
	if _, err := logger.NewLogger(); err != nil {
		panic(fmt.Sprintf("初始化日志失败: %v", err))
	}
	defer func() {
		if err := logger.Sync(); err != nil {
			fmt.Printf("logger sync error: %v\n", err)
		}
	}()

	// 3. 初始化数据库连接
	err = database.InitMongoDB()
	if err != nil {
		logger.Log.Fatal("初始化gomongo失败", logger.Error(err))
	}
	// gomongo会自动管理连接，无需手动关闭

	// 4. 初始化 JWT 认证
	if err := middleware.InitJWTSecret(); err != nil {
		logger.Log.Fatal("初始化JWT密钥失败", logger.Error(err))
	}

	// 可选：创建索引（受配置控制）
	if cfg.Database.AutoCreateIndexes {
		database.CreateIndexes(context.Background())
	}

	// 5. 初始化各领域组件
	// 视频（提前以便注入）
	videoRepo := video.NewRepository()

	// 分类
	categoryRepo := category.NewRepository()
	categoryService := category.NewService(categoryRepo, videoRepo)

	// 确保 "None" 分类存在
	if err := categoryService.EnsureNoneCategoryExists(context.Background()); err != nil {
		logger.Log.Fatal("初始化 'None' 分类失败", logger.Error(err))
	}

	categoryHandler := category.NewHandler(categoryService)

	// 广告主
	advertiserRepo := advertiser.NewRepository()
	advertiserService := advertiser.NewService(advertiserRepo, videoRepo, cfg)
	advertiserHandler := advertiser.NewHandler(advertiserService, cfg)

	// 房源
	propertyRepo := main_property.NewRepository()
	propertyService := main_property.NewService(propertyRepo)
	propertyHandler := main_property.NewHandler(propertyService)

	// 视频
	videoService := video.NewService(videoRepo, cfg)
	videoHandler, err := video.NewHandler(videoService, cfg.Server.DraftDir, cfg)
	if err != nil {
		logger.Log.Fatal("创建视频处理器失败", logger.Error(err))
	}

	// 注意：移除了内部文件上传服务，现在完全使用goupload

	// 事务管理器（指定用于获取客户端的集合名，避免硬编码）
	txManager := database.NewTransactionManager("realmaster_video", cfg.Transaction.Support, "video_videos")

	// 交互事件
	interactionRepo := interaction.NewRepository()
	stateRepo := state.NewRepository()
	interactionService := interaction.NewService(interactionRepo, stateRepo, videoRepo, txManager)
	interactionHandler := interaction.NewHandler(interactionService)

	// 公共API
	publicService := public.NewService(videoRepo, stateRepo, cfg)
	publicHandler := public.NewHandler(publicService)

	// 认证处理器（使用 goauth 的 controller）

	// 6. 创建 Gin 引擎
	router := gin.Default()

	// 使用安全的CORS中间件配置
	corsConfig := cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}

	// 根据配置设置允许来源（若配置为空，开发环境退回默认本地端口）
	if len(cfg.CORS.AllowOrigins) > 0 {
		corsConfig.AllowOrigins = cfg.CORS.AllowOrigins
	} else if gin.Mode() == gin.DebugMode {
		corsConfig.AllowOrigins = []string{
			"http://localhost:5173",
			"http://localhost:5174",
			"http://127.0.0.1:5173",
			"http://127.0.0.1:5174",
		}
	}

	router.Use(cors.New(corsConfig))

	// 7. 注册路由
	systemGroup := router.Group("/")
	systemGroup.Use()
	{
		systemGroup.GET("/", func(c *gin.Context) { c.String(http.StatusOK, "OK") })
	}

	server.RegisterSystemRoutes(router)
	server.RegisterAuthRoutes(router)

	if gin.Mode() == gin.DebugMode {
		server.RegisterDevRoutes(router)
	}

	adminGroup := router.Group("/video/admin")
	adminGroup.Use(middleware.JWTAuthMiddleware())
	{
		server.RegisterCategoryRoutes(adminGroup, categoryHandler)
		server.RegisterAdvertiserRoutes(adminGroup, advertiserHandler)
		server.RegisterVideoRoutes(adminGroup, videoHandler)
		server.RegisterMainPropertyRoutes(adminGroup, propertyHandler)
	}

	publicGroup := router.Group("/video/public")
	publicGroup.Use(middleware.JWTAuthMiddleware()) // 使用 goauth 的 JWT 中间件
	{
		server.RegisterPublicRoutes(publicGroup, publicHandler)
		server.RegisterInteractionRoutes(publicGroup, interactionHandler)
	}

	// 8. 启动服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	// 优雅关闭
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Log.Fatal("启动服务器失败", logger.Error(err))
		}
	}()

	logger.Log.Info("服务器已启动",
		logger.String("host", cfg.Server.Host),
		logger.Int("port", cfg.Server.Port),
	)

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Log.Info("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Log.Fatal("服务器关闭失败", logger.Error(err))
	}

	logger.Log.Info("服务器已关闭")
}
