package main

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/media"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger" // zap兼容别名
	"realmaster-video-backend/internal/uploader"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}

	// 2. 初始化日志
	if _, err := logger.NewLogger(); err != nil {
		panic(fmt.Sprintf("初始化日志失败: %v", err))
	}
	defer func() {
		if err := logger.Sync(); err != nil {
			fmt.Printf("logger sync error: %v\n", err)
		}
	}()

	// 3. 初始化数据库连接
	err = database.InitMongoDB()
	if err != nil {
		logger.Log.Fatal("初始化gomongo失败", logger.Error(err))
	}
	// gomongo会自动管理连接，无需手动关闭

	// 4. 初始化 video repository
	videoRepo := video.NewRepository()

	// 5. 初始化 goupload 服务
	uploadService, err := uploader.NewGoUploadService(cfg.UserUpload.Site)
	if err != nil {
		logger.Log.Fatal("初始化上传服务失败", logger.Error(err))
	}

	logger.Log.Info("Video Worker 已启动，开始轮询任务...")

	// 6. 开始无限循环，轮询任务
	// 使用动态轮询间隔：有任务时快速轮询，无任务时延长间隔
	pollInterval := 5 * time.Second
	maxPollInterval := 30 * time.Second

	for {
		// a. 尝试获取并锁定一个待处理的任务
		job, err := videoRepo.FindOneAndUpdateToProcessing(context.Background())
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				// 没有待处理的任务，增加轮询间隔
				if pollInterval < maxPollInterval {
					pollInterval = time.Duration(float64(pollInterval) * 1.5)
					if pollInterval > maxPollInterval {
						pollInterval = maxPollInterval
					}
				}
				logger.Log.Debug("没有待处理的任务，等待下一次轮询...", logger.Duration("interval", pollInterval))
			} else {
				// 发生了其他数据库错误，保持较短间隔重试
				logger.Log.Error("获取任务失败", logger.Error(err))
				pollInterval = 5 * time.Second
			}

			// 等待一段时间再试
			time.Sleep(pollInterval)
			continue
		}

		// 成功获取任务，重置轮询间隔
		pollInterval = 1 * time.Second

		// b. 如果我们到达这里，意味着我们成功获取了一个任务
		logger.Log.Info("成功认领任务",
			logger.String("videoID", job.ID.Hex()),
			logger.String("draftVideoGouploadPath", job.DraftVideoGouploadPath))

		// c. 构建草稿视频的本地路径（基于goupload配置）
		// 根据config.toml: video_draft存储在 /var/www/draft/rm_video_drafts
		localVideoPath := filepath.Join("/var/www/draft/rm_video_drafts", job.DraftVideoGouploadPath)

		// 检查文件是否存在
		if _, err := os.Stat(localVideoPath); os.IsNotExist(err) {
			logger.Log.Error("草稿视频文件不存在",
				logger.String("videoID", job.ID.Hex()),
				logger.String("path", localVideoPath))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("草稿视频文件不存在: %s", localVideoPath)
			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}
			continue
		}

		// d. 验证文件并提取元数据
		metadata, err := media.GetMetadata(context.Background(), localVideoPath)
		if err != nil {
			logger.Log.Error("FFprobe 处理失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			// 更新数据库状态为失败
			job.Status = video.StatusProcessingFailed
			job.ProcessingError = err.Error() // 记录错误信息
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}

			// 继续下一次循环
			continue
		}

		logger.Log.Info("FFprobe 处理成功",
			logger.String("videoID", job.ID.Hex()),
			logger.String("duration", metadata.Duration.String()),
			logger.Int("width", metadata.Width),
			logger.Int("height", metadata.Height),
		)

		// e. 执行 FFmpeg 转码
		transcodeResult, err := media.Transcode(context.Background(), localVideoPath)
		if err != nil {
			logger.Log.Error("FFmpeg 转码失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("FFmpeg aac: %v", err)
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}
			continue
		}

		// 将转码结果文件路径记录到日志
		for profile, path := range transcodeResult.VideoOutputs {
			logger.Log.Info("转码输出",
				logger.String("videoID", job.ID.Hex()),
				logger.String("profile", profile),
				logger.String("path", path),
			)
		}

		// e. 执行 Shaka Packager 打包
		packageResult, err := media.Package(context.Background(), transcodeResult)
		if err != nil {
			logger.Log.Error("Shaka Packager 打包失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("Shaka Packager failed: %v", err)
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}
			continue
		}

		logger.Log.Info("Shaka Packager 打包成功",
			logger.String("videoID", job.ID.Hex()),
			logger.String("hls_manifest", packageResult.HLSManifestPath),
		)

		// f. 使用goupload将打包好的HLS目录上传到最终位置
		videoID := job.ID.Hex()
		uploadResult, err := uploadService.UploadDirectoryToFinal(
			context.Background(),
			job.UploaderID,
			videoID,
			transcodeResult.PackagingDir,
		)
		if err != nil {
			logger.Log.Error("上传HLS目录失败", logger.String("videoID", videoID), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("HLS目录上传失败: %v", err)

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", videoID), logger.Error(updateErr))
			}
			continue
		}

		logger.Log.Info("HLS目录上传成功",
			logger.String("videoID", videoID),
			logger.String("directoryPath", uploadResult.DirectoryPath),
			logger.Int("totalFiles", uploadResult.TotalFiles))

		// g. 处理缩略图上传（如果存在草稿缩略图）
		var finalThumbGouploadPath string
		if job.DraftThumbGouploadPath != "" {
			// 构建草稿缩略图的本地路径（基于goupload配置）
			// 根据config.toml: thumbnail_draft存储在 /var/www/draft/rm_thumbnail_drafts
			localThumbPath := filepath.Join("/var/www/draft/rm_thumbnail_drafts", job.DraftThumbGouploadPath)

			// 检查缩略图文件是否存在
			if _, err := os.Stat(localThumbPath); err == nil {
				// 打开缩略图文件
				thumbFile, err := os.Open(localThumbPath)
				if err != nil {
					logger.Log.Error("打开缩略图文件失败", logger.String("path", localThumbPath), logger.Error(err))
				} else {
					defer thumbFile.Close()

					// 获取文件信息
					thumbInfo, _ := thumbFile.Stat()

					// 上传到最终位置
					thumbResult, err := uploadService.UploadThumbnailFinal(
						context.Background(),
						job.UploaderID,
						thumbFile,
						filepath.Base(localThumbPath),
						thumbInfo.Size(),
					)
					if err != nil {
						logger.Log.Error("上传最终缩略图失败", logger.String("videoID", videoID), logger.Error(err))
					} else {
						finalThumbGouploadPath = thumbResult.Path
						logger.Log.Info("缩略图上传成功", logger.String("videoID", videoID), logger.String("path", finalThumbGouploadPath))
					}
				}
			}
		}

		// h. 从goupload目录上传结果中找到HLS主清单文件
		var hlsGouploadPath string
		masterManifestName := transcodeResult.ManifestsBaseName + ".m3u8"

		// 首先查找主清单文件
		for _, successFile := range uploadResult.SuccessFiles {
			if strings.HasSuffix(successFile, masterManifestName) {
				hlsGouploadPath = filepath.Join(uploadResult.DirectoryPath, successFile)
				break
			}
		}

		// 如果没找到主清单文件，则查找任意.m3u8文件作为降级
		if hlsGouploadPath == "" {
			for _, successFile := range uploadResult.SuccessFiles {
				if strings.HasSuffix(successFile, ".m3u8") {
					hlsGouploadPath = filepath.Join(uploadResult.DirectoryPath, successFile)
					break
				}
			}
		}
		if hlsGouploadPath == "" {
			logger.Log.Error("未能从上传结果中找到HLS清单路径", logger.String("videoID", videoID))
			// 这是致命错误，因为没有播放地址
			job.Status = video.StatusProcessingFailed
			job.ProcessingError = "未能找到HLS清单路径"

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", videoID), logger.Error(updateErr))
			}
			continue
		}

		// i. 更新数据库，将视频状态发布，使用goupload路径
		now := time.Now()
		job.Status = video.StatusPublished
		job.FinalVideoGouploadPath = hlsGouploadPath
		job.FinalThumbGouploadPath = finalThumbGouploadPath
		job.Duration = metadata.DurationSeconds
		job.Width = metadata.Width                                // 存储视频宽度
		job.Height = metadata.Height                              // 存储视频高度
		job.ManifestsBaseName = transcodeResult.ManifestsBaseName // 保存base name
		job.PublishedAt = &now
		job.ProcessingError = "" // 清除之前的错误信息

		// 在清空数据库字段之前，先将草稿路径保存到临时变量中，以便后续删除
		draftVideoPathToDelete := job.DraftVideoGouploadPath
		draftThumbPathToDelete := job.DraftThumbGouploadPath

		// 清理草稿字段
		job.DraftVideoGouploadPath = ""
		job.DraftThumbGouploadPath = ""

		if err := videoRepo.Update(context.Background(), job); err != nil {
			logger.Log.Error("发布视频失败（最终数据库更新失败）", logger.String("videoID", videoID), logger.Error(err))
			// 这是一个严重问题，文件已上传但数据库状态未同步
			// 我们选择继续，不删除文件，以便手动干预
			continue
		}

		logger.Log.Info("视频发布成功", logger.String("videoID", videoID))

		// j. 清理本地临时文件
		if err := os.RemoveAll(transcodeResult.PackagingDir); err != nil {
			logger.Log.Error("清理打包目录失败", logger.String("dir", transcodeResult.PackagingDir), logger.Error(err))
		}

		// 使用goupload删除草稿文件
		if draftVideoPathToDelete != "" {
			if err := uploadService.DeleteVideoDraft(context.Background(), draftVideoPathToDelete); err != nil {
				logger.Log.Error("删除草稿视频文件失败", logger.String("path", draftVideoPathToDelete), logger.Error(err))
			} else {
				logger.Log.Info("草稿视频文件删除成功", logger.String("path", draftVideoPathToDelete))
			}
		}

		if draftThumbPathToDelete != "" {
			if err := uploadService.DeleteThumbnailDraft(context.Background(), draftThumbPathToDelete); err != nil {
				logger.Log.Error("删除草稿缩略图文件失败", logger.String("path", draftThumbPathToDelete), logger.Error(err))
			} else {
				logger.Log.Info("草稿缩略图文件删除成功", logger.String("path", draftThumbPathToDelete))
			}
		}

		logger.Log.Info("任务处理与清理全部完成", logger.String("videoID", job.ID.Hex()))
	}
}
