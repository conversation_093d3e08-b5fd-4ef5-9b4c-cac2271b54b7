# GoUpload

GoUpload 是一个为 Go 语言设计的高级文件上传库，提供事务性文件上传和多存储目标支持。

## 特性

- **事务性写入与回滚**: 保证向多个存储目标写入时的原子性，失败时自动回滚
- **多存储支持**: 同时支持本地文件系统和 S3 兼容存储
- **分块上传**: 支持大文件分块上传，提高可靠性和性能
- **目录上传**: 支持整个目录的批量上传，保持目录结构
- **文件删除**: 支持多存储目标的同步删除，包括目录递归删除
- **智能路径生成**: 基于统计数据的分层目录结构，支持多入口板块
- **并发安全**: 线程安全，支持高并发场景
- **灵活配置**: 支持新的存储配置格式和连接源管理

## 快速开始

```go
package main

import (
    "context"
    "log"
    "strings"

    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
)

func main() {
    // 1. 初始化MongoDB
    gomongo.InitMongoDB()

    // 2. 为不同的entryName创建独立的统计更新器
    statsColl := gomongo.Coll("tmp", "dir_stats")

    // 单文件上传统计更新器
    fileStatsUpdater, err := goupload.NewStatsUpdater("MYBOARD", "video", statsColl)
    if err != nil {
        log.Fatal(err)
    }

    // 分块上传统计更新器（如果需要）
    chunkedStatsUpdater, err := goupload.NewStatsUpdater("MYBOARD", "chunked_video", statsColl)
    if err != nil {
        log.Fatal(err)
    }

    // 3. 上传文件
    fileReader := strings.NewReader("Hello, World!")
    result, err := goupload.Upload(
        context.Background(),
        fileStatsUpdater,    // 使用对应的统计更新器
        "MYBOARD",           // 板块
        "video",             // 入口类型
        "user123",           // 用户ID
        fileReader,          // 文件内容
        "hello.txt",         // 文件名
        0,              // 文件大小（0表示自动检测）
    )

    if err != nil {
        log.Fatal(err)
    }

    log.Printf("上传成功: %s", result.Path)
    log.Printf("写入位置: %d 个", len(result.WrittenPaths))
}
```

## 安装

```bash
go get github.com/real-rm/goupload
```

## 配置

### 1. 配置文件

```toml
# config.toml
[dbs]
verbose = 3
[dbs.tmp]
uri = "mongodb://127.0.0.1:27017/goupload_test"

# 连接源配置（S3提供商等）
[connection_sources]
  [[connection_sources.s3_providers]]
    name = "aws-primary-storage"
    endpoint = "s3.us-east-1.amazonaws.com"
    key = "YOUR_AWS_ACCESS_KEY"
    pass = "YOUR_AWS_SECRET_KEY"
    region = "us-east-1"

  [[connection_sources.s3_providers]]
    name = "garage-storage"
    endpoint = "http://localhost:3900"
    key = "GK123456789"
    pass = "SK123456789"
    region = "garage"

# 用户上传配置
[userupload]
site = "CAR"
  [[userupload.types]]
    entryName = "video"
    prefix = "/uploads"
    tmpPath = "./temp/uploads"
    maxSize = "500MB"
    storage = [
      { type = "local", path = "uploads/files" },
      { type = "s3", target = "aws-primary-storage", bucket = "my-video-bucket" }
    ]

  [[userupload.types]]
    entryName = "avatar"
    prefix = "/avatars"
    tmpPath = "./temp/avatars"
    maxSize = "10MB"
    storage = [
      { type = "local", path = "uploads/avatars" },
      { type = "s3", target = "garage-storage", bucket = "avatars" }
    ]
```

### 2. 基本使用

```go
package main

import (
    "context"
    "log"
    "strings"
    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
)

func main() {
    // 初始化MongoDB和统计更新器
    gomongo.InitMongoDB()
    statsColl := gomongo.Coll("tmp", "dir_stats")
    statsUpdater, _ := goupload.NewStatsUpdater("CAR", "video", statsColl)

    // 上传文件
    fileReader := strings.NewReader("Hello, World!")
    result, err := goupload.Upload(
        context.Background(),
        statsUpdater, "CAR", "video", "user123",
        fileReader, "test.txt", 0,
    )
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("上传成功: %s", result.Path)
}
```

## 核心API

### NewStatsUpdater - 创建统计更新器

```go
func NewStatsUpdater(site string, entryName string, coll *gomongo.MongoCollection) (StatsUpdater, error)
```

统计更新器是必需的，用于L1目录管理和统计收集。所有板块都使用统计基础的L1计算，支持不同的entryName。

**⚠️ 重要**: 不同的entryName必须使用独立的StatsUpdater实例，以确保统计数据正确分离。

```go
// ✅ 正确：为不同entryName创建独立的统计更新器
videoUpdater, err := goupload.NewStatsUpdater("MYBOARD", "video", statsColl)
audioUpdater, err := goupload.NewStatsUpdater("MYBOARD", "audio", statsColl)
chunkedUpdater, err := goupload.NewStatsUpdater("MYBOARD", "chunked_upload", statsColl)

// ❌ 错误：多个entryName共享同一个统计更新器会导致统计数据混合
// 不要这样做！
```

### Upload - 单文件上传

```go
func Upload(ctx context.Context, statsUpdater StatsUpdater, site, entryName, uid string, reader io.Reader, originalFilename string, clientDeclaredSize int64) (*UploadResult, error)
```

### UploadDirectory - 目录上传

```go
func UploadDirectory(ctx context.Context, statsUpdater StatsUpdater, request *DirectoryUploadRequest) (*DirectoryUploadResult, error)

type DirectoryUploadRequest struct {
    Site          string                 // 板块名称
    EntryName     string                 // 入口类型
    UserID        string                 // 用户ID
    DirectoryName string                 // 目录名称
    Files         []DirectoryFileEntry   // 文件列表
    MaxSize       int64                  // 目录总大小限制（可选，0表示使用配置默认值）
}

type DirectoryFileEntry struct {
    RelativePath string     // 文件在目录中的相对路径，如 "subdir/file.txt"
    Reader       io.Reader  // 文件内容
    Size         int64      // 文件大小
    ModTime      *time.Time // 文件修改时间（可选）
}
```

### 分块上传

```go
// 1. 初始化
func InitiateChunkedUpload(ctx context.Context, provider PathAndConfigProvider, site, entryName, uid, originalFilename string, totalFileSize int64) (string, error)

// 2. 上传分块
func UploadChunk(ctx context.Context, uploadID string, chunkNumber int, chunkReader io.Reader) error

// 3. 完成上传
func CompleteChunkedUpload(ctx context.Context, statsUpdater StatsUpdater, uploadID string, expectedChunks int, s3ProviderMap map[string]levelStore.S3ProviderConfig) (*UploadResult, error)
```

### Delete - 文件/目录删除

```go
func Delete(ctx context.Context, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error)
```

支持删除单个文件或整个目录。对于目录，会递归删除所有子文件和子目录。

## 使用示例

### 基础文件上传

```go
package main

import (
    "context"
    "log"
    "os"
    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
)

func main() {
    // 初始化
    gomongo.InitMongoDB()
    statsColl := gomongo.Coll("tmp", "dir_stats")
    statsUpdater, _ := goupload.NewStatsUpdater("CAR", "video", statsColl)

    // 打开文件
    file, _ := os.Open("video.mp4")
    defer file.Close()

    // 上传文件
    result, err := goupload.Upload(
        context.Background(),
        statsUpdater, "CAR", "video", "user123",
        file, "video.mp4", 0,
    )
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("上传成功: %s", result.Path)
}
```

### 分块上传

```go
package main

import (
    "bytes"
    "context"
    "io"
    "log"
    "os"
    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
    levelStore "github.com/real-rm/golevelstore"
)

func main() {
    // 初始化
    gomongo.InitMongoDB()
    statsColl := gomongo.Coll("tmp", "dir_stats")
    statsUpdater, _ := goupload.NewStatsUpdater("CAR", "video", statsColl)

    // 打开大文件
    file, _ := os.Open("large_video.mp4")
    defer file.Close()
    fileInfo, _ := file.Stat()
    fileSize := fileInfo.Size()

    // 1. 初始化分块上传
    provider := &goupload.LevelStoreProvider{}
    uploadID, _ := goupload.InitiateChunkedUpload(
        context.Background(), provider,
        "CAR", "video", "user123", "large_video.mp4", fileSize,
    )

    // 2. 分块上传
    chunkSize := int64(5 * 1024 * 1024) // 5MB
    chunkNumber := 1
    buffer := make([]byte, chunkSize)

    for {
        n, err := file.Read(buffer)
        if err == io.EOF {
            break
        }
        chunkReader := bytes.NewReader(buffer[:n])
        goupload.UploadChunk(context.Background(), uploadID, chunkNumber, chunkReader)
        chunkNumber++
    }

    // 3. 完成上传
    s3Map := make(map[string]levelStore.S3ProviderConfig)
    result, _ := goupload.CompleteChunkedUpload(
        context.Background(), statsUpdater, uploadID, chunkNumber-1, s3Map,
    )

    log.Printf("分块上传成功: %s", result.Path)
}
```

### 目录上传

```go
package main

import (
    "context"
    "log"
    "os"
    "path/filepath"
    "strings"
    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
)

func main() {
    // 初始化
    gomongo.InitMongoDB()
    statsColl := gomongo.Coll("tmp", "dir_stats")
    statsUpdater, _ := goupload.NewStatsUpdater("CAR", "documents", statsColl)

    // 方法1：从本地目录上传
    result1, err := uploadFromLocalDirectory(statsUpdater, "/path/to/local/directory")
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("本地目录上传成功: %s", result1.DirectoryPath)

    // 方法2：从文件流上传（适用于HTTP处理器）
    result2, err := uploadFromStreams(statsUpdater)
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("流文件上传成功: %s", result2.DirectoryPath)
}

// 从本地目录上传
func uploadFromLocalDirectory(statsUpdater goupload.StatsUpdater, localPath string) (*goupload.DirectoryUploadResult, error) {
    var files []goupload.DirectoryFileEntry

    // 遍历本地目录
    err := filepath.Walk(localPath, func(path string, info os.FileInfo, err error) error {
        if err != nil || info.IsDir() {
            return err
        }

        // 计算相对路径
        relPath, err := filepath.Rel(localPath, path)
        if err != nil {
            return err
        }

        // 打开文件
        file, err := os.Open(path)
        if err != nil {
            return err
        }
        defer file.Close()

        files = append(files, goupload.DirectoryFileEntry{
            RelativePath: filepath.ToSlash(relPath), // 统一使用正斜杠
            Reader:       file,
            Size:         info.Size(),
            ModTime:      &info.ModTime(),
        })

        return nil
    })

    if err != nil {
        return nil, err
    }

    // 执行目录上传
    request := &goupload.DirectoryUploadRequest{
        Site:          "CAR",
        EntryName:     "documents",
        UserID:        "user123",
        DirectoryName: "my_documents",
        Files:         files,
        MaxSize:       500 * 1024 * 1024, // 500MB
    }

    return goupload.UploadDirectory(context.Background(), statsUpdater, request)
}

// 从文件流上传（适用于HTTP处理器）
func uploadFromStreams(statsUpdater goupload.StatsUpdater) (*goupload.DirectoryUploadResult, error) {
    // 模拟从HTTP请求中获取的文件流
    files := []goupload.DirectoryFileEntry{
        {
            RelativePath: "doc1.txt",
            Reader:       strings.NewReader("Hello, World! This is document 1."),
            Size:         33,
        },
        {
            RelativePath: "subdir/doc2.txt",
            Reader:       strings.NewReader("Hello, World! This is document 2 in subdirectory."),
            Size:         50,
        },
        {
            RelativePath: "images/photo.txt", // 模拟图片文件
            Reader:       strings.NewReader("This is a photo file content."),
            Size:         30,
        },
    }

    request := &goupload.DirectoryUploadRequest{
        Site:          "CAR",
        EntryName:     "documents",
        UserID:        "user456",
        DirectoryName: "uploaded_docs",
        Files:         files,
        MaxSize:       100 * 1024 * 1024, // 100MB
    }

    return goupload.UploadDirectory(context.Background(), statsUpdater, request)
}
```

## 板块配置

GoUpload 支持多入口板块，使用基于统计的L1目录计算，每个entryName都有独立的统计管理：

```go
// 多入口板块示例
videoUpdater, err := goupload.NewStatsUpdater("MYBOARD", "video", statsColl)
audioUpdater, err := goupload.NewStatsUpdater("MYBOARD", "audio", statsColl)
fileUpdater, err := goupload.NewStatsUpdater("MYBOARD", "file", statsColl)

// 每个entryName都有独立的L1/L2目录结构和统计
// 内部会调用 levelStore.NewDirKeyStore(site, coll, entryName)
```

### 存储配置格式

新版本支持更灵活的存储配置格式：

```toml
# 新格式：使用 storage 数组
[[userupload.types]]
  entryName = "video"
  storage = [
    { type = "local", path = "/data/videos" },
    { type = "s3", target = "aws-primary", bucket = "videos" },
    { type = "s3", target = "backup-storage", bucket = "video-backup" }
  ]
```

### 文件/目录删除

```go
package main

import (
    "context"
    "log"
    "github.com/real-rm/goupload"
    "github.com/real-rm/gomongo"
)

func main() {
    // 初始化
    gomongo.InitMongoDB()
    statsColl := gomongo.Coll("tmp", "dir_stats")
    statsUpdater, _ := goupload.NewStatsUpdater("CAR", "video", statsColl)

    // 删除单个文件
    result1, err := goupload.Delete(
        context.Background(),
        statsUpdater, "CAR", "video", "1225/abc12/file.mp4",
    )
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("文件删除成功: %d 个位置", len(result1.DeletedPaths))

    // 删除整个目录（递归删除所有子文件和子目录）
    result2, err := goupload.Delete(
        context.Background(),
        statsUpdater, "CAR", "documents", "1225/def34/my_documents_20250124",
    )
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("目录删除成功: %d 个位置", len(result2.DeletedPaths))

    if result2.IsPartialDelete {
        log.Printf("部分删除失败: %d 个位置", len(result2.FailedPaths))
    }
}
```

## 错误处理

GoUpload 使用标准的 Go 错误处理机制。根据错误消息内容判断错误类型：

```go
result, err := goupload.Upload(ctx, statsUpdater, "CAR", "avatar", "user123", imageReader, "avatar.jpg", 0)
if err != nil {
    errMsg := err.Error()

    // 验证错误 - 客户端错误
    if strings.Contains(errMsg, "filename") ||
       strings.Contains(errMsg, "exceeds limit") ||
       strings.Contains(errMsg, "cannot be zero") {
        return http.StatusBadRequest
    }

    // 写入错误 - 服务器错误
    if strings.Contains(errMsg, "write to") ||
       strings.Contains(errMsg, "failed to write") {
        return http.StatusInternalServerError
    }

    // 回滚错误 - 严重服务器错误
    if strings.Contains(errMsg, "rollback") {
        return http.StatusInternalServerError
    }
}
```

## 常见问题

### Q: 如何选择单文件上传、目录上传还是分块上传？
A:
- **单文件上传**: 适用于 < 100MB 的单个文件，简单快速
- **目录上传**: 适用于批量上传多个文件，保持目录结构
- **分块上传**: 适用于 > 100MB 的大文件，支持断点续传

### Q: StatsUpdater 是必需的吗？
A: 是的，StatsUpdater 是必需的，用于L1目录管理和统计收集。必须使用 `NewStatsUpdater()` 创建。

### Q: entryName参数的作用是什么？
A: entryName用于区分同一板块下的不同文件类型，每个entryName都有独立的统计和L1/L2目录结构。例如：
- `"video"` - 视频文件
- `"audio"` - 音频文件
- `"document"` - 文档文件
- `"image"` - 图片文件

### Q: 如何配置多个S3存储？
A: 在配置文件中定义多个S3提供商，然后在storage中引用：
```toml
[connection_sources]
  [[connection_sources.s3_providers]]
    name = "primary-s3"
    endpoint = "s3.amazonaws.com"
    # ...
  [[connection_sources.s3_providers]]
    name = "backup-s3"
    endpoint = "backup.s3.amazonaws.com"
    # ...

[userupload]
  [[userupload.types]]
    storage = [
      { type = "s3", target = "primary-s3", bucket = "main" },
      { type = "s3", target = "backup-s3", bucket = "backup" }
    ]
```

### Q: 如何处理上传失败？
A: GoUpload 提供详细的错误信息和自动回滚机制：
```go
if err != nil {
    errMsg := err.Error()
    if strings.Contains(errMsg, "filename") || strings.Contains(errMsg, "exceeds limit") {
        // 处理验证错误（文件名、大小等）
    } else if strings.Contains(errMsg, "write to") || strings.Contains(errMsg, "failed to write") {
        // 处理写入错误，已写入的文件会自动回滚
    }
}
```

### Q: 如何删除已上传的文件或目录？
A: 使用 Delete 函数可以同步删除多个存储目标中的文件或目录：
```go
// 删除单个文件
result, err := goupload.Delete(ctx, statsUpdater, "CAR", "video", "1225/abc12/file.mp4")

// 删除整个目录（递归删除）
result, err := goupload.Delete(ctx, statsUpdater, "CAR", "documents", "1225/def34/my_docs")

if err != nil {
    log.Printf("删除失败: %v", err)
} else {
    log.Printf("成功删除 %d 个位置", len(result.DeletedPaths))
}
```

### Q: 目录上传时如何处理文件路径？
A: 目录上传会保持原始的目录结构：
- 使用 `RelativePath` 字段指定文件在目录中的相对路径
- 支持嵌套目录，如 `"subdir/nested/file.txt"`
- 自动防止路径遍历攻击（如 `"../../../etc/passwd"`）
- 统一使用正斜杠作为路径分隔符

### Q: 目录上传的统计是如何计算的？
A: 目录上传的统计计算方式：
- **实体数量**: 1个（整个目录算作1个实体）
- **文件数量**: 目录中实际文件的数量
- 例如：上传包含5个文件的目录，统计为1个实体，5个文件

### Q: 统计数据何时更新到数据库？
A: 统计数据更新机制：
- **立即调用**: 每次上传成功后，立即调用`statsUpdater.AddDirStats()`
- **内存缓存**: 统计数据在内存中累积，每10分钟批量写入数据库
- **JSON文件生成**: 同时在对应目录下生成统计JSON文件
- **服务重启风险**: 如果在10分钟内服务重启，内存中的统计增量可能丢失
- **建议**: 生产环境中实现优雅关闭，在服务停止前强制刷新统计数据

### Q: 目录上传支持哪些数据源？
A: 目录上传支持两种主要数据源：
- **本地文件夹**: 适用于服务器端文件迁移，支持seeker操作
- **HTTP文件流**: 适用于Web上传，自动处理non-seeker流
- 内部会自动检测并转换，non-seeker流会先写入临时文件

### Q: 分块上传的临时文件如何清理？
A: 分块上传临时文件清理机制：
- **成功完成**: `CompleteChunkedUpload`后立即删除所有临时文件
- **主动中止**: `AbortChunkedUpload`后立即删除所有临时文件
- **自动清理**: 超过24小时的未完成上传会被后台任务自动清理
- **清理频率**: 每小时检查一次过期的上传任务
- **存储位置**: 临时文件存储在`{tmpPath}/chunks/{uploadID}/`目录下
- **注意**: 未完成的分块上传会保留临时文件，直到自动清理

## 依赖和版本

### 主要依赖

- **golevelstore**: `v0.0.0-20250724195325-13b35eedc7c9` - 目录统计和路径管理
- **gomongo**: MongoDB 数据库操作
- **goconfig**: 配置文件管理
- **golog**: 日志记录
- **AWS SDK v2**: S3 兼容存储支持

### 版本兼容性

- **Go**: 1.19+
- **MongoDB**: 4.0+
- **S3 API**: 兼容 AWS S3 和其他 S3 兼容存储（如 Garage、MinIO）

### 更新日志

#### v1.3.1 (2025-07-25)
- ✅ **修复统计数据分离** - 不同entryName的统计数据现在正确分离
- ✅ **完善临时文件清理** - 分块上传临时文件清理机制完善
- ✅ **统计数据持久化** - 10分钟周期性更新到数据库和JSON文件
- ✅ **testserver改进** - 修复了分块上传统计归属错误的问题
- ✅ **全面测试验证** - 单文件、目录、分块上传的完整测试覆盖

#### v1.3.0 (2025-01-25)
- ✅ **新增目录上传功能** - 支持整个目录的批量上传
- ✅ **目录删除支持** - 扩展删除功能支持递归删除目录
- ✅ **seeker/non-seeker自动处理** - 智能处理不同类型的文件流
- ✅ **路径安全验证** - 防止路径遍历攻击
- ✅ **统计正确计算** - 目录上传的统计信息正确更新
- ✅ **完整的测试覆盖** - 包含单元测试和集成测试

#### v1.2.0 (2025-01-24)
- ✅ 更新到 golevelstore 新版本 API
- ✅ 统一使用多入口板块模式，所有板块都支持entryName
- ✅ 新增存储配置格式支持
- ✅ 改进的连接源管理
- ✅ 完善的测试覆盖

#### v1.1.0
- ✅ 分块上传支持
- ✅ 文件删除功能
- ✅ 事务性写入和回滚

#### v1.0.0
- ✅ 基础文件上传功能
- ✅ 多存储目标支持
- ✅ 智能路径生成

## 生产环境注意事项

### 统计数据一致性
- 统计数据每10分钟批量更新到数据库，服务重启可能导致内存中的统计增量丢失
- 建议实现优雅关闭机制，在服务停止前强制刷新统计数据
- 可以定期检查统计数据与实际文件的一致性

### 临时文件管理
- 分块上传的临时文件会在24小时后自动清理
- 监控临时文件目录的磁盘使用情况
- 考虑在高并发场景下调整清理间隔

### 多entryName配置
- 不同的entryName应该使用独立的StatsUpdater实例
- 确保每个entryName的配置（存储路径、限制等）正确分离
- 避免统计数据混合的问题

## 许可证

本项目基于 MIT 许可证开源。
