//go:build integration
// +build integration

package main

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/category"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/server"
	"realmaster-video-backend/internal/testutil"
)

// IntegrationTestSuite 集成测试套件
type IntegrationTestSuite struct {
	router         *gin.Engine
	testMongo      *testutil.TestMongoDB
	cfg            *config.Config
	videoRepo      video.Repository
	categoryRepo   category.Repository
	advertiserRepo advertiser.Repository
}

func setupIntegrationTest(t *testing.T) *IntegrationTestSuite {
	// 设置测试配置
	os.Setenv("CONFIG_FILE", "test_config.toml")

	cfg, err := config.LoadConfig()
	if err != nil {
		t.Fatalf("加载测试配置失败: %v", err)
	}

	// 连接测试数据库
	testMongo := testutil.SetupMongoDB(t)
	if testMongo == nil {
		t.Skip("跳过集成测试：无法连接到MongoDB")
	}

	// 初始化数据库连接
	err = database.InitMongoDB()
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	// 清理测试数据库
	testMongo.CleanDatabase(t, "test_realmaster_video")

	// 创建repositories
	videoRepo := video.NewRepository()
	categoryRepo := category.NewRepository()
	advertiserRepo := advertiser.NewRepository()

	// 创建services
	videoService := video.NewService(videoRepo, cfg)
	categoryService := category.NewService(categoryRepo, videoRepo)
	advertiserService := advertiser.NewService(advertiserRepo, videoRepo, cfg)

	// 创建handlers
	videoHandler := video.NewHandler(videoService, cfg)
	categoryHandler := category.NewHandler(categoryService)
	advertiserHandler := advertiser.NewHandler(advertiserService)

	// 设置路由
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 注册系统路由
	server.RegisterSystemRoutes(router)
	server.RegisterAuthRoutes(router)

	// 注册业务路由
	adminGroup := router.Group("/video/admin")
	{
		server.RegisterCategoryRoutes(adminGroup, categoryHandler)
		server.RegisterAdvertiserRoutes(adminGroup, advertiserHandler)
		server.RegisterVideoRoutes(adminGroup, videoHandler)
	}

	return &IntegrationTestSuite{
		router:         router,
		testMongo:      testMongo,
		cfg:            cfg,
		videoRepo:      videoRepo,
		categoryRepo:   categoryRepo,
		advertiserRepo: advertiserRepo,
	}
}

func (suite *IntegrationTestSuite) cleanup(t *testing.T) {
	if suite.testMongo != nil {
		suite.testMongo.Cleanup(t)
	}
}

func TestIntegration_VideoLifecycle(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	suite := setupIntegrationTest(t)
	defer suite.cleanup(t)

	t.Run("完整的视频生命周期", func(t *testing.T) {
		ctx := context.Background()

		// 1. 创建分类
		categoryReq := map[string]interface{}{
			"name":  "集成测试分类",
			"order": 1,
		}
		categoryJSON, _ := json.Marshal(categoryReq)

		req := httptest.NewRequest("POST", "/video/admin/categories/", bytes.NewBuffer(categoryJSON))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("创建分类失败，状态码: %d, 响应: %s", w.Code, w.Body.String())
		}

		var categoryResponse map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &categoryResponse)
		if err != nil {
			t.Fatalf("解析分类响应失败: %v", err)
		}

		categoryData := categoryResponse["data"].(map[string]interface{})
		categoryID := categoryData["id"].(string)

		// 2. 创建广告主
		advertiserReq := map[string]interface{}{
			"nm":   "集成测试广告主",
			"ph":   "13800138000",
			"em":   "<EMAIL>",
			"mUid": "test-mapp-user",
			"rem":  "集成测试广告主备注",
		}
		advertiserJSON, _ := json.Marshal(advertiserReq)

		req = httptest.NewRequest("POST", "/video/admin/advertisers", bytes.NewBuffer(advertiserJSON))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("创建广告主失败，状态码: %d, 响应: %s", w.Code, w.Body.String())
		}

		var advertiserResponse map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &advertiserResponse)
		if err != nil {
			t.Fatalf("解析广告主响应失败: %v", err)
		}

		advertiserData := advertiserResponse["data"].(map[string]interface{})
		advertiserID := advertiserData["id"].(string)

		// 3. 创建视频草稿
		videoReq := map[string]interface{}{
			"title": map[string]string{
				"zh": "集成测试视频",
				"en": "Integration Test Video",
			},
			"description": map[string]string{
				"zh": "这是一个集成测试视频",
				"en": "This is an integration test video",
			},
			"uploaderId":  "test-uploader",
			"categoryId":  categoryID,
			"clientId":    advertiserID,
			"tags":        []string{"集成测试", "视频"},
			"propertyIds": []string{"test-property-1"},
			"externalUrl": "https://example.com/property/test",
		}
		videoJSON, _ := json.Marshal(videoReq)

		req = httptest.NewRequest("POST", "/video/admin/videos/", bytes.NewBuffer(videoJSON))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("创建视频失败，状态码: %d, 响应: %s", w.Code, w.Body.String())
		}

		var videoResponse map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &videoResponse)
		if err != nil {
			t.Fatalf("解析视频响应失败: %v", err)
		}

		videoData := videoResponse["data"].(map[string]interface{})
		videoID := videoData["id"].(string)

		// 验证视频状态为草稿
		if videoData["status"] != video.StatusDraft {
			t.Errorf("期望视频状态为 %s, 实际为 %s", video.StatusDraft, videoData["status"])
		}

		// 4. 获取视频详情
		req = httptest.NewRequest("GET", "/video/admin/videos/"+videoID, nil)
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("获取视频详情失败，状态码: %d", w.Code)
		}

		// 5. 发布视频
		req = httptest.NewRequest("POST", "/video/admin/videos/"+videoID+"/publish", nil)
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("发布视频失败，状态码: %d, 响应: %s", w.Code, w.Body.String())
		}

		// 6. 验证视频状态已更新
		foundVideo, err := suite.videoRepo.FindByID(ctx, videoID)
		if err != nil {
			t.Fatalf("查找视频失败: %v", err)
		}

		if foundVideo.Status != video.StatusPending {
			t.Errorf("期望视频状态为 %s, 实际为 %s", video.StatusPending, foundVideo.Status)
		}

		// 7. 获取视频列表
		req = httptest.NewRequest("GET", "/video/admin/videos?page=1&limit=10", nil)
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("获取视频列表失败，状态码: %d", w.Code)
		}

		var listResponse map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &listResponse)
		if err != nil {
			t.Fatalf("解析视频列表响应失败: %v", err)
		}

		listData := listResponse["data"].(map[string]interface{})
		items := listData["items"].([]interface{})

		if len(items) != 1 {
			t.Errorf("期望1个视频, 实际为 %d", len(items))
		}

		// 8. 删除视频
		req = httptest.NewRequest("DELETE", "/video/admin/videos/"+videoID, nil)
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Fatalf("删除视频失败，状态码: %d", w.Code)
		}

		// 9. 验证视频已删除
		_, err = suite.videoRepo.FindByID(ctx, videoID)
		if err == nil {
			t.Error("期望视频已删除，但仍然存在")
		}
	})
}

func TestIntegration_ErrorHandling(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	suite := setupIntegrationTest(t)
	defer suite.cleanup(t)

	t.Run("错误处理测试", func(t *testing.T) {
		// 1. 获取不存在的视频
		nonExistentID := primitive.NewObjectID().Hex()
		req := httptest.NewRequest("GET", "/video/admin/videos/"+nonExistentID, nil)
		w := httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusNotFound {
			t.Errorf("期望状态码 %d, 实际为 %d", http.StatusNotFound, w.Code)
		}

		// 2. 创建无效的分类
		invalidCategoryReq := map[string]interface{}{
			"name": "", // 空名称应该失败
		}
		categoryJSON, _ := json.Marshal(invalidCategoryReq)

		req = httptest.NewRequest("POST", "/video/admin/categories/", bytes.NewBuffer(categoryJSON))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Error("期望创建无效分类失败，但成功了")
		}

		// 3. 发布不存在的视频
		req = httptest.NewRequest("POST", "/video/admin/videos/"+nonExistentID+"/publish", nil)
		w = httptest.NewRecorder()

		suite.router.ServeHTTP(w, req)

		if w.Code != http.StatusNotFound {
			t.Errorf("期望状态码 %d, 实际为 %d", http.StatusNotFound, w.Code)
		}
	})
}
