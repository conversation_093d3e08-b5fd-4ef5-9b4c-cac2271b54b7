package advertiser

import (
	"realmaster-video-backend/internal/domain/common"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Advertiser 表示广告主信息
type Advertiser struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`              // 广告主ID
	Name      string             `bson:"nm" json:"nm"`                                   // 广告主名称
	AvatarURL string             `bson:"avatarUrl,omitempty" json:"avatarUrl,omitempty"` // 头像URL
	Phone     string             `bson:"ph" json:"ph"`                                   // 电话号码
	Email     string             `bson:"em" json:"em"`                                   // 电子邮箱
	MAppUID   string             `bson:"mUid" json:"mUid"`                               // 主App用户ID
	Remark    string             `bson:"rem" json:"rem"`                                 // 备注信息
	// 移除手动时间戳字段，使用gomongo自动管理的_ts和_mt
	// CreatedAt time.Time          `bson:"ts" json:"ts"`                                   // 创建时间
	// UpdatedAt time.Time          `bson:"mt" json:"mt"`                                   // 更新时间
}

// CreateAdvertiserRequest 创建广告主的请求结构
type CreateAdvertiserRequest struct {
	Name    string `json:"nm" binding:"required"`        // 广告主名称
	Phone   string `json:"ph"`                           // 电话号码
	Email   string `json:"em" binding:"omitempty,email"` // 电子邮箱
	MAppUID string `json:"mUid"`                         // 主App用户ID
	Remark  string `json:"rem"`                          // 备注信息
}

// UpdateAdvertiserRequest 更新广告主的请求结构
type UpdateAdvertiserRequest struct {
	Name      *string `json:"nm,omitempty"`                           // 广告主名称
	AvatarURL *string `json:"avatarUrl,omitempty"`                    // 头像URL
	Phone     *string `json:"ph,omitempty"`                           // 电话号码
	Email     *string `json:"em,omitempty" binding:"omitempty,email"` // 电子邮箱
	MAppUID   *string `json:"mUid,omitempty"`                         // 主App用户ID
	Remark    *string `json:"rem,omitempty"`                          // 备注信息
}

// ListAdvertisersResponseData 是广告主列表响应中 'data' 字段的结构
type ListAdvertisersResponseData struct {
	Items      []Advertiser       `json:"items"`
	Pagination *common.Pagination `json:"pgn"`
}

// --- API Request Structures ---

// ListAdvertisersRequest 定义了查询广告主列表的请求参数
type ListAdvertisersRequest struct {
	Name  string `form:"nm"`    // 按名称模糊查询
	Phone string `form:"ph"`    // 按电话精确查询
	Email string `form:"em"`    // 按邮箱精确查询
	Page  int64  `form:"page"`  // 页码
	Limit int64  `form:"limit"` // 每页数量
}

// MergeDeleteAdvertiserRequest 定义了合并删除广告主的请求体
type MergeDeleteAdvertiserRequest struct {
	TargetAdvertiserID string `json:"target_advertiser_id" binding:"required"` // 视频要合并到的目标广告主ID
}
