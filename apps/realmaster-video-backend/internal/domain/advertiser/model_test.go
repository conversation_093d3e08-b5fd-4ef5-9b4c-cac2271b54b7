package advertiser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestAdvertiserModel_AvatarURL 测试广告主模型的头像URL字段
func TestAdvertiserModel_AvatarURL(t *testing.T) {
	t.Run("创建包含头像URL的Advertiser", func(t *testing.T) {
		advertiser := &Advertiser{
			ID:        primitive.NewObjectID(),
			Name:      "测试客户",
			Email:     "<EMAIL>",
			Phone:     "13800138000",
			MAppUID:   "main_app_user_123",
			Remark:    "测试客户",
			AvatarURL: "/media/avatars/test-user/2025-28/abc123/avatar.jpg",
		}

		// 验证字段设置正确
		assert.Equal(t, "测试客户", advertiser.Name)
		assert.Equal(t, "<EMAIL>", advertiser.Email)
		assert.Equal(t, "13800138000", advertiser.Phone)
		assert.Equal(t, "main_app_user_123", advertiser.MAppUID)
		assert.Equal(t, "/media/avatars/test-user/2025-28/abc123/avatar.jpg", advertiser.AvatarURL)
		assert.NotEqual(t, primitive.NilObjectID, advertiser.ID)
	})

	t.Run("创建不包含头像URL的Advertiser", func(t *testing.T) {
		advertiser := &Advertiser{
			ID:        primitive.NewObjectID(),
			Name:      "无头像客户",
			Email:     "<EMAIL>",
			Phone:     "13800138001",
			MAppUID:   "main_app_user_456",
			Remark:    "无头像客户",
			AvatarURL: "", // 空头像URL
		}

		// 验证字段设置正确
		assert.Equal(t, "无头像客户", advertiser.Name)
		assert.Equal(t, "<EMAIL>", advertiser.Email)
		assert.Equal(t, "13800138001", advertiser.Phone)
		assert.Equal(t, "main_app_user_456", advertiser.MAppUID)
		assert.Empty(t, advertiser.AvatarURL) // 头像URL应该为空
		assert.NotEqual(t, primitive.NilObjectID, advertiser.ID)
	})
}

// TestCreateAdvertiserRequest_Validation 测试创建广告主请求的验证
func TestCreateAdvertiserRequest_Validation(t *testing.T) {
	t.Run("有效的创建请求", func(t *testing.T) {
		req := CreateAdvertiserRequest{
			Name:    "有效客户",
			Email:   "<EMAIL>",
			Phone:   "13800138000",
			MAppUID: "main_app_user_123",
			Remark:  "有效的客户信息",
		}

		// 验证字段设置正确
		assert.Equal(t, "有效客户", req.Name)
		assert.Equal(t, "<EMAIL>", req.Email)
		assert.Equal(t, "13800138000", req.Phone)
		assert.Equal(t, "main_app_user_123", req.MAppUID)
		assert.Equal(t, "有效的客户信息", req.Remark)
	})

	t.Run("空字段的创建请求", func(t *testing.T) {
		req := CreateAdvertiserRequest{
			Name:    "",
			Email:   "",
			Phone:   "",
			MAppUID: "",
			Remark:  "",
		}

		// 验证空字段
		assert.Empty(t, req.Name)
		assert.Empty(t, req.Email)
		assert.Empty(t, req.Phone)
		assert.Empty(t, req.MAppUID)
		assert.Empty(t, req.Remark)
	})
}

// TestUpdateAdvertiserRequest_Validation 测试更新广告主请求的验证
func TestUpdateAdvertiserRequest_Validation(t *testing.T) {
	t.Run("有效的更新请求", func(t *testing.T) {
		name := "更新后客户"
		email := "<EMAIL>"
		phone := "13800138001"
		mAppUID := "updated_main_app_user"
		remark := "更新后的客户信息"
		avatarURL := "/media/avatars/updated-avatar.jpg"

		req := UpdateAdvertiserRequest{
			Name:      &name,
			Email:     &email,
			Phone:     &phone,
			MAppUID:   &mAppUID,
			Remark:    &remark,
			AvatarURL: &avatarURL,
		}

		// 验证字段设置正确
		assert.Equal(t, "更新后客户", *req.Name)
		assert.Equal(t, "<EMAIL>", *req.Email)
		assert.Equal(t, "13800138001", *req.Phone)
		assert.Equal(t, "updated_main_app_user", *req.MAppUID)
		assert.Equal(t, "更新后的客户信息", *req.Remark)
		assert.Equal(t, "/media/avatars/updated-avatar.jpg", *req.AvatarURL)
	})

	t.Run("部分更新请求", func(t *testing.T) {
		name := "仅更新名称"

		req := UpdateAdvertiserRequest{
			Name:      &name,
			Email:     nil,
			Phone:     nil,
			MAppUID:   nil,
			Remark:    nil,
			AvatarURL: nil,
		}

		// 验证只有Name字段有值
		assert.NotNil(t, req.Name)
		assert.Equal(t, "仅更新名称", *req.Name)
		assert.Nil(t, req.Email)
		assert.Nil(t, req.Phone)
		assert.Nil(t, req.MAppUID)
		assert.Nil(t, req.Remark)
		assert.Nil(t, req.AvatarURL)
	})
}

// TestListAdvertisersRequest_Pagination 测试列表请求的分页参数
func TestListAdvertisersRequest_Pagination(t *testing.T) {
	t.Run("有效的分页参数", func(t *testing.T) {
		req := ListAdvertisersRequest{
			Name:  "测试客户",
			Phone: "13800138000",
			Email: "<EMAIL>",
			Page:  1,
			Limit: 10,
		}

		// 验证分页参数
		assert.Equal(t, int64(1), req.Page)
		assert.Equal(t, int64(10), req.Limit)
		assert.Equal(t, "测试客户", req.Name)
		assert.Equal(t, "13800138000", req.Phone)
		assert.Equal(t, "<EMAIL>", req.Email)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		req := ListAdvertisersRequest{}

		// 验证默认值
		assert.Equal(t, int64(0), req.Page)
		assert.Equal(t, int64(0), req.Limit)
		assert.Empty(t, req.Name)
		assert.Empty(t, req.Phone)
		assert.Empty(t, req.Email)
	})
}

// TestMergeDeleteAdvertiserRequest_Validation 测试合并删除请求的验证
func TestMergeDeleteAdvertiserRequest_Validation(t *testing.T) {
	t.Run("有效的合并删除请求", func(t *testing.T) {
		targetID := primitive.NewObjectID().Hex()
		req := MergeDeleteAdvertiserRequest{
			TargetAdvertiserID: targetID,
		}

		// 验证字段设置正确
		assert.Equal(t, targetID, req.TargetAdvertiserID)
		assert.NotEmpty(t, req.TargetAdvertiserID)
	})

	t.Run("空目标ID的合并删除请求", func(t *testing.T) {
		req := MergeDeleteAdvertiserRequest{
			TargetAdvertiserID: "",
		}

		// 验证空字段
		assert.Empty(t, req.TargetAdvertiserID)
	})
}

// TestAdvertiserModel_JSONSerialization 测试广告主模型的JSON序列化
func TestAdvertiserModel_JSONSerialization(t *testing.T) {
	t.Run("包含所有字段的序列化", func(t *testing.T) {
		advertiser := &Advertiser{
			ID:        primitive.NewObjectID(),
			Name:      "JSON测试客户",
			Email:     "<EMAIL>",
			Phone:     "13800138000",
			MAppUID:   "json_main_app_user",
			Remark:    "JSON序列化测试",
			AvatarURL: "/media/avatars/test-user/avatar.jpg",
		}

		// 验证所有字段都有值
		assert.NotEmpty(t, advertiser.Name)
		assert.NotEmpty(t, advertiser.Email)
		assert.NotEmpty(t, advertiser.Phone)
		assert.NotEmpty(t, advertiser.MAppUID)
		assert.NotEmpty(t, advertiser.Remark)
		assert.NotEmpty(t, advertiser.AvatarURL)
		assert.NotEqual(t, primitive.NilObjectID, advertiser.ID)
	})

	t.Run("最小字段的序列化", func(t *testing.T) {
		advertiser := &Advertiser{
			ID:   primitive.NewObjectID(),
			Name: "最小字段客户",
		}

		// 验证必需字段有值，可选字段为空
		assert.NotEmpty(t, advertiser.Name)
		assert.NotEqual(t, primitive.NilObjectID, advertiser.ID)
		assert.Empty(t, advertiser.Email)
		assert.Empty(t, advertiser.Phone)
		assert.Empty(t, advertiser.MAppUID)
		assert.Empty(t, advertiser.Remark)
		assert.Empty(t, advertiser.AvatarURL)
	})
}
