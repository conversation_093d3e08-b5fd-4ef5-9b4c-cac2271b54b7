package advertiser

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"regexp"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/sync/errgroup"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/uploader"
)

var (
	ErrAdvertiserNotFound       = errors.New("广告主不存在")
	ErrInvalidAdvertiserID      = errors.New("无效的广告主ID")
	ErrEmailExists              = errors.New("电子邮箱已被使用")
	ErrPhoneExists              = errors.New("电话号码已被使用")
	ErrNameExists               = errors.New("广告主名称已被使用")
	ErrInvalidEmailFormat       = errors.New("无效的电子邮箱格式")
	ErrInvalidPhoneFormat       = errors.New("无效的电话号码格式")
	ErrCannotDeleteLast         = errors.New("无法删除唯一的广告主")
	ErrMergeToSelf              = errors.New("不能将广告主合并到其自身")
	ErrTargetAdvertiserNotFound = errors.New("目标广告主不存在")
)

// Service 定义了广告主相关的业务逻辑接口
type Service interface {
	List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error)
	GetByID(ctx context.Context, id string) (*Advertiser, error)
	Create(ctx context.Context, userID string, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error)
	Update(ctx context.Context, userID string, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error
	Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error
}

type service struct {
	repo          Repository
	videoRepo     video.Repository
	cfg           *config.Config
	uploadService *uploader.GoUploadService
}

// NewService 创建一个新的广告主服务实例
func NewService(repo Repository, videoRepo video.Repository, cfg *config.Config) Service {
	// 初始化 goupload 服务
	uploadService, err := uploader.NewGoUploadService(cfg.UserUpload.Site)
	if err != nil {
		logger.Log.Fatal("初始化上传服务失败", logger.Error(err))
	}

	return &service{
		repo:          repo,
		videoRepo:     videoRepo,
		cfg:           cfg,
		uploadService: uploadService,
	}
}

// List 获取广告主列表（带分页和筛选）
func (s *service) List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error) {
	// 1. 设置默认分页参数
	paginationParams := common.PaginationParams{
		Page:  int(req.Page),
		Limit: int(req.Limit),
	}
	paginationParams.ValidateWithType(common.PaginationTypeAdmin)
	req.Page = int64(paginationParams.Page)
	req.Limit = int64(paginationParams.Limit)

	// 2. 构建查询 filter（对正则进行转义，防 ReDoS）
	filter := bson.M{}
	if req.Name != "" {
		// 限制长度，避免过长输入
		name := req.Name
		if len(name) > 100 {
			name = name[:100]
		}
		filter["nm"] = primitive.Regex{Pattern: regexp.QuoteMeta(name), Options: "i"}
	}
	if req.Phone != "" {
		filter["ph"] = req.Phone
	}
	if req.Email != "" {
		filter["em"] = req.Email
	}

	// 3. 并行执行查询和计数（带取消）
	var advertisers []Advertiser
	var total int64

	g, gctx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var err error
		advertisers, err = s.repo.Find(gctx, filter, req.Page, req.Limit)
		return err
	})

	g.Go(func() error {
		var err error
		total, err = s.repo.Count(gctx, filter)
		return err
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	// 4. 转换为响应格式（构建完整头像URL）
	responseItems := make([]Advertiser, len(advertisers))
	for i, advertiser := range advertisers {
		responseItems[i] = s.toResponseAdvertiser(advertiser)
	}

	// 5. 组装并返回响应数据
	return &ListAdvertisersResponseData{
		Items:      responseItems,
		Pagination: common.CalculatePagination(total, int(req.Page), int(req.Limit)),
	}, nil
}

// GetByID 获取单个广告主详情
func (s *service) GetByID(ctx context.Context, id string) (*Advertiser, error) {
	if _, err := primitive.ObjectIDFromHex(id); err != nil {
		return nil, ErrInvalidAdvertiserID
	}

	advertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err // 直接传递仓库层的错误
	}
	if advertiser == nil {
		return nil, ErrAdvertiserNotFound
	}

	// 转换为响应格式（构建完整头像URL）
	responseAdvertiser := s.toResponseAdvertiser(*advertiser)
	return &responseAdvertiser, nil
}

// saveAvatarWithGoupload 使用goupload保存头像文件并返回其可访问的 URL
func (s *service) saveAvatarWithGoupload(ctx context.Context, userID string, fileHeader *multipart.FileHeader) (string, error) {
	// 打开上传的文件
	file, err := fileHeader.Open()
	if err != nil {
		return "", fmt.Errorf("无法打开上传的文件: %w", err)
	}
	defer file.Close()

	// 使用goupload上传头像
	result, err := s.uploadService.UploadClientAvatar(
		ctx,
		userID,
		file,
		fileHeader.Filename,
		fileHeader.Size,
	)
	if err != nil {
		return "", fmt.Errorf("上传头像失败: %w", err)
	}

	// 直接返回goupload路径，不包含prefix（与video模型保持一致）
	// 这样在删除时可以直接使用这个路径
	// 格式: user/2025-28/abc123/avatar.jpg
	return result.Path, nil
}

// toResponseAdvertiser 将数据库中的Advertiser转换为API响应格式
// 主要是构建完整的头像URL
func (s *service) toResponseAdvertiser(advertiser Advertiser) Advertiser {
	responseAdvertiser := advertiser

	// 如果有头像路径，构建完整URL
	if advertiser.AvatarURL != "" {
		if fullURL, err := s.buildFullAvatarURL(advertiser.AvatarURL); err == nil {
			responseAdvertiser.AvatarURL = fullURL
		}
		// 如果构建失败，保持原始路径（降级处理）
	}

	return responseAdvertiser
}

// buildFullAvatarURL 从goupload路径构建完整的头像URL
func (s *service) buildFullAvatarURL(gouploadPath string) (string, error) {
	if gouploadPath == "" {
		return "", nil
	}

	prefix, err := common.GetPrefixFromConfig(s.cfg.UserUpload.Site, "client_avatar")
	if err != nil {
		return "", fmt.Errorf("获取头像URL前缀失败: %w", err)
	}

	return fmt.Sprintf("%s/%s", prefix, gouploadPath), nil
}

// Create 创建一个新的广告主
func (s *service) Create(ctx context.Context, userID string, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error) {
	// 1. 检查唯一性和格式
	if req.Email != "" {
		if !common.IsValidEmail(req.Email) {
			return nil, ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmail(ctx, req.Email)
		if err != nil {
			return nil, err
		}
		if emailExists {
			return nil, ErrEmailExists
		}
	}

	if req.Phone != "" {
		if !common.IsValidPhone(req.Phone) {
			return nil, ErrInvalidPhoneFormat
		}
		// 标准化电话号码格式
		normalizedPhone := common.NormalizePhone(req.Phone)
		phoneExists, err := s.repo.ExistsByPhone(ctx, normalizedPhone)
		if err != nil {
			return nil, err
		}
		if phoneExists {
			return nil, ErrPhoneExists
		}
		// 使用标准化后的电话号码
		req.Phone = normalizedPhone
	}

	// 处理头像上传
	var avatarURL string
	if avatarFile != nil {
		var err error
		avatarURL, err = s.saveAvatarWithGoupload(ctx, userID, avatarFile)
		if err != nil {
			return nil, fmt.Errorf("保存头像失败: %w", err)
		}
	}

	// 2. 创建实体
	advertiser := &Advertiser{
		Name:      req.Name,
		AvatarURL: avatarURL,
		Phone:     req.Phone,
		Email:     req.Email,
		MAppUID:   req.MAppUID,
		Remark:    req.Remark,
		// gomongo会自动添加_ts和_mt字段
	}

	// 3. 保存到数据库
	if err := s.repo.Create(ctx, advertiser); err != nil {
		// 注意：goupload已经处理了文件管理，这里不需要手动清理文件
		return nil, fmt.Errorf("service: 创建广告主失败: %w", err)
	}

	// 4. 转换为响应格式（构建完整头像URL）
	responseAdvertiser := s.toResponseAdvertiser(*advertiser)
	return &responseAdvertiser, nil
}

// Update 更新一个广告主
func (s *service) Update(ctx context.Context, userID string, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error {
	// 0. 验证ID格式
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return ErrInvalidAdvertiserID
	}

	// 1. 获取当前广告主数据
	existingAdvertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return err
	}

	// 2. 处理头像更新
	var newAvatarURL string
	var oldAvatarURL string

	newAvatarURL = existingAdvertiser.AvatarURL
	oldAvatarURL = existingAdvertiser.AvatarURL // 保存旧头像URL用于后续删除

	if avatarFile != nil {
		// 使用goupload保存新头像
		savedURL, err := s.saveAvatarWithGoupload(ctx, userID, avatarFile)
		if err != nil {
			return fmt.Errorf("保存新头像失败: %w", err)
		}
		newAvatarURL = savedURL
	}

	// 3. 准备更新的数据
	updateData := bson.M{}

	// Use helper to add fields to updateData if they are not nil
	addField := func(key string, value *string) {
		if value != nil {
			updateData[key] = *value
		}
	}

	addField("nm", req.Name)

	if req.Phone != nil {
		if !common.IsValidPhone(*req.Phone) {
			return ErrInvalidPhoneFormat
		}
		// 标准化电话号码格式
		normalizedPhone := common.NormalizePhone(*req.Phone)
		phoneExists, err := s.repo.ExistsByPhoneAndNotID(ctx, normalizedPhone, id)
		if err != nil {
			return err
		}
		if phoneExists {
			return ErrPhoneExists
		}
		// 使用标准化后的电话号码
		updateData["ph"] = normalizedPhone
	}
	if req.Email != nil {
		if !common.IsValidEmail(*req.Email) {
			return ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmailAndNotID(ctx, *req.Email, id)
		if err != nil {
			return err
		}
		if emailExists {
			return ErrEmailExists
		}
		updateData["em"] = *req.Email
	}
	addField("mUid", req.MAppUID)
	addField("rem", req.Remark)

	// Only update avatar URL if it has changed
	if newAvatarURL != existingAdvertiser.AvatarURL {
		updateData["avatarUrl"] = newAvatarURL
	}

	// 4. 如果有更新，则执行更新（gomongo会自动更新_mt）
	if len(updateData) > 0 {
		// 5. 执行更新
		err = s.repo.Update(ctx, objectID.Hex(), updateData)
		if err != nil {
			// 如果数据库更新失败，不需要清理新上传的文件，因为goupload会自动处理
			if errors.Is(err, mongo.ErrNoDocuments) {
				return ErrAdvertiserNotFound
			}
			return fmt.Errorf("service: 更新广告主失败: %w", err)
		}

		// 6. 数据库更新成功后，删除旧头像文件（如果有新头像上传）
		if avatarFile != nil && oldAvatarURL != "" && oldAvatarURL != newAvatarURL {
			if err := s.uploadService.DeleteClientAvatar(ctx, oldAvatarURL); err != nil {
				// 记录警告但不失败整个操作，因为主要的业务逻辑（数据库更新）已经成功
				logger.Log.Warn("删除旧头像文件失败",
					logger.String("advertiserID", objectID.Hex()),
					logger.String("oldAvatarUrl", oldAvatarURL),
					logger.String("newAvatarUrl", newAvatarURL),
					logger.Error(err))
			} else {
				logger.Log.Info("旧头像文件删除成功",
					logger.String("advertiserID", objectID.Hex()),
					logger.String("oldAvatarUrl", oldAvatarURL),
					logger.String("newAvatarUrl", newAvatarURL))
			}
		}
	}

	return nil
}

// Delete a client and merge its videos to another client.
func (s *service) Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error {
	// 1. Validate IDs
	sourceID, err := primitive.ObjectIDFromHex(idToDelete)
	if err != nil {
		return ErrInvalidAdvertiserID
	}
	targetID, err := primitive.ObjectIDFromHex(targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("无效的目标广告主ID: %w", err)
	}

	if sourceID == targetID {
		return ErrMergeToSelf
	}

	// 2. Check if this is the last advertiser
	count, err := s.repo.Count(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("无法统计广告主数量: %w", err)
	}
	if count <= 1 {
		return ErrCannotDeleteLast
	}

	// 3. Check if target advertiser exists
	targetExists, err := s.repo.ExistsByID(ctx, targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("检查目标广告主是否存在时出错: %w", err)
	}
	if !targetExists {
		return ErrTargetAdvertiserNotFound
	}

	// 4. Get source advertiser details to delete avatar later
	sourceAdvertiser, err := s.repo.FindByID(ctx, idToDelete)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return fmt.Errorf("查找要删除的广告主失败: %w", err)
	}

	// 5. Perform operations sequentially without a transaction
	// Step 5a: Re-assign videos to the target advertiser
	_, err = s.videoRepo.UpdateAdvertiser(ctx, sourceID, targetID)
	if err != nil {
		return fmt.Errorf("转移视频失败: %w", err)
	}

	// Step 5b: Delete the source advertiser
	if err := s.repo.Delete(ctx, idToDelete); err != nil {
		// If this fails, the videos are already safely migrated.
		// The operation can be safely retried by the user.
		return fmt.Errorf("删除广告主失败 (视频已转移): %w", err)
	}

	// If deletion is successful, delete the avatar file using goupload
	if sourceAdvertiser.AvatarURL != "" {
		if err := s.uploadService.DeleteClientAvatar(ctx, sourceAdvertiser.AvatarURL); err != nil {
			// Log this error but don't fail the whole operation, as the main DB changes are already committed.
			logger.Log.Warn("删除头像文件失败", logger.String("avatarUrl", sourceAdvertiser.AvatarURL), logger.Error(err))
		} else {
			logger.Log.Info("头像文件删除成功", logger.String("avatarUrl", sourceAdvertiser.AvatarURL))
		}
	}

	return nil
}
