package category

import (
	"context"
	"errors"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/domain/common"
)

// Handler 处理分类相关的 HTTP 请求
type Handler struct {
	service Service
}

// NewHandler 创建一个新的分类处理器实例
func NewHandler(service Service) *Handler {
	return &Handler{
		service: service,
	}
}

// --- Response Helpers ---

// --- Handlers ---

// ListCategories 获取所有分类列表
// @Summary 获取所有分类列表
// @Description 获取所有视频分类，按排序字段升序排列
// @Tags categories
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} common.APIResponse{data=ListCategoriesResponseData}
// @Failure 400 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/categories [get]
func (h *Handler) ListCategories(c *gin.Context) {
	var req ListCategoriesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的查询参数", err.Error())
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	responseData, err := h.service.List(ctx, req)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "获取分类列表失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, responseData)
}

// CreateCategory 创建新的分类
// @Summary 创建新的分类
// @Description 创建一个新的视频分类
// @Tags categories
// @Accept json
// @Produce json
// @Param category body CreateCategoryRequest true "分类信息"
// @Success 201 {object} common.APIResponse{data=Category}
// @Failure 400 {object} common.APIResponse
// @Failure 409 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/categories [post]
func (h *Handler) CreateCategory(c *gin.Context) {
	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的请求数据: "+err.Error(), err.Error())
		return
	}

	category, err := h.service.Create(c.Request.Context(), req)
	if err != nil {
		if errors.Is(err, ErrCategoryNameRequired) || errors.Is(err, ErrCategoryNameTooLong) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}

		var writeException mongo.WriteException
		if errors.As(err, &writeException) {
			for _, writeError := range writeException.WriteErrors {
				if writeError.Code == 11000 {
					common.SendError(c, http.StatusConflict, ErrCategoryNameExists.Error(), err.Error())
					return
				}
			}
		}

		common.SendError(c, http.StatusInternalServerError, "创建分类失败", err.Error())
		return
	}
	common.SendSuccess(c, http.StatusCreated, category, "创建成功")
}

// UpdateCategory 更新指定ID的分类
// @Summary 更新分类
// @Description 更新指定ID的视频分类
// @Tags categories
// @Accept json
// @Produce json
// @Param id path string true "分类ID"
// @Param category body UpdateCategoryRequest true "要更新的分类信息"
// @Success 200 {object} common.APIResponse
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 409 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/categories/{id} [put]
func (h *Handler) UpdateCategory(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的请求数据: "+err.Error(), err.Error())
		return
	}

	err := h.service.Update(c.Request.Context(), id, req)
	if err != nil {
		if errors.Is(err, ErrInvalidCategoryID) || errors.Is(err, ErrUpdatePayloadRequired) || errors.Is(err, ErrCategoryNameRequired) || errors.Is(err, ErrCategoryNameTooLong) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrCategoryNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrCategoryNameExists) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "更新分类失败", err.Error())
		return
	}
	common.SendSuccess(c, http.StatusOK, nil, "更新成功")
}

// DeleteCategory 删除指定ID的分类
// @Summary 删除分类
// @Description 删除指定ID的视频分类
// @Tags categories
// @Accept json
// @Produce json
// @Param id path string true "分类ID"
// @Success 200 {object} common.APIResponse
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 409 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/categories/{id} [delete]
func (h *Handler) DeleteCategory(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		common.SendError(c, http.StatusBadRequest, ErrInvalidCategoryID.Error(), "分类ID不能为空")
		return
	}

	err := h.service.Delete(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, ErrInvalidCategoryID) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrCategoryNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrCannotDeleteNoneCategory) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "删除分类失败", err.Error())
		return
	}
	common.SendSuccess(c, http.StatusOK, nil, "删除成功")
}
