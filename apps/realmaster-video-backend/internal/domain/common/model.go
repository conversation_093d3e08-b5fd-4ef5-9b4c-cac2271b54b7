package common

// APIResponse 是所有API响应的基础结构
type APIResponse struct {
	OK   int         `json:"ok"`
	Msg  string      `json:"msg,omitempty"`
	Err  string      `json:"err,omitempty"`
	Data interface{} `json:"data,omitempty"`
}

// Pagination 包含了标准的分页信息
type Pagination struct {
	TotalItems  int64 `json:"totItms"`
	TotalPages  int64 `json:"totPgs"`
	CurrentPage int64 `json:"currPg"`
	Limit       int64 `json:"lim"`
}
