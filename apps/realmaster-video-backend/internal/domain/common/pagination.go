package common

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaginationConfig 分页配置常量
const (
	// 默认分页配置
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPageSize     = 1
	DefaultPage     = 1

	// 业务特定的分页配置
	VideoPageSize     = 20  // 视频列表默认页大小（内容较重）
	AdminPageSize     = 10  // 管理后台默认页大小（便于管理）
	PublicMaxPageSize = 100 // 公共API最大页大小（安全限制）
	AdminMaxPageSize  = 200 // 管理API最大页大小（管理员可以查看更多）
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page  int `form:"page" json:"page"`
	Limit int `form:"limit" json:"limit"`
}

// PaginationType 分页类型，用于区分不同业务场景
type PaginationType string

const (
	PaginationTypeVideo  PaginationType = "video"  // 视频相关API
	PaginationTypeAdmin  PaginationType = "admin"  // 管理后台API
	PaginationTypePublic PaginationType = "public" // 公共API
)

// Validate 验证分页参数
func (p *PaginationParams) Validate() {
	p.ValidateWithType(PaginationTypePublic) // 默认使用公共API的限制
}

// ValidateWithType 根据业务类型验证分页参数
func (p *PaginationParams) ValidateWithType(pType PaginationType) {
	if p.Page <= 0 {
		p.Page = DefaultPage
	}

	// 根据业务类型设置默认值和限制
	switch pType {
	case PaginationTypeVideo:
		if p.Limit <= 0 {
			p.Limit = VideoPageSize
		}
		if p.Limit > PublicMaxPageSize {
			p.Limit = PublicMaxPageSize
		}
	case PaginationTypeAdmin:
		if p.Limit <= 0 {
			p.Limit = AdminPageSize
		}
		if p.Limit > AdminMaxPageSize {
			p.Limit = AdminMaxPageSize
		}
	case PaginationTypePublic:
		if p.Limit <= 0 {
			p.Limit = DefaultPageSize
		}
		if p.Limit > PublicMaxPageSize {
			p.Limit = PublicMaxPageSize
		}
	default:
		if p.Limit <= 0 {
			p.Limit = DefaultPageSize
		}
		if p.Limit > MaxPageSize {
			p.Limit = MaxPageSize
		}
	}
}

// ParsePaginationFromQuery 从查询参数解析分页信息
func ParsePaginationFromQuery(c *gin.Context) PaginationParams {
	return ParsePaginationFromQueryWithType(c, PaginationTypePublic)
}

// ParsePaginationFromQueryWithType 根据业务类型从查询参数解析分页信息
func ParsePaginationFromQueryWithType(c *gin.Context, pType PaginationType) PaginationParams {
	var params PaginationParams

	// 解析页码
	if pageStr := c.DefaultQuery("page", "1"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			params.Page = page
		}
	}

	// 解析每页数量
	if limitStr := c.DefaultQuery("limit", ""); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			params.Limit = limit
		}
	}

	params.ValidateWithType(pType)
	return params
}

// CalculatePagination 计算分页信息
func CalculatePagination(total int64, page, limit int) *Pagination {
	var totalPages int64
	if limit > 0 && total > 0 {
		totalPages = (total + int64(limit) - 1) / int64(limit)
	} else if total > 0 {
		totalPages = 1
	}

	return &Pagination{
		TotalItems:  total,
		TotalPages:  totalPages,
		CurrentPage: int64(page),
		Limit:       int64(limit),
	}
}

// PaginatedResponse 通用分页响应结构
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Pagination *Pagination `json:"pgn"`
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(items interface{}, total int64, page, limit int) *PaginatedResponse {
	return &PaginatedResponse{
		Items:      items,
		Pagination: CalculatePagination(total, page, limit),
	}
}
