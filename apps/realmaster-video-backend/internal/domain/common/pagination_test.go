package common

import (
	"testing"
)

func TestCalculatePagination(t *testing.T) {
	tests := []struct {
		name        string
		totalItems  int64
		page        int
		limit       int
		expectedPgn Pagination
	}{
		{
			name:       "正常分页计算",
			totalItems: 100,
			page:       1,
			limit:      20,
			expectedPgn: Pagination{
				TotalItems:  100,
				TotalPages:  5,
				CurrentPage: 1,
				Limit:       20,
			},
		},
		{
			name:       "不能整除的分页计算",
			totalItems: 101,
			page:       2,
			limit:      20,
			expectedPgn: Pagination{
				TotalItems:  101,
				TotalPages:  6,
				CurrentPage: 2,
				Limit:       20,
			},
		},
		{
			name:       "空结果分页计算",
			totalItems: 0,
			page:       1,
			limit:      20,
			expectedPgn: Pagination{
				TotalItems:  0,
				TotalPages:  0,
				CurrentPage: 1,
				Limit:       20,
			},
		},
		{
			name:       "单页结果",
			totalItems: 5,
			page:       1,
			limit:      20,
			expectedPgn: Pagination{
				TotalItems:  5,
				TotalPages:  1,
				CurrentPage: 1,
				Limit:       20,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculatePagination(tt.totalItems, tt.page, tt.limit)
			
			if result.TotalItems != tt.expectedPgn.TotalItems {
				t.Errorf("TotalItems = %d, want %d", result.TotalItems, tt.expectedPgn.TotalItems)
			}
			if result.TotalPages != tt.expectedPgn.TotalPages {
				t.Errorf("TotalPages = %d, want %d", result.TotalPages, tt.expectedPgn.TotalPages)
			}
			if result.CurrentPage != tt.expectedPgn.CurrentPage {
				t.Errorf("CurrentPage = %d, want %d", result.CurrentPage, tt.expectedPgn.CurrentPage)
			}
			if result.Limit != tt.expectedPgn.Limit {
				t.Errorf("Limit = %d, want %d", result.Limit, tt.expectedPgn.Limit)
			}
		})
	}
}

func TestPaginationParamsValidateWithType(t *testing.T) {
	tests := []struct {
		name     string
		params   PaginationParams
		pType    PaginationType
		expected PaginationParams
	}{
		{
			name:   "视频API默认值",
			params: PaginationParams{Page: 0, Limit: 0},
			pType:  PaginationTypeVideo,
			expected: PaginationParams{Page: 1, Limit: VideoPageSize},
		},
		{
			name:   "管理API默认值",
			params: PaginationParams{Page: 0, Limit: 0},
			pType:  PaginationTypeAdmin,
			expected: PaginationParams{Page: 1, Limit: AdminPageSize},
		},
		{
			name:   "公共API默认值",
			params: PaginationParams{Page: 0, Limit: 0},
			pType:  PaginationTypePublic,
			expected: PaginationParams{Page: 1, Limit: DefaultPageSize},
		},
		{
			name:   "视频API限制检查",
			params: PaginationParams{Page: 1, Limit: 200},
			pType:  PaginationTypeVideo,
			expected: PaginationParams{Page: 1, Limit: PublicMaxPageSize},
		},
		{
			name:   "管理API限制检查",
			params: PaginationParams{Page: 1, Limit: 300},
			pType:  PaginationTypeAdmin,
			expected: PaginationParams{Page: 1, Limit: AdminMaxPageSize},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.params.ValidateWithType(tt.pType)
			
			if tt.params.Page != tt.expected.Page {
				t.Errorf("Page = %d, want %d", tt.params.Page, tt.expected.Page)
			}
			if tt.params.Limit != tt.expected.Limit {
				t.Errorf("Limit = %d, want %d", tt.params.Limit, tt.expected.Limit)
			}
		})
	}
}
