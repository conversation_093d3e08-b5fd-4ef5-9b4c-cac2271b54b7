package common

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/platform/logger"
)

// SendSuccess sends a standardized success response.
func SendSuccess(c *gin.Context, status int, data interface{}, msg ...string) {
	message := "操作成功"
	if len(msg) > 0 {
		message = msg[0]
	}
	c.<PERSON>SO<PERSON>(status, APIResponse{
		OK:   1,
		Msg:  message,
		Data: data,
	})
}

// SendError sends a standardized error response and logs the detailed error.
func SendError(c *gin.Context, status int, errMessage, devMessage string) {
	logger.Log.Error(devMessage, logger.String("error", errMessage))
	c.AbortWithStatusJSON(status, APIResponse{
		OK:  0,
		Err: errMessage,
	})
}

// SendAppError maps common application errors to HTTP status and sends standardized response.
func SendAppError(c *gin.Context, appErr error) {
	status := http.StatusInternalServerError
	msg := "服务器内部错误"

	switch {
	case errors.Is(appErr, ErrInvalidRequest),
		errors.Is(appErr, ErrInvalidID),
		errors.Is(appErr, ErrInvalidCategoryID),
		errors.Is(appErr, ErrInvalidClientID),
		errors.Is(appErr, ErrInvalidAdvertiserID):
		status = http.StatusBadRequest
		msg = "无效的请求参数"
	case errors.Is(appErr, ErrResourceNotFound):
		status = http.StatusNotFound
		msg = "资源未找到"
	case errors.Is(appErr, ErrResourceExists):
		status = http.StatusConflict
		msg = "资源已存在"
	}

	SendError(c, status, msg, appErr.Error())
}
