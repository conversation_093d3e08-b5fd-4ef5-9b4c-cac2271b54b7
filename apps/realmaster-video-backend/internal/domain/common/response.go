package common

import (
	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/platform/logger"
	// zap兼容别名
)

// SendSuccess sends a standardized success response.
func SendSuccess(c *gin.Context, status int, data interface{}, msg ...string) {
	message := "操作成功"
	if len(msg) > 0 {
		message = msg[0]
	}
	c.<PERSON>(status, APIResponse{
		OK:   1,
		Msg:  message,
		Data: data,
	})
}

// SendError sends a standardized error response and logs the detailed error.
func SendError(c *gin.Context, status int, errMessage, devMessage string) {
	logger.Log.Error(devMessage, logger.String("error", errMessage))
	c.AbortWithStatusJSON(status, APIResponse{
		OK:  0,
		Err: errMessage,
	})
}
