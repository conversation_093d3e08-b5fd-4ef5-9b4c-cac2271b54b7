package common

import (
	"regexp"
)

// IsValidEmail 检查邮箱格式是否有效
func IsValidEmail(email string) bool {
	// 一个常用的邮箱正则表达式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsValidPhone 检查电话号码格式是否为有效的北美电话号码
// 支持以下格式：
// - 10位数字: "5551234567"
// - 带+1前缀: "+15551234567"
// - 带括号格式: "(*************"
// - 带连字符格式: "************"
func IsValidPhone(phone string) bool {
	if phone == "" {
		return false
	}

	// 首先检查是否只包含有效的电话号码字符
	// 允许的字符：数字、+、-、.、()、空格
	validCharsRegex := regexp.MustCompile(`^[0-9+\-.()\s]+$`)
	if !validCharsRegex.MatchString(phone) {
		return false
	}

	// 移除所有非数字字符
	digitsOnly := regexp.MustCompile(`\D`).ReplaceAllString(phone, "")

	// 检查是否为10位数字（北美本地号码）
	if len(digitsOnly) == 10 {
		return regexp.MustCompile(`^\d{10}$`).MatchString(digitsOnly)
	}

	// 检查是否为11位数字且以1开头（带+1国家代码的北美号码）
	if len(digitsOnly) == 11 && digitsOnly[0] == '1' {
		return regexp.MustCompile(`^1\d{10}$`).MatchString(digitsOnly)
	}

	return false
}

// NormalizePhone 将电话号码标准化为10位数字格式
// 输入: "+15551234567", "(*************", "************" 等
// 输出: "5551234567" (统一的10位数字格式)
func NormalizePhone(phone string) string {
	if phone == "" {
		return ""
	}

	// 移除所有非数字字符
	digitsOnly := regexp.MustCompile(`\D`).ReplaceAllString(phone, "")

	// 如果是11位数字且以1开头，移除国家代码
	if len(digitsOnly) == 11 && digitsOnly[0] == '1' {
		return digitsOnly[1:] // 返回后10位
	}

	// 如果是10位数字，直接返回
	if len(digitsOnly) == 10 {
		return digitsOnly
	}

	// 其他情况返回原始输入（让验证函数处理错误）
	return phone
}
