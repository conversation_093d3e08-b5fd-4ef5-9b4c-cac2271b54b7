package common

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestIsValidPhone 测试电话号码验证函数
func TestIsValidPhone(t *testing.T) {
	tests := []struct {
		name     string
		phone    string
		expected bool
	}{
		// 有效的10位数字格式
		{"10位数字", "5551234567", true},
		{"10位数字-不同号码", "4161234567", true},
		
		// 有效的+1前缀格式
		{"带+1前缀", "+15551234567", true},
		{"带+1前缀-不同号码", "+14161234567", true},
		
		// 有效的格式化电话号码
		{"带括号格式", "(*************", true},
		{"带连字符格式", "************", true},
		{"带点分隔格式", "************", true},
		{"带空格格式", "************", true},
		{"混合格式", "+****************", true},
		{"混合格式2", "+1-************", true},
		
		// 无效格式
		{"空字符串", "", false},
		{"太短", "123456789", false},
		{"太长", "123456789012", false},
		{"11位但不以1开头", "25551234567", false},
		{"包含字母", "555abc4567", false},
		{"只有字母", "abcdefghij", false},
		{"特殊字符", "555@123#4567", false},
		
		// 边界情况
		{"只有+号", "+", false},
		{"只有1", "1", false},
		{"11位全1", "11111111111", true}, // 以1开头的11位数字
		{"10位全0", "0000000000", true},  // 10位数字
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidPhone(tt.phone)
			assert.Equal(t, tt.expected, result, 
				"IsValidPhone(%q) = %v, expected %v", tt.phone, result, tt.expected)
		})
	}
}

// TestNormalizePhone 测试电话号码标准化函数
func TestNormalizePhone(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		// 标准化+1前缀
		{"带+1前缀", "+15551234567", "5551234567"},
		{"带+1前缀-不同号码", "+14161234567", "4161234567"},
		
		// 10位数字保持不变
		{"10位数字", "5551234567", "5551234567"},
		{"10位数字-不同号码", "4161234567", "4161234567"},
		
		// 格式化电话号码标准化
		{"带括号格式", "(*************", "5551234567"},
		{"带连字符格式", "************", "5551234567"},
		{"带点分隔格式", "************", "5551234567"},
		{"带空格格式", "************", "5551234567"},
		{"混合格式", "+****************", "5551234567"},
		{"混合格式2", "+1-************", "5551234567"},
		
		// 边界情况
		{"空字符串", "", ""},
		{"无效长度保持原样", "123456789", "123456789"},
		{"11位不以1开头保持原样", "25551234567", "25551234567"},
		
		// 特殊情况
		{"11位全1", "11111111111", "1111111111"}, // 移除第一个1
		{"10位全0", "0000000000", "0000000000"},  // 保持不变
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizePhone(tt.input)
			assert.Equal(t, tt.expected, result,
				"NormalizePhone(%q) = %q, expected %q", tt.input, result, tt.expected)
		})
	}
}

// TestIsValidEmail 测试邮箱验证函数
func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		// 有效邮箱
		{"标准邮箱", "<EMAIL>", true},
		{"带数字邮箱", "<EMAIL>", true},
		{"带特殊字符邮箱", "<EMAIL>", true},
		{"带连字符邮箱", "<EMAIL>", true},
		
		// 无效邮箱
		{"空字符串", "", false},
		{"缺少@符号", "testexample.com", false},
		{"缺少域名", "test@", false},
		{"缺少用户名", "@example.com", false},
		{"缺少顶级域名", "test@example", false},
		{"多个@符号", "test@@example.com", false},
		{"域名太短", "test@a.b", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidEmail(tt.email)
			assert.Equal(t, tt.expected, result,
				"IsValidEmail(%q) = %v, expected %v", tt.email, result, tt.expected)
		})
	}
}

// TestPhoneValidationIntegration 测试电话号码验证和标准化的集成
func TestPhoneValidationIntegration(t *testing.T) {
	testCases := []string{
		"+15551234567",
		"(*************",
		"************",
		"************",
		"************",
		"+****************",
	}

	for _, input := range testCases {
		t.Run("Integration_"+input, func(t *testing.T) {
			// 首先验证输入是有效的
			assert.True(t, IsValidPhone(input), "输入应该是有效的电话号码: %s", input)
			
			// 然后标准化
			normalized := NormalizePhone(input)
			assert.Equal(t, "5551234567", normalized, "标准化后应该是10位数字: %s -> %s", input, normalized)
			
			// 标准化后的结果也应该是有效的
			assert.True(t, IsValidPhone(normalized), "标准化后的结果应该仍然有效: %s", normalized)
		})
	}
}
