package interaction

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goauth/middleware"

	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/platform/logger"
)

// Handler 处理交互相关的HTTP请求
type Handler struct {
	service Service
}

// NewHandler 创建新的交互处理器
func NewHandler(service Service) *Handler {
	return &Handler{
		service: service,
	}
}

// CreateInteraction 创建交互事件
// POST /video/public/interactions
func (h *Handler) CreateInteraction(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析请求体
	var req CreateInteractionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Error("解析交互请求失败",
			logger.Error(err),
			logger.String("userId", userID),
		)
		common.SendError(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 验证交互类型
	if !isValidInteractionType(req.Type) {
		logger.Log.Error("无效的交互类型",
			logger.String("type", string(req.Type)),
			logger.String("userId", userID),
		)
		common.SendError(c, http.StatusBadRequest, "无效的交互类型", string(req.Type))
		return
	}

	// 创建交互事件
	response, err := h.service.CreateInteraction(c.Request.Context(), userID, req)
	if err != nil {
		logger.Log.Error("创建交互事件失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.String("videoId", req.VideoID),
			logger.String("type", string(req.Type)),
		)
		common.SendError(c, http.StatusInternalServerError, "创建交互事件失败", err.Error())
		return
	}

	logger.Log.Info("成功创建交互事件",
		logger.String("id", response.ID.Hex()),
		logger.String("userId", userID),
		logger.String("videoId", req.VideoID),
		logger.String("type", string(req.Type)),
	)

	common.SendSuccess(c, http.StatusOK, response, "交互事件创建成功")
}

// GetUserInteractions 获取用户交互历史
// GET /video/public/interactions/user
func (h *Handler) GetUserInteractions(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50 // 默认限制50条，最大100条
	}

	// 获取用户交互历史
	interactions, err := h.service.GetUserInteractions(c.Request.Context(), userID, limit)
	if err != nil {
		logger.Log.Error("获取用户交互历史失败",
			logger.Error(err),
			logger.String("userId", userID),
		)
		common.SendError(c, http.StatusInternalServerError, "获取交互历史失败", err.Error())
		return
	}

	logger.Log.Info("成功获取用户交互历史",
		logger.String("userId", userID),
		logger.Int("count", len(interactions)),
	)

	common.SendSuccess(c, http.StatusOK, gin.H{
		"interactions": interactions,
		"count":        len(interactions),
	}, "获取交互历史成功")
}

// GetVideoInteractions 获取视频交互历史
// GET /video/public/interactions/video/:videoId
func (h *Handler) GetVideoInteractions(c *gin.Context) {
	// 从JWT中获取用户ID（验证登录状态）
	_, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 获取视频ID
	videoID := c.Param("videoId")
	if videoID == "" {
		common.SendError(c, http.StatusBadRequest, "视频ID不能为空", "")
		return
	}

	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50 // 默认限制50条，最大100条
	}

	// 获取视频交互历史
	interactions, err := h.service.GetVideoInteractions(c.Request.Context(), videoID, limit)
	if err != nil {
		logger.Log.Error("获取视频交互历史失败",
			logger.Error(err),
			logger.String("videoId", videoID),
		)
		common.SendError(c, http.StatusInternalServerError, "获取交互历史失败", err.Error())
		return
	}

	logger.Log.Info("成功获取视频交互历史",
		logger.String("videoId", videoID),
		logger.Int("count", len(interactions)),
	)

	common.SendSuccess(c, http.StatusOK, gin.H{
		"interactions": interactions,
		"count":        len(interactions),
	}, "获取交互历史成功")
}

// isValidInteractionType 验证交互类型是否有效
func isValidInteractionType(t InteractionType) bool {
	validTypes := []InteractionType{
		TypeViewStart,
		TypeViewProgress,
		TypeViewComplete,
		TypeLike,
		TypeUnlike,
		TypeFavorite,
		TypeUnfavorite,
		TypeShare,
		TypeClickListingLink,
		TypeBlockVideo,
	}

	for _, validType := range validTypes {
		if t == validType {
			return true
		}
	}
	return false
}
