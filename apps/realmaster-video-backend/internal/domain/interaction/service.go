package interaction

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
)

// Service 定义交互服务接口
type Service interface {
	CreateInteraction(ctx context.Context, userID string, req CreateInteractionRequest) (*InteractionResponse, error)
	GetUserInteractions(ctx context.Context, userID string, limit int) ([]InteractionResponse, error)
	GetVideoInteractions(ctx context.Context, videoID string, limit int) ([]InteractionResponse, error)
}

type service struct {
	interactionRepo Repository
	stateRepo       state.Repository
	videoRepo       video.Repository
	txManager       *database.TransactionManager
}

// NewService 创建新的交互服务实例
func NewService(interactionRepo Repository, stateRepo state.Repository, videoRepo video.Repository, txManager *database.TransactionManager) Service {
	return &service{
		interactionRepo: interactionRepo,
		stateRepo:       stateRepo,
		videoRepo:       videoRepo,
		txManager:       txManager,
	}
}

// CreateInteraction 创建交互事件（包含事务处理）
func (s *service) CreateInteraction(ctx context.Context, userID string, req CreateInteractionRequest) (*InteractionResponse, error) {
	// 转换用户ID
	userObjectID, err := s.convertUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("无效的用户ID: %w", err)
	}

	// 转换视频ID
	videoObjectID, err := primitive.ObjectIDFromHex(req.VideoID)
	if err != nil {
		return nil, fmt.Errorf("无效的视频ID: %w", err)
	}

	// 创建交互记录
	interaction := &Interaction{
		UID:  userObjectID,
		VID:  videoObjectID,
		Type: req.Type,
		Meta: req.Meta,
	}

	var response *InteractionResponse

	// 使用事务处理
	err = s.txManager.WithTransaction(ctx, func(ctx context.Context) error {
		// 1. 创建交互事件记录
		if err := s.interactionRepo.Create(ctx, interaction); err != nil {
			return fmt.Errorf("创建交互事件失败: %w", err)
		}

		// 2. 更新状态记录
		if err := s.updateUserState(ctx, userObjectID, videoObjectID, req.Type, req.Meta); err != nil {
			return fmt.Errorf("更新用户状态失败: %w", err)
		}

		// 3. 更新视频统计（如果视频不存在，记录警告但不失败）
		if err := s.updateVideoStats(ctx, videoObjectID, req.Type); err != nil {
			// 检查是否是视频不存在的错误
			if errors.Is(err, video.ErrVideoNotFound) {
				logger.Log.Warn("视频不存在，跳过统计更新",
					logger.String("videoId", req.VideoID),
					logger.String("userId", userID),
					logger.String("type", string(req.Type)),
				)
				// 不返回错误，允许交互事件继续创建
			} else {
				return fmt.Errorf("更新视频统计失败: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		logger.Log.Error("创建交互事件事务失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.String("videoId", req.VideoID),
			logger.String("type", string(req.Type)),
		)
		return nil, err
	}

	// 构建响应
	response = &InteractionResponse{
		ID:        interaction.ID,
		UserID:    interaction.UID,
		VideoID:   interaction.VID,
		Type:      interaction.Type,
		Meta:      interaction.Meta,
		Timestamp: interaction.Timestamp,
	}

	logger.Log.Info("成功创建交互事件",
		logger.String("id", interaction.ID.Hex()),
		logger.String("userId", userID),
		logger.String("videoId", req.VideoID),
		logger.String("type", string(req.Type)),
	)

	return response, nil
}

// updateUserState 更新用户状态
func (s *service) updateUserState(ctx context.Context, userID, videoID primitive.ObjectID, interactionType InteractionType, meta map[string]interface{}) error {
	// 获取当前状态
	currentState, err := s.stateRepo.FindByUserAndVideo(ctx, userID, videoID)
	if err != nil {
		return err
	}

	// 如果没有状态记录，创建新的
	if currentState == nil {
		currentState = state.NewState(userID, videoID)
	}

	// 根据交互类型更新状态
	switch interactionType {
	case TypeLike:
		currentState.Liked = true
	case TypeUnlike:
		currentState.Liked = false
	case TypeFavorite:
		currentState.Favorited = true
	case TypeUnfavorite:
		currentState.Favorited = false
	case TypeBlockVideo:
		currentState.Blocked = true
	case TypeViewStart:
		// 移除ViewStatus字段，只记录观看开始事件
		// 不需要更新状态，因为已经移除了st字段
	case TypeViewProgress:
		// 只更新观看进度，不更新观看状态
		if meta != nil {
			if progSec, ok := meta["progSec"]; ok {
				if progSecFloat, ok := progSec.(float64); ok {
					currentState.ProgressSeconds = int(progSecFloat)
				} else if progSecInt, ok := progSec.(int); ok {
					currentState.ProgressSeconds = progSecInt
				}
			}
		}
	case TypeViewComplete:
		// 只更新观看进度，不更新观看状态
		if meta != nil {
			if progSec, ok := meta["progSec"]; ok {
				if progSecFloat, ok := progSec.(float64); ok {
					currentState.ProgressSeconds = int(progSecFloat)
				} else if progSecInt, ok := progSec.(int); ok {
					currentState.ProgressSeconds = progSecInt
				}
			}
		}
	}

	// 保存状态（gomongo会自动更新_mt时间戳）
	return s.stateRepo.Upsert(ctx, currentState)
}

// updateVideoStats 更新视频统计
func (s *service) updateVideoStats(ctx context.Context, videoID primitive.ObjectID, interactionType InteractionType) error {
	var views, likes, collections, completions int64

	switch interactionType {
	case TypeViewStart:
		views = 1
	case TypeLike:
		likes = 1
	case TypeUnlike:
		likes = -1
	case TypeFavorite:
		collections = 1
	case TypeUnfavorite:
		collections = -1
	case TypeViewComplete:
		completions = 1
	default:
		// 其他类型不影响统计
		return nil
	}

	return s.videoRepo.IncrementStats(ctx, videoID.Hex(), views, likes, collections, completions)
}

// convertUserID 将字符串用户ID转换为ObjectID
func (s *service) convertUserID(userIDStr string) (primitive.ObjectID, error) {
	// 如果是ObjectID格式，直接转换
	if objectID, err := primitive.ObjectIDFromHex(userIDStr); err == nil {
		return objectID, nil
	}

	// 如果是字符串格式（如dev_admin_001），需要转换
	// 这里使用简单的哈希方法，实际项目中可能需要更复杂的映射
	// 为了保持一致性，我们可以使用用户ID的哈希值生成ObjectID
	return s.generateObjectIDFromString(userIDStr), nil
}

// generateObjectIDFromString 从字符串生成一致的ObjectID
func (s *service) generateObjectIDFromString(str string) primitive.ObjectID {
	// 使用字符串的哈希值生成ObjectID
	// 这确保相同的字符串总是生成相同的ObjectID
	hash := 0
	for _, char := range str {
		hash = hash*31 + int(char)
	}

	// 将哈希值转换为12字节的ObjectID
	bytes := make([]byte, 12)
	for i := 0; i < 12; i++ {
		bytes[i] = byte(hash >> (i * 8))
	}

	return primitive.ObjectID(bytes)
}

// GetUserInteractions 获取用户交互历史
func (s *service) GetUserInteractions(ctx context.Context, userID string, limit int) ([]InteractionResponse, error) {
	userObjectID, err := s.convertUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("无效的用户ID: %w", err)
	}

	interactions, err := s.interactionRepo.FindByUser(ctx, userObjectID, int64(limit))
	if err != nil {
		return nil, err
	}

	var responses []InteractionResponse
	for _, interaction := range interactions {
		responses = append(responses, InteractionResponse{
			ID:        interaction.ID,
			UserID:    interaction.UID,
			VideoID:   interaction.VID,
			Type:      interaction.Type,
			Meta:      interaction.Meta,
			Timestamp: interaction.Timestamp,
		})
	}

	return responses, nil
}

// GetVideoInteractions 获取视频交互历史
func (s *service) GetVideoInteractions(ctx context.Context, videoID string, limit int) ([]InteractionResponse, error) {
	videoObjectID, err := primitive.ObjectIDFromHex(videoID)
	if err != nil {
		return nil, fmt.Errorf("无效的视频ID: %w", err)
	}

	interactions, err := s.interactionRepo.FindByVideo(ctx, videoObjectID, int64(limit))
	if err != nil {
		return nil, err
	}

	var responses []InteractionResponse
	for _, interaction := range interactions {
		responses = append(responses, InteractionResponse{
			ID:        interaction.ID,
			UserID:    interaction.UID,
			VideoID:   interaction.VID,
			Type:      interaction.Type,
			Meta:      interaction.Meta,
			Timestamp: interaction.Timestamp,
		})
	}

	return responses, nil
}
