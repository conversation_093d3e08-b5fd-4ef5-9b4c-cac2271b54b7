package main_property

import (
	"encoding/json"
	"strconv"
)

// NullableFloat可以处理来自JSON的数字或"N/A"字符串。
type NullableFloat struct {
	Value float64
	Valid bool // 如果值是数字而不是"N/A"，则为true
}

// UnmarshalJSON为NullableFloat实现自定义解组逻辑。
func (nf *NullableFloat) UnmarshalJSON(data []byte) error {
	// 首先，检查它是否是字符串"N/A"。
	var s string
	if err := json.Unmarshal(data, &s); err == nil {
		if s == "N/A" {
			nf.Valid = false
			return nil
		}
		// API也可能将数字作为字符串发送。
		f, err := strconv.ParseFloat(s, 64)
		if err == nil {
			nf.Value = f
			nf.Valid = true
			return nil
		}
	}

	// 如果不是字符串，则尝试将其解组为浮点数。
	var f float64
	if err := json.Unmarshal(data, &f); err == nil {
		nf.Value = f
		nf.Valid = true
		return nil
	}

	// 如果两者都不是，则认为它无效，但不使整个解析失败。
	nf.Valid = false
	return nil
}

// String返回数字的字符串表示形式，如果无效则返回空字符串。
func (nf NullableFloat) String() string {
	if !nf.Valid {
		return ""
	}
	// 'g'用于格式化浮点数，不带无关的尾随零。
	return strconv.FormatFloat(nf.Value, 'g', -1, 64)
}

// ExternalAPIResponse 用于解码外部API响应的顶层结构
type ExternalAPIResponse struct {
	Ok   int                `json:"ok"`
	List []ExternalProperty `json:"l"`
}

// ExternalProperty 用于解码来自外部API列表中的房源对象
// 只包含我们需要提取的字段
type ExternalProperty struct {
	ID         string `json:"_id"`
	SearchAddr string `json:"searchAddr"`
	Price      string `json:"priceValStrRed"`
	ThumbURL   string `json:"thumbUrl"`
	SaleOrRent string `json:"saleOrRent"`
	City       string `json:"city_en"`
	Province   string `json:"prov_en"`
	Community  string `json:"cmty"`
	WebURL     string `json:"webUrl"`

	// 用于计算卧室的字段
	RmBdr  string        `json:"rmbdrm"`
	Bdrms  NullableFloat `json:"bdrms"`
	TBdrms NullableFloat `json:"tbdrms"`
	BrPlus NullableFloat `json:"br_plus"`

	// 用于计算卫生间的字段
	RmBthrm NullableFloat `json:"rmbthrm"`
	TBthrms NullableFloat `json:"tbthrms"`
	Bthrms  NullableFloat `json:"bthrms"`

	// 用于计算停车位的字段
	RmGr NullableFloat `json:"rmgr"`
	TGr  NullableFloat `json:"tgr"`
	Gr   NullableFloat `json:"gr"`
}

// MainProperty 是我们应用内部使用的、干净的房源结构
type MainProperty struct {
	ID         string `json:"id"`
	SearchAddr string `json:"searchAddr"`
	Price      string `json:"price"`
	ThumbURL   string `json:"thumbUrl"`
	SaleOrRent string `json:"saleOrRent"`
	City       string `json:"city"`
	Province   string `json:"prov"`
	Community  string `json:"cmty"`
	WebURL     string `json:"webUrl"`
	Bedroom    string `json:"bedroom"`
	Bathroom   string `json:"bathroom"`
	Parking    string `json:"parking"`
}

// Transform 将外部房源数据转换为内部房源模型，并执行计算逻辑
func Transform(extProp *ExternalProperty) *MainProperty {
	// 计算卧室
	var bedroom string
	if extProp.RmBdr != "" && extProp.RmBdr != "N/A" {
		bedroom = extProp.RmBdr
	} else {
		bdrmsStr := extProp.Bdrms.String()
		tbdrmsStr := extProp.TBdrms.String()

		if tbdrmsStr != "" {
			bedroom = tbdrmsStr
		} else if bdrmsStr != "" {
			bedroom = bdrmsStr
		}

		brPlusStr := extProp.BrPlus.String()
		if brPlusStr != "" && brPlusStr != "0" {
			if bedroom != "" {
				bedroom += "+" + brPlusStr
			} else {
				bedroom = brPlusStr
			}
		}
	}

	// 计算卫生间
	var bathroom string
	rmbthrmStr := extProp.RmBthrm.String()
	tbthrmsStr := extProp.TBthrms.String()
	bthrmsStr := extProp.Bthrms.String()
	if rmbthrmStr != "" {
		bathroom = rmbthrmStr
	} else if tbthrmsStr != "" {
		bathroom = tbthrmsStr
	} else {
		bathroom = bthrmsStr
	}

	// 计算停车位
	var parking string
	rmgrStr := extProp.RmGr.String()
	if rmgrStr != "" {
		parking = rmgrStr
	} else {
		tgrStr := extProp.TGr.String()
		grStr := extProp.Gr.String()
		if tgrStr != "" {
			parking = tgrStr
		} else {
			parking = grStr
		}
	}

	return &MainProperty{
		ID:         extProp.ID,
		SearchAddr: extProp.SearchAddr,
		Price:      extProp.Price,
		ThumbURL:   extProp.ThumbURL,
		SaleOrRent: extProp.SaleOrRent,
		City:       extProp.City,
		Province:   extProp.Province,
		Community:  extProp.Community,
		WebURL:     extProp.WebURL,
		Bedroom:    bedroom,
		Bathroom:   bathroom,
		Parking:    parking,
	}
}

// SearchByKeywordRequest 定义了通过关键词搜索房源的请求体
type SearchByKeywordRequest struct {
	Keyword string `json:"s" binding:"required"`
}

// BatchGetRequest 定义了通过ID批量获取多个房源的请求体
type BatchGetRequest struct {
	IDs []string `json:"ids" binding:"required"`
}
