package main_property

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"realmaster-video-backend/internal/platform/logger"
)

const (
	baseURL        = "https://www.realmaster.com/1.5/props/autocompleteGetNext"
	requestTimeout = 10 * time.Second
)

// Repository 定义了房源数据的访问接口
type Repository interface {
	SearchByQuery(ctx context.Context, query string) ([]MainProperty, error)
	SearchByID(ctx context.Context, id string) (*MainProperty, error)
	SearchByIDs(ctx context.Context, ids []string) ([]MainProperty, error)
}

type repository struct {
	httpClient *http.Client
}

// NewRepository 创建一个新的房源仓库实例
func NewRepository() Repository {
	return &repository{
		httpClient: &http.Client{
			Timeout: requestTimeout,
		},
	}
}

// SearchByQuery 通过关键词搜索房源
func (r *repository) SearchByQuery(ctx context.Context, query string) ([]MainProperty, error) {
	return r.search(ctx, query)
}

// SearchByIDs 通过ID列表搜索房源
func (r *repository) SearchByIDs(ctx context.Context, ids []string) ([]MainProperty, error) {
	if len(ids) == 0 {
		return []MainProperty{}, nil
	}
	return r.search(ctx, ids)
}

// SearchByID 通过ID搜索单个房源
func (r *repository) SearchByID(ctx context.Context, id string) (*MainProperty, error) {
	properties, err := r.search(ctx, id)
	if err != nil {
		return nil, err
	}
	if len(properties) == 0 {
		return nil, nil // 未找到
	}
	return &properties[0], nil
}

// search 是一个内部辅助函数，用于执行对外部API的POST请求
// 它接受一个 interface{} 作为 's' 字段的值，以处理字符串和字符串切片两种情况
func (r *repository) search(ctx context.Context, sValue interface{}) ([]MainProperty, error) {
	// 创建请求体
	requestData := map[string]interface{}{
		"s":  sValue,
		"ts": time.Now().UnixMilli(),
	}
	reqBody, err := json.Marshal(requestData)
	if err != nil {
		logger.Log.Error("序列化房源搜索请求体失败", logger.Error(err))
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, baseURL, bytes.NewBuffer(reqBody))
	if err != nil {
		logger.Log.Error("创建房源API请求失败", logger.Error(err), logger.String("url", baseURL))
		return nil, fmt.Errorf("创建房源API请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	resp, err := r.httpClient.Do(req)
	if err != nil {
		logger.Log.Error("请求外部房源API失败", logger.Error(err), logger.String("url", baseURL))
		return nil, fmt.Errorf("请求外部房源API失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Log.Error("外部房源API返回非200状态码", logger.Int("status", resp.StatusCode), logger.String("url", baseURL))
		return nil, fmt.Errorf("外部房源API返回错误状态码: %d", resp.StatusCode)
	}

	// 解码响应
	var apiResponse ExternalAPIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		logger.Log.Error("解码房源API响应失败", logger.Error(err))
		return nil, fmt.Errorf("解码房源API响应失败: %w", err)
	}

	if apiResponse.Ok != 1 {
		logger.Log.Warn("外部房源API指示失败", logger.Int("ok_status", apiResponse.Ok))
		return []MainProperty{}, nil
	}

	// 使用 Transform 函数将外部模型映射到我们的内部模型，并执行计算
	mainProperties := make([]MainProperty, 0, len(apiResponse.List))
	for i := range apiResponse.List {
		mainProperties = append(mainProperties, *Transform(&apiResponse.List[i]))
	}

	return mainProperties, nil
}
