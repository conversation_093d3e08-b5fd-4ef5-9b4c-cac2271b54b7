package main_property

import (
	"context"
	"errors"
)

var (
	ErrPropertyNotFound    = errors.New("房源未找到")
	ErrInvalidPropertyID   = errors.New("无效的房源ID格式")
	ErrKeywordRequired     = errors.New("搜索关键词不能为空")
	ErrPropertyIDsRequired = errors.New("房源ID列表不能为空")
)

// Service 定义了房源相关的业务逻辑接口
type Service interface {
	SearchByKeyword(ctx context.Context, keyword string) ([]MainProperty, error)
	GetByID(ctx context.Context, id string) (*MainProperty, error)
	GetByIDs(ctx context.Context, ids []string) ([]MainProperty, error)
}

type service struct {
	repo Repository
}

// NewService 创建一个新的房源服务实例
func NewService(repo Repository) Service {
	return &service{repo: repo}
}

// SearchByKeyword 通过关键词搜索房源
func (s *service) SearchByKeyword(ctx context.Context, keyword string) ([]MainProperty, error) {
	properties, err := s.repo.SearchByQuery(ctx, keyword)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

// GetByID 通过ID获取单个房源
func (s *service) GetByID(ctx context.Context, id string) (*MainProperty, error) {
	property, err := s.repo.SearchByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if property == nil {
		return nil, ErrPropertyNotFound
	}
	return property, nil
}

// GetByIDs 通过ID列表批量获取房源
func (s *service) GetByIDs(ctx context.Context, ids []string) ([]MainProperty, error) {
	if len(ids) == 0 {
		return []MainProperty{}, nil
	}
	// Repository现在直接处理ID切片
	properties, err := s.repo.SearchByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	return properties, nil
}
