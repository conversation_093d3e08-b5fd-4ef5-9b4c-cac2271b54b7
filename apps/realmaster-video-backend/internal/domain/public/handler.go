package public

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goauth/middleware"

	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/platform/logger"
)

// Handler 处理公共API相关的HTTP请求
type Handler struct {
	service Service
}

// NewHandler 创建新的公共API处理器
func NewHandler(service Service) *Handler {
	return &Handler{
		service: service,
	}
}

// GetFeed 获取视频Feed
// GET /video/public/feed
func (h *Handler) GetFeed(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析分页参数
	pagination := common.ParsePaginationFromQueryWithType(c, common.PaginationTypeVideo)
	page := pagination.Page
	limit := pagination.Limit

	// 获取Feed
	feed, err := h.service.GetFeed(c.Request.Context(), userID, page, limit)
	if err != nil {
		logger.Log.Error("获取Feed失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("page", page),
			logger.Int("limit", limit),
		)
		common.SendError(c, http.StatusInternalServerError, "获取Feed失败", err.Error())
		return
	}

	logger.Log.Info("成功获取Feed",
		logger.String("userId", userID),
		logger.Int("page", page),
		logger.Int("limit", limit),
		logger.Int("videoCount", len(feed.Videos)),
	)

	common.SendSuccess(c, http.StatusOK, feed, "获取Feed成功")
}

// GetUserStates 批量获取用户状态
// POST /video/public/states/batch
func (h *Handler) GetUserStates(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析请求体
	var req state.BatchStateQuery
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Error("解析批量状态查询请求失败",
			logger.Error(err),
			logger.String("userId", userID),
		)
		common.SendError(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 验证用户ID是否匹配（确保用户只能查询自己的状态）
	if req.UserID != userID {
		logger.Log.Error("用户ID不匹配",
			logger.String("jwtUserId", userID),
			logger.String("requestUserId", req.UserID),
		)
		common.SendError(c, http.StatusForbidden, "只能查询自己的状态", "")
		return
	}

	// 验证视频ID列表
	if len(req.VideoIDs) == 0 {
		common.SendError(c, http.StatusBadRequest, "视频ID列表不能为空", "")
		return
	}

	if len(req.VideoIDs) > 100 {
		common.SendError(c, http.StatusBadRequest, "一次最多查询100个视频的状态", "")
		return
	}

	// 获取用户状态
	states, err := h.service.GetUserStates(c.Request.Context(), userID, req.VideoIDs)
	if err != nil {
		logger.Log.Error("批量获取用户状态失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("videoCount", len(req.VideoIDs)),
		)
		common.SendError(c, http.StatusInternalServerError, "获取用户状态失败", err.Error())
		return
	}

	logger.Log.Info("成功批量获取用户状态",
		logger.String("userId", userID),
		logger.Int("requestedCount", len(req.VideoIDs)),
		logger.Int("returnedCount", len(states)),
	)

	common.SendSuccess(c, http.StatusOK, gin.H{
		"states": states,
		"count":  len(states),
	}, "获取用户状态成功")
}

// GetUserStatesQuery 通过查询参数批量获取用户状态
// GET /video/public/states?videoIds=id1,id2,id3
func (h *Handler) GetUserStatesQuery(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析视频ID列表
	videoIDsStr := c.Query("videoIds")
	if videoIDsStr == "" {
		common.SendError(c, http.StatusBadRequest, "视频ID列表不能为空", "")
		return
	}

	videoIDs := strings.Split(videoIDsStr, ",")
	if len(videoIDs) > 100 {
		common.SendError(c, http.StatusBadRequest, "一次最多查询100个视频的状态", "")
		return
	}

	// 获取用户状态
	states, err := h.service.GetUserStates(c.Request.Context(), userID, videoIDs)
	if err != nil {
		logger.Log.Error("批量获取用户状态失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("videoCount", len(videoIDs)),
		)
		common.SendError(c, http.StatusInternalServerError, "获取用户状态失败", err.Error())
		return
	}

	logger.Log.Info("成功批量获取用户状态",
		logger.String("userId", userID),
		logger.Int("requestedCount", len(videoIDs)),
		logger.Int("returnedCount", len(states)),
	)

	common.SendSuccess(c, http.StatusOK, gin.H{
		"states": states,
		"count":  len(states),
	}, "获取用户状态成功")
}

// GetUserFavorites 获取用户收藏
// GET /video/public/favorites
func (h *Handler) GetUserFavorites(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 解析分页参数
	pagination := common.ParsePaginationFromQueryWithType(c, common.PaginationTypeVideo)
	page := pagination.Page
	limit := pagination.Limit

	// 获取用户收藏
	favorites, err := h.service.GetUserFavorites(c.Request.Context(), userID, page, limit)
	if err != nil {
		logger.Log.Error("获取用户收藏失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("page", page),
			logger.Int("limit", limit),
		)
		common.SendError(c, http.StatusInternalServerError, "获取用户收藏失败", err.Error())
		return
	}

	logger.Log.Info("成功获取用户收藏",
		logger.String("userId", userID),
		logger.Int("page", page),
		logger.Int("limit", limit),
		logger.Int("favoriteCount", len(favorites.Favorites)),
	)

	common.SendSuccess(c, http.StatusOK, favorites, "获取用户收藏成功")
}
