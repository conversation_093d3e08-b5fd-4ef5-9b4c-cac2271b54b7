package state

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 移除ViewStatus相关定义，因为已经去掉了st字段

// StateID 复合主键结构
type StateID struct {
	UserID  primitive.ObjectID `bson:"uid" json:"userId"`  // 用户ID
	VideoID primitive.ObjectID `bson:"vid" json:"videoId"` // 视频ID
}

// State 用户视频状态快照模型
type State struct {
	ID              StateID `bson:"_id" json:"id"`                  // 复合主键
	Liked           bool    `bson:"liked" json:"liked"`             // 是否点赞 (默认: false)
	Favorited       bool    `bson:"faved" json:"favorited"`         // 是否收藏 (默认: false)
	Blocked         bool    `bson:"blkd" json:"blocked"`            // 是否屏蔽 (默认: false)
	ProgressSeconds int     `bson:"progSec" json:"progressSeconds"` // 观看进度（秒） (默认: 0)
	// 移除手动时间戳字段，使用gomongo自动管理的_ts和_mt
	// 移除st字段（观看状态），只保留观看进度
}

// NewState 创建一个具有默认值的新State
func NewState(userID, videoID primitive.ObjectID) *State {
	return &State{
		ID: StateID{
			UserID:  userID,
			VideoID: videoID,
		},
		Liked:           false, // 默认未点赞
		Favorited:       false, // 默认未收藏
		Blocked:         false, // 默认未屏蔽
		ProgressSeconds: 0,     // 默认进度为0
		// gomongo会自动添加_ts和_mt时间戳
	}
}

// BatchStateQuery 批量查询状态的请求
type BatchStateQuery struct {
	UserID   string   `json:"userId" binding:"required"`
	VideoIDs []string `json:"videoIds" binding:"required"`
}

// StateResponse 状态响应
type StateResponse struct {
	UserID          primitive.ObjectID `json:"userId"`
	VideoID         primitive.ObjectID `json:"videoId"`
	Liked           bool               `json:"liked"`
	Favorited       bool               `json:"favorited"`
	Blocked         bool               `json:"blocked"`
	ProgressSeconds int                `json:"progressSeconds"`
	// TODO: 添加gomongo时间戳支持
	// ModifiedTime    time.Time          `json:"modifiedTime,omitempty"`
}

// UserFavoritesQuery 用户收藏查询
type UserFavoritesQuery struct {
	UserID string `json:"userId" binding:"required"`
	Page   int    `json:"page,omitempty"`
	Limit  int    `json:"limit,omitempty"`
}

// FavoriteVideoResponse 收藏视频响应
type FavoriteVideoResponse struct {
	VideoID         primitive.ObjectID `json:"videoId"`
	ProgressSeconds int                `json:"progressSeconds"`
	// TODO: 添加gomongo时间戳支持
	// ModifiedTime    time.Time          `json:"modifiedTime,omitempty"`
}
