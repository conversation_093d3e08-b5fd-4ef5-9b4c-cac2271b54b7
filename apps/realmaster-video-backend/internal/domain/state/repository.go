package state

import (
	"context"
	"fmt"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
)

// Repository 定义状态的数据访问接口
type Repository interface {
	Upsert(ctx context.Context, state *State) error
	FindByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) (*State, error)
	FindBatchByUser(ctx context.Context, userID primitive.ObjectID, videoIDs []primitive.ObjectID) ([]State, error)
	FindUserFavorites(ctx context.Context, userID primitive.ObjectID, page, limit int64) ([]State, int64, error)
	DeleteByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) error
}

type repository struct {
	collection *gomongo.MongoCollection
}

// NewRepository 创建新的状态仓库实例
func NewRepository() Repository {
	collection := database.GetCollection("realmaster_video", "video_states")
	return &repository{collection: collection}
}

// Upsert 创建或更新状态记录
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) Upsert(ctx context.Context, state *State) error {
	filter := bson.M{"_id": state.ID}
	// 明确指定要更新的字段，让gomongo自动管理时间戳
	update := bson.M{
		"$set": bson.M{
			"liked":   state.Liked,
			"faved":   state.Favorited,
			"blkd":    state.Blocked,
			"progSec": state.ProgressSeconds,
		},
	}

	opts := options.Update().SetUpsert(true)
	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		logger.Log.Error("更新状态失败",
			logger.Error(err),
			logger.String("userId", state.ID.UserID.Hex()),
			logger.String("videoId", state.ID.VideoID.Hex()),
		)
		return fmt.Errorf("更新状态失败: %w", err)
	}

	if result.UpsertedCount > 0 {
		logger.Log.Info("成功创建状态记录",
			logger.String("userId", state.ID.UserID.Hex()),
			logger.String("videoId", state.ID.VideoID.Hex()),
		)
	} else {
		logger.Log.Info("成功更新状态记录",
			logger.String("userId", state.ID.UserID.Hex()),
			logger.String("videoId", state.ID.VideoID.Hex()),
		)
	}

	return nil
}

// FindByUserAndVideo 根据用户ID和视频ID查询状态
func (r *repository) FindByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) (*State, error) {
	filter := bson.M{
		"_id": StateID{
			UserID:  userID,
			VideoID: videoID,
		},
	}

	var state State
	err := r.collection.FindOne(ctx, filter).Decode(&state)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil // 没有找到记录，返回nil而不是错误
		}
		logger.Log.Error("查询状态失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
			logger.String("videoId", videoID.Hex()),
		)
		return nil, fmt.Errorf("查询状态失败: %w", err)
	}

	return &state, nil
}

// FindBatchByUser 批量查询用户对多个视频的状态
func (r *repository) FindBatchByUser(ctx context.Context, userID primitive.ObjectID, videoIDs []primitive.ObjectID) ([]State, error) {
	// 构建查询条件：用户ID匹配且视频ID在给定列表中
	var stateIDs []StateID
	for _, videoID := range videoIDs {
		stateIDs = append(stateIDs, StateID{
			UserID:  userID,
			VideoID: videoID,
		})
	}

	filter := bson.M{"_id": bson.M{"$in": stateIDs}}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		logger.Log.Error("批量查询状态失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
			logger.Int("videoCount", len(videoIDs)),
		)
		return nil, fmt.Errorf("批量查询状态失败: %w", err)
	}
	defer cursor.Close(ctx)

	var states []State
	if err := cursor.All(ctx, &states); err != nil {
		logger.Log.Error("解码状态列表失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, fmt.Errorf("解码状态列表失败: %w", err)
	}

	return states, nil
}

// FindUserFavorites 查询用户收藏的视频
func (r *repository) FindUserFavorites(ctx context.Context, userID primitive.ObjectID, page, limit int64) ([]State, int64, error) {
	filter := bson.M{
		"_id.uid": userID,
		"faved":   true,
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		logger.Log.Error("统计用户收藏数量失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, 0, fmt.Errorf("统计用户收藏数量失败: %w", err)
	}

	// 查询分页数据
	queryOpts := gomongo.QueryOptions{
		Sort: bson.D{{Key: "_mt", Value: -1}}, // 按修改时间倒序
	}

	if limit > 0 {
		queryOpts.Limit = limit
		if page > 1 {
			queryOpts.Skip = (page - 1) * limit
		}
	}

	cursor, err := r.collection.Find(ctx, filter, queryOpts)
	if err != nil {
		logger.Log.Error("查询用户收藏失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, 0, fmt.Errorf("查询用户收藏失败: %w", err)
	}
	defer cursor.Close(ctx)

	var states []State
	if err := cursor.All(ctx, &states); err != nil {
		logger.Log.Error("解码用户收藏列表失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
		)
		return nil, 0, fmt.Errorf("解码用户收藏列表失败: %w", err)
	}

	return states, total, nil
}

// DeleteByUserAndVideo 删除特定用户和视频的状态记录
// 支持事务：如果ctx是SessionContext，操作将在事务中执行
func (r *repository) DeleteByUserAndVideo(ctx context.Context, userID, videoID primitive.ObjectID) error {
	filter := bson.M{
		"_id": StateID{
			UserID:  userID,
			VideoID: videoID,
		},
	}

	// 使用传入的context（可能是SessionContext）
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		logger.Log.Error("删除状态失败",
			logger.Error(err),
			logger.String("userId", userID.Hex()),
			logger.String("videoId", videoID.Hex()),
		)
		return fmt.Errorf("删除状态失败: %w", err)
	}

	if result.DeletedCount == 0 {
		logger.Log.Warn("未找到要删除的状态记录",
			logger.String("userId", userID.Hex()),
			logger.String("videoId", videoID.Hex()),
		)
	} else {
		logger.Log.Info("成功删除状态记录",
			logger.String("userId", userID.Hex()),
			logger.String("videoId", videoID.Hex()),
		)
	}

	return nil
}
