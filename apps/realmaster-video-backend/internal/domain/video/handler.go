package video

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/uploader"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goauth/middleware"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Handler 处理视频相关的HTTP请求
type Handler struct {
	service       Service
	draftDir      string // 用于草稿文件存储的目录
	uploadService *uploader.GoUploadService
}

// NewHandler 创建一个新的视频处理器实例
// 需要一个用于存储草稿文件的目录路径
func NewHandler(service Service, draftDir string, cfg *config.Config) (*Handler, error) {
	if draftDir == "" {
		return nil, errors.New("草稿目录draftDir不能为空")
	}
	// 确保草稿目录存在
	if err := os.MkdirAll(draftDir, 0755); err != nil {
		return nil, fmt.Errorf("创建草稿目录失败: %w", err)
	}

	// 初始化 goupload 服务
	uploadService, err := uploader.NewGoUploadService(cfg.UserUpload.Site)
	if err != nil {
		return nil, fmt.Errorf("初始化上传服务失败: %w", err)
	}

	return &Handler{
		service:       service,
		draftDir:      draftDir,
		uploadService: uploadService,
	}, nil
}

// CreateDraft 处理新视频草稿的创建，支持传统上传和分块上传两种模式
func (h *Handler) CreateDraft(c *gin.Context) {
	contentType := c.GetHeader("Content-Type")

	if strings.Contains(contentType, "multipart/form-data") {
		// 传统上传模式：处理文件上传
		h.handleMultipartUpload(c)
	} else if strings.Contains(contentType, "application/json") {
		// 分块上传模式：处理已上传文件的路径
		h.handleChunkedUploadResult(c)
	} else {
		common.SendError(c, http.StatusBadRequest, "不支持的Content-Type",
			"请使用multipart/form-data或application/json")
		return
	}
}

// handleMultipartUpload 处理传统的multipart文件上传
func (h *Handler) handleMultipartUpload(c *gin.Context) {
	// 限制内存使用，防止大文件占用过多内存
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB内存限制
		common.SendError(c, http.StatusRequestEntityTooLarge,
			"文件过大，请使用分块上传",
			"建议文件大小小于10MB，大文件请使用chunked upload API")
		return
	}

	// 2. 处理文件上传使用 goupload
	thumbFile, thumbHeader, err := c.Request.FormFile("thumbnail")
	if err != nil && !errors.Is(err, http.ErrMissingFile) {
		common.SendError(c, http.StatusBadRequest, "无法获取封面文件", err.Error())
		return
	}
	videoFile, videoHeader, err := c.Request.FormFile("video")
	if err != nil && !errors.Is(err, http.ErrMissingFile) {
		common.SendError(c, http.StatusBadRequest, "无法获取视频文件", err.Error())
		return
	}

	// 检查文件大小限制（真正的文件大小检查）
	const maxFileSize = 10 << 20 // 10MB
	if thumbFile != nil && thumbHeader.Size > maxFileSize {
		common.SendError(c, http.StatusRequestEntityTooLarge,
			"封面文件过大，请使用分块上传",
			fmt.Sprintf("封面文件大小 %.2f MB 超过限制 10MB，大文件请使用chunked upload API", float64(thumbHeader.Size)/(1<<20)))
		return
	}
	if videoFile != nil && videoHeader.Size > maxFileSize {
		common.SendError(c, http.StatusRequestEntityTooLarge,
			"视频文件过大，请使用分块上传",
			fmt.Sprintf("视频文件大小 %.2f MB 超过限制 10MB，大文件请使用chunked upload API", float64(videoHeader.Size)/(1<<20)))
		return
	}

	var thumbPath, videoPath string

	// 获取用户ID (从JWT中获取)
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 使用 goupload 上传缩略图到草稿目录
	if thumbFile != nil {
		defer thumbFile.Close()
		thumbResult, err := h.uploadService.UploadThumbnailDraft(
			c.Request.Context(),
			userID,
			thumbFile,
			thumbHeader.Filename,
			thumbHeader.Size,
		)
		if err != nil {
			common.SendError(c, http.StatusInternalServerError, "上传封面文件失败", err.Error())
			return
		}
		thumbPath = thumbResult.Path
	}

	// 使用 goupload 上传视频文件到草稿目录
	if videoFile != nil {
		defer videoFile.Close()
		videoResult, err := h.uploadService.UploadVideoDraft(
			c.Request.Context(),
			userID,
			videoFile,
			videoHeader.Filename,
			videoHeader.Size,
		)
		if err != nil {
			common.SendError(c, http.StatusInternalServerError, "上传视频文件失败", err.Error())
			return
		}
		videoPath = videoResult.Path
	}

	// 3. 从表单字段解析元数据
	var req CreateVideoRequest
	metadataJSON := c.PostForm("metadata")
	if metadataJSON != "" {
		// 将 JSON 字符串解析到请求结构体中
		if err := json.Unmarshal([]byte(metadataJSON), &req); err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的元数据JSON格式", err.Error())
			return
		}
	}

	// 4. 设置goupload路径字段
	// 优先使用从metadata中传入的路径（分块上传场景），如果没有则使用当前上传的文件路径
	if req.DraftThumbGouploadPath == "" {
		req.DraftThumbGouploadPath = thumbPath
	}
	if req.DraftVideoGouploadPath == "" {
		req.DraftVideoGouploadPath = videoPath
	}
	req.PublishNow, _ = strconv.ParseBool(c.PostForm("publishNow"))

	// 5. 设置UploaderID为JWT中的userID（不管前端是否传入，都使用JWT中的userID）
	req.UploaderID = userID

	// 5. 调用服务
	video, err := h.service.CreateDraft(c.Request.Context(), req)
	if err != nil {
		// 这里可以添加更具体的错误处理
		common.SendError(c, http.StatusInternalServerError, "创建视频草稿失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusCreated, video)
}

// handleChunkedUploadResult 处理分块上传完成后的视频记录创建
func (h *Handler) handleChunkedUploadResult(c *gin.Context) {
	var req struct {
		CreateVideoRequest
		DraftVideoGouploadPath string `json:"draftVideoGouploadPath" binding:"required"` // goupload返回的相对路径
		DraftThumbGouploadPath string `json:"draftThumbGouploadPath"`                    // goupload返回的相对路径
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	// 获取用户ID (从JWT中获取)
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 直接设置goupload路径到请求中
	req.CreateVideoRequest.DraftVideoGouploadPath = req.DraftVideoGouploadPath
	req.CreateVideoRequest.DraftThumbGouploadPath = req.DraftThumbGouploadPath

	// 设置UploaderID为JWT中的userID（不管前端是否传入，都使用JWT中的userID）
	req.CreateVideoRequest.UploaderID = userID

	// PublishNow字段现在支持从JSON中自动解析，无需手动设置

	// 调用服务创建视频记录
	video, err := h.service.CreateDraft(c.Request.Context(), req.CreateVideoRequest)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "创建视频草稿失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusCreated, video)
}

// UploadThumbnail 处理单独的缩略图上传
func (h *Handler) UploadThumbnail(c *gin.Context) {
	// 解析multipart表单，限制为5MB
	if err := c.Request.ParseMultipartForm(5 << 20); err != nil {
		common.SendError(c, http.StatusBadRequest, "无法解析表单", err.Error())
		return
	}

	// 获取缩略图文件
	thumbFile, thumbHeader, err := c.Request.FormFile("thumbnail")
	if err != nil {
		common.SendError(c, http.StatusBadRequest, "无法获取缩略图文件", err.Error())
		return
	}
	defer thumbFile.Close()

	// 获取用户ID (从JWT中获取)
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	// 使用goupload上传缩略图到草稿目录
	thumbResult, err := h.uploadService.UploadThumbnailDraft(
		c.Request.Context(),
		userID,
		thumbFile,
		thumbHeader.Filename,
		thumbHeader.Size,
	)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "上传缩略图失败", err.Error())
		return
	}

	// 返回上传结果
	common.SendSuccess(c, http.StatusOK, gin.H{
		"path":     thumbResult.Path,
		"filename": thumbHeader.Filename,
		"size":     thumbHeader.Size,
	})
}

// PublishVideo 处理发布视频的请求
func (h *Handler) PublishVideo(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		common.SendError(c, http.StatusBadRequest, "视频ID不能为空", "video ID is empty")
		return
	}

	err := h.service.PublishVideo(c.Request.Context(), videoID)
	if err != nil {
		if errors.Is(err, ErrVideoNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrInvalidVideoStatus) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "发布视频失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusAccepted, gin.H{"message": "视频已提交，等待处理"})
}

// UpdateVideoStats 处理更新视频统计数据的请求
func (h *Handler) UpdateVideoStats(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		common.SendError(c, http.StatusBadRequest, "视频ID不能为空", "video ID is empty")
		return
	}

	var req UpdateStatsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的请求数据", err.Error())
		return
	}

	err := h.service.UpdateStats(c.Request.Context(), videoID, req)
	if err != nil {
		if errors.Is(err, ErrVideoNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		// 可以根据 service 层返回的具体错误类型，返回不同的 HTTP 状态码
		common.SendError(c, http.StatusInternalServerError, "更新视频统计失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, gin.H{"message": "统计数据更新成功"})
}

// FindVideos handles the request to get a list of videos with filters.
func (h *Handler) FindVideos(c *gin.Context) {
	var filter VideoFilter

	// 解析分页参数
	paginationParams := common.ParsePaginationFromQueryWithType(c, common.PaginationTypeVideo)
	filter.Page = paginationParams.Page
	filter.Limit = paginationParams.Limit

	// 解析过滤参数
	if catIDStr := c.Query("categoryId"); catIDStr != "" {
		catID, err := primitive.ObjectIDFromHex(catIDStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的分类ID格式", err.Error())
			return
		}
		filter.CategoryID = &catID
	}

	if clientIDStr := c.Query("clientId"); clientIDStr != "" {
		if clientIDStr == "none" {
			filter.FilterForNullClient = true
		} else {
			clientID, err := primitive.ObjectIDFromHex(clientIDStr)
			if err != nil {
				common.SendError(c, http.StatusBadRequest, "无效的客户ID格式", err.Error())
				return
			}
			filter.ClientID = &clientID
		}
	}

	if statuses := c.QueryArray("status"); len(statuses) > 0 {
		// 这里可以添加对status值的验证，确保是合法的状态
		filter.Status = statuses
	}

	// 解析时间范围参数 (RFC3339 format, e.g., "2006-01-02T15:04:05Z")
	if fromStr := c.Query("from"); fromStr != "" {
		from, err := time.Parse(time.RFC3339, fromStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的起始时间格式", err.Error())
			return
		}
		filter.FromDate = &from
	}

	if toStr := c.Query("to"); toStr != "" {
		to, err := time.Parse(time.RFC3339, toStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的截止时间格式", err.Error())
			return
		}
		filter.ToDate = &to
	}

	// 调用 service
	videos, pagination, err := h.service.FindVideos(c.Request.Context(), filter)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "获取视频列表失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, gin.H{
		"items": videos,
		"pgn":   pagination,
	})
}

// GetVideoStats handles the request to get aggregated statistics for videos based on filters.
func (h *Handler) GetVideoStats(c *gin.Context) {
	var filter VideoFilter

	// This part is identical to FindVideos to ensure consistent filtering.
	if catIDStr := c.Query("categoryId"); catIDStr != "" {
		catID, err := primitive.ObjectIDFromHex(catIDStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的分类ID格式", err.Error())
			return
		}
		filter.CategoryID = &catID
	}

	if clientIDStr := c.Query("clientId"); clientIDStr != "" {
		if clientIDStr == "none" {
			filter.FilterForNullClient = true
		} else {
			clientID, err := primitive.ObjectIDFromHex(clientIDStr)
			if err != nil {
				common.SendError(c, http.StatusBadRequest, "无效的客户ID格式", err.Error())
				return
			}
			filter.ClientID = &clientID
		}
	}

	if statuses := c.QueryArray("status"); len(statuses) > 0 {
		filter.Status = statuses
	}

	if fromStr := c.Query("from"); fromStr != "" {
		from, err := time.Parse(time.RFC3339, fromStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的起始时间格式", err.Error())
			return
		}
		filter.FromDate = &from
	}

	if toStr := c.Query("to"); toStr != "" {
		to, err := time.Parse(time.RFC3339, toStr)
		if err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的截止时间格式", err.Error())
			return
		}
		filter.ToDate = &to
	}

	// Call the service layer to get the aggregated stats
	stats, err := h.service.GetVideoStats(c.Request.Context(), filter)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "获取视频统计数据失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, stats)
}

// GetByID handles the request to get a single video by its ID.
// @Summary Get video by ID
// @Description Retrieves detailed information for a single video.
// @Tags videos
// @Accept json
// @Produce json
// @Param id path string true "Video ID"
// @Success 200 {object} common.APIResponse{data=VideoResponse}
// @Failure 400 {object} common.APIResponse "Invalid ID format"
// @Failure 404 {object} common.APIResponse "Video not found"
// @Failure 500 {object} common.APIResponse "Internal server error"
// @Router /api/video/admin/videos/{id} [get]
func (h *Handler) GetByID(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		common.SendError(c, http.StatusBadRequest, "视频ID不能为空", "ID is empty in path")
		return
	}

	video, err := h.service.GetVideoByID(c.Request.Context(), videoID)
	if err != nil {
		if errors.Is(err, ErrVideoNotFound) {
			common.SendError(c, http.StatusNotFound, "视频未找到", err.Error())
			return
		}
		// This also catches invalid ID format from the repository layer
		if errors.Is(err, ErrInvalidVideoID) {
			common.SendError(c, http.StatusBadRequest, "无效的视频ID格式", err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "获取视频失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, video)
}

// DeleteVideo handles the request to delete a video.
func (h *Handler) DeleteVideo(c *gin.Context) {
	videoID := c.Param("id")

	err := h.service.DeleteVideo(c.Request.Context(), videoID)
	if err != nil {
		if errors.Is(err, ErrVideoNotFound) {
			common.SendError(c, http.StatusNotFound, "视频未找到", err.Error())
			return
		}
		if errors.Is(err, ErrCannotDeleteProcessing) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "删除视频失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, nil, "视频删除成功")
}

// UpdateVideo handles PATCH requests to update a video.
func (h *Handler) UpdateVideo(c *gin.Context) {
	videoID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的视频ID", err.Error())
		return
	}

	contentType := c.GetHeader("Content-Type")
	var req UpdateVideoRequest

	// 根据Content-Type处理不同类型的请求
	if strings.Contains(contentType, "multipart/form-data") {
		// 场景1: 传统表单上传 (小文件)
		if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max memory
			common.SendError(c, http.StatusBadRequest, "无法解析表单数据", err.Error())
			return
		}

		req = UpdateVideoRequest{ID: videoID}

		// 1. Decode metadata from form field
		metadataJSON := c.PostForm("metadata")
		if metadataJSON != "" {
			var metadata map[string]interface{}
			if err := json.Unmarshal([]byte(metadataJSON), &metadata); err != nil {
				common.SendError(c, http.StatusBadRequest, "无效的元数据格式 (非JSON)", err.Error())
				return
			}
			req.Metadata = metadata
		}

		// 2. Get action from form field
		req.Action = c.PostForm("action")

		// 3. Get potential file uploads and check file size limits
		videoFile, err := c.FormFile("video")
		if err != nil && !errors.Is(err, http.ErrMissingFile) {
			common.SendError(c, http.StatusBadRequest, "无法处理视频文件上传", err.Error())
			return
		}

		thumbFile, err := c.FormFile("thumbnail")
		if err != nil && !errors.Is(err, http.ErrMissingFile) {
			common.SendError(c, http.StatusBadRequest, "无法处理封面文件上传", err.Error())
			return
		}

		// 检查文件大小限制（与CreateDraft保持一致）
		const maxFileSize = 10 << 20 // 10MB
		if videoFile != nil && videoFile.Size > maxFileSize {
			common.SendError(c, http.StatusRequestEntityTooLarge,
				"视频文件过大，请使用分块上传",
				fmt.Sprintf("视频文件大小 %.2f MB 超过限制 10MB，大文件请使用chunked upload API", float64(videoFile.Size)/(1<<20)))
			return
		}
		if thumbFile != nil && thumbFile.Size > maxFileSize {
			common.SendError(c, http.StatusRequestEntityTooLarge,
				"封面文件过大，请使用分块上传",
				fmt.Sprintf("封面文件大小 %.2f MB 超过限制 10MB，大文件请使用chunked upload API", float64(thumbFile.Size)/(1<<20)))
			return
		}

		req.DraftVideoFile = videoFile
		req.DraftThumbnailFile = thumbFile

	} else if strings.Contains(contentType, "application/json") {
		// 场景2: 分块上传完成后的JSON请求
		var jsonReq struct {
			Metadata               map[string]interface{} `json:"metadata"`
			Action                 string                 `json:"action"`
			DraftVideoGouploadPath string                 `json:"draftVideoGouploadPath"`
			DraftThumbGouploadPath string                 `json:"draftThumbGouploadPath"`
		}

		if err := c.ShouldBindJSON(&jsonReq); err != nil {
			common.SendError(c, http.StatusBadRequest, "无效的JSON请求体", err.Error())
			return
		}

		req = UpdateVideoRequest{
			ID:                     videoID,
			Metadata:               jsonReq.Metadata,
			Action:                 jsonReq.Action,
			DraftVideoGouploadPath: jsonReq.DraftVideoGouploadPath,
			DraftThumbGouploadPath: jsonReq.DraftThumbGouploadPath,
			// DraftVideoFile and DraftThumbnailFile will be nil
		}

	} else {
		common.SendError(c, http.StatusBadRequest, "不支持的Content-Type", "请使用 multipart/form-data 或 application/json")
		return
	}

	// Call service
	updatedVideo, err := h.service.UpdateVideo(c.Request.Context(), req)
	if err != nil {
		if errors.Is(err, ErrVideoInProcessing) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrVideoNotFound) {
			common.SendError(c, http.StatusNotFound, err.Error(), err.Error())
			return
		}
		if errors.Is(err, common.ErrInvalidCategoryID) || errors.Is(err, common.ErrInvalidClientID) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrInvalidRepublishOperation) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "更新视频失败", err.Error())
		return
	}

	// Convert the updated video model to a response DTO before sending
	videoResponse, err := h.service.GetVideoByID(c.Request.Context(), updatedVideo.ID.Hex())
	if err != nil {
		// This should ideally not happen if the update was successful, but handle it just in case
		common.SendError(c, http.StatusInternalServerError, "获取更新后的视频信息失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, videoResponse)
}

// 注意：移除了ServeTempContent方法，现在通过goupload生成的URL直接访问预览文件

// InitiateChunkedUpload 初始化分块上传
func (h *Handler) InitiateChunkedUpload(c *gin.Context) {
	var req struct {
		Filename  string `json:"filename" binding:"required"`
		TotalSize int64  `json:"totalSize" binding:"required"`
		FileType  string `json:"fileType" binding:"required"` // "video" or "thumbnail"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	// 获取用户ID (从JWT中获取)
	userID, err := middleware.RequireLogin(c)
	if err != nil {
		logger.Log.Error("获取用户ID失败", logger.Error(err))
		common.SendError(c, http.StatusUnauthorized, "需要登录", err.Error())
		return
	}

	uploadID, err := h.uploadService.InitiateChunkedVideoUpload(
		c.Request.Context(),
		userID,
		req.Filename,
		req.TotalSize,
	)

	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "初始化分块上传失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, gin.H{
		"uploadId": uploadID,
	}, "分块上传初始化成功")
}

// UploadChunk 上传文件分块
func (h *Handler) UploadChunk(c *gin.Context) {
	uploadID := c.Param("uploadId")
	chunkNumberStr := c.Param("chunkNumber")

	if uploadID == "" || chunkNumberStr == "" {
		common.SendError(c, http.StatusBadRequest, "缺少必要参数", "uploadId和chunkNumber不能为空")
		return
	}

	chunkNumber, err := strconv.Atoi(chunkNumberStr)
	if err != nil {
		common.SendError(c, http.StatusBadRequest, "分块编号无效", err.Error())
		return
	}

	// 获取上传的分块数据
	file, _, err := c.Request.FormFile("chunk")
	if err != nil {
		common.SendError(c, http.StatusBadRequest, "无法获取分块数据", err.Error())
		return
	}
	defer file.Close()

	err = h.uploadService.UploadChunk(c.Request.Context(), uploadID, chunkNumber, file)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "分块上传失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, nil, "分块上传成功")
}

// CompleteChunkedUpload 完成分块上传
func (h *Handler) CompleteChunkedUpload(c *gin.Context) {
	var req struct {
		UploadID       string `json:"uploadId" binding:"required"`
		ExpectedChunks int    `json:"expectedChunks" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	result, err := h.uploadService.CompleteChunkedVideoUpload(
		c.Request.Context(),
		req.UploadID,
		req.ExpectedChunks,
	)

	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "完成分块上传失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, gin.H{
		"path": result.Path,
		"size": result.Size,
	}, "分块上传完成")
}
