package video

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/testutil"
)

func TestVideoRepository_Create(t *testing.T) {
	// 设置测试环境
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	// 创建repository实例
	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功创建视频", func(t *testing.T) {
		// 准备测试数据
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "测试视频标题",
				En: "Test Video Title",
			},
			Description: MultilingualString{
				Zh: "测试视频描述",
				En: "Test Video Description",
			},
			Duration:    120.5,
			UploaderID:  "test-uploader",
			CategoryID:  primitive.NewObjectID(),
			Tags:        []string{"测试", "视频"},
			Status:      StatusDraft,
			PropertyIDs: []string{"prop-123"},
			ExternalURL: "https://example.com/property/123",
			ClientID:    primitive.NewObjectID(),
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
			// 使用新的goupload字段
			DraftVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			DraftThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
		}

		// 执行创建操作
		err := repo.Create(ctx, video)

		// 验证结果
		require.NoError(t, err)
		assert.False(t, video.ID.IsZero())

		// 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.Title.Zh, foundVideo.Title.Zh)
		assert.Equal(t, video.Status, foundVideo.Status)
		assert.Equal(t, video.Duration, foundVideo.Duration)
	})

	t.Run("创建视频时需要提供ID", func(t *testing.T) {
		video := &Video{
			ID: primitive.NewObjectID(), // 手动生成ID
			Title: MultilingualString{
				Zh: "手动ID测试",
				En: "Manual ID Test",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, video)

		require.NoError(t, err)
		assert.False(t, video.ID.IsZero())

		// 验证能够通过ID找到创建的视频
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.ID, foundVideo.ID)
	})
}

func TestVideoRepository_FindByID(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功查找存在的视频", func(t *testing.T) {
		// 先创建一个视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "查找测试视频",
				En: "Find Test Video",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 通过ID查找
		foundVideo, err := repo.FindByID(ctx, originalVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, foundVideo)
		assert.Equal(t, originalVideo.ID, foundVideo.ID)
		assert.Equal(t, originalVideo.Title.Zh, foundVideo.Title.Zh)
		assert.Equal(t, originalVideo.Status, foundVideo.Status)
	})

	t.Run("查找不存在的视频返回错误", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		video, err := repo.FindByID(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
		assert.Nil(t, video)
	})

	t.Run("无效ID格式返回错误", func(t *testing.T) {
		invalidID := "invalid-id-format"

		video, err := repo.FindByID(ctx, invalidID)

		assert.Error(t, err)
		assert.Nil(t, video)
	})
}

func TestVideoRepository_Update(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("成功更新视频", func(t *testing.T) {
		// 创建原始视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "原始标题",
				En: "Original Title",
			},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 更新视频
		originalVideo.Title.Zh = "更新后的标题"
		originalVideo.Status = StatusPending
		originalVideo.Duration = 300.0

		err = repo.Update(ctx, originalVideo)
		require.NoError(t, err)

		// 验证更新结果
		updatedVideo, err := repo.FindByID(ctx, originalVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, "更新后的标题", updatedVideo.Title.Zh)
		assert.Equal(t, StatusPending, updatedVideo.Status)
		assert.Equal(t, 300.0, updatedVideo.Duration)
	})

	t.Run("更新不存在的视频", func(t *testing.T) {
		nonExistentVideo := &Video{
			ID:         primitive.NewObjectID(),
			Status:     StatusDraft,
			UploaderID: "test-user",
		}

		err := repo.Update(ctx, nonExistentVideo)

		// 根据实际实现，更新不存在的视频应该返回错误
		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})
}

func TestVideoRepository_Find(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	ctx := context.Background()

	t.Run("按状态过滤查找视频", func(t *testing.T) {
		// 创建不同状态的视频
		draftVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "草稿视频", En: "Draft Video"},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}
		publishedVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "已发布视频", En: "Published Video"},
			Status:     StatusPublished,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)
		err = repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 查找已发布的视频
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  10,
		}

		videos, total, err := repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, videos, 1)
		assert.Equal(t, StatusPublished, videos[0].Status)
		assert.Equal(t, "已发布视频", videos[0].Title.Zh)
	})

	t.Run("分页查询", func(t *testing.T) {
		// 先清理数据库，确保测试隔离
		suite.CleanupDatabase()

		// 创建多个视频
		for i := 0; i < 5; i++ {
			video := &Video{
				ID:         primitive.NewObjectID(),
				Title:      MultilingualString{Zh: fmt.Sprintf("分页视频%d", i), En: fmt.Sprintf("PagingVideo%d", i)},
				Status:     StatusPublished,
				UploaderID: "test-user",
			}
			err := repo.Create(ctx, video)
			require.NoError(t, err)
		}

		// 第一页，每页2个
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  2,
		}

		videos, total, err := repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(5), total)
		assert.Len(t, videos, 2)

		// 第二页
		filter.Page = 2
		videos, total, err = repo.Find(ctx, filter)

		require.NoError(t, err)
		assert.Equal(t, int64(5), total)
		assert.Len(t, videos, 2)
	})
}
