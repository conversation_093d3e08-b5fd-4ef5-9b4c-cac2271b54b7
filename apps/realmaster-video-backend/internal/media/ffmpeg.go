package media

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"realmaster-video-backend/internal/platform/logger"

)

// TranscodingProfile 定义了单个转码流的参数
type TranscodingProfile struct {
	Name         string // e.g., "720p"
	Width        int
	Height       int
	VideoBitrate string // e.g., "2000k"
	AudioBitrate string // e.g., "128k"
}

// DefaultProfiles 返回一个默认的转码配置列表
func DefaultProfiles() []TranscodingProfile {
	return []TranscodingProfile{
		{Name: "1080p", Width: 1920, Height: 1080, VideoBitrate: "5000k", AudioBitrate: "192k"},
		{Name: "720p", Width: 1280, Height: 720, VideoBitrate: "2500k", AudioBitrate: "128k"},
		{Name: "480p", Width: 854, Height: 480, VideoBitrate: "1000k", AudioBitrate: "96k"},
		{Name: "360p", Width: 640, Height: 360, VideoBitrate: "600k", AudioBitrate: "64k"},
	}
}

// TranscodeResult 包含转码后的文件信息
type TranscodeResult struct {
	AudioOutputPath   string            // 单独的音频文件路径
	VideoOutputs      map[string]string // map[profileName] -> video-only outputPath
	PackagingDir      string            // 用于存放打包后内容的新目录
	ManifestsBaseName string            // MPD和M3U8清单文件的基础名称
}

// Transcode 使用 ffmpeg 将输入视频文件转码为单独的音频和多个视频流
func Transcode(ctx context.Context, inputPath string) (*TranscodeResult, error) {
	sourceDir := filepath.Dir(inputPath)
	baseName := strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))

	// 创建一个新的子目录来存放打包内容，避免混乱
	packagingDir := filepath.Join(sourceDir, baseName+"_packaged")
	if err := os.MkdirAll(packagingDir, 0755); err != nil {
		return nil, fmt.Errorf("创建打包目录失败: %w", err)
	}

	result := &TranscodeResult{
		VideoOutputs:      make(map[string]string),
		PackagingDir:      packagingDir,
		ManifestsBaseName: baseName,
	}

	// 1. 检查是否存在音轨，如果存在则转码
	hasAudio, err := HasAudioStream(ctx, inputPath)
	if err != nil {
		return nil, fmt.Errorf("检查音轨失败: %w", err)
	}

	if hasAudio {
		logger.Log.Info("检测到音轨，开始提取音频。")
		// 转码生成一个高质量的音频文件 (不含视频)
		audioOutputPath := filepath.Join(packagingDir, baseName+"_audio.mp4")
		audioArgs := []string{
			"-i", inputPath,
			"-vn", // No video
			"-c:a", "aac",
			"-b:a", "192k",
			"-y",
			audioOutputPath,
		}
		if err := runFFmpegCommand(ctx, "audio", audioArgs); err != nil {
			return nil, err
		}
		result.AudioOutputPath = audioOutputPath
	} else {
		logger.Log.Info("未检测到音轨，跳过音频提取。")
		result.AudioOutputPath = "" // 明确设置为空字符串
	}

	// 2. 为每个 profile 转码生成一个不含音频的视频文件
	profiles := DefaultProfiles()
	for _, profile := range profiles {
		outputFileName := fmt.Sprintf("%s_%s.mp4", baseName, profile.Name)
		outputPath := filepath.Join(packagingDir, outputFileName)
		result.VideoOutputs[profile.Name] = outputPath

		// -an: No audio
		videoArgs := []string{
			"-i", inputPath,
			"-vf", fmt.Sprintf("scale=%d:%d", profile.Width, profile.Height),
			"-c:v", "libx264",
			"-b:v", profile.VideoBitrate,
			"-an", // No audio
			"-y",
			outputPath,
		}

		if err := runFFmpegCommand(ctx, profile.Name, videoArgs); err != nil {
			// 清理已生成的音视频文件
			_ = os.Remove(result.AudioOutputPath)
			for _, p := range result.VideoOutputs {
				_ = os.Remove(p)
			}
			return nil, err
		}
	}

	return result, nil
}

// runFFmpegCommand 是一个辅助函数，用于执行具体的ffmpeg命令
func runFFmpegCommand(ctx context.Context, profileName string, args []string) error {
	cmd := exec.CommandContext(ctx, "ffmpeg", args...)

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	logger.Log.Info("开始执行 ffmpeg...",
		logger.String("profile", profileName),
		logger.String("command", cmd.String()),
	)

	if err := cmd.Run(); err != nil {
		// 注意: ffmpeg 成功执行时也可能向 stderr 输出大量信息，所以只有在返回 error 时才记录 stderr
		return fmt.Errorf("ffmpeg %s 处理失败: %v, stderr: %s", profileName, err, stderr.String())
	}

	logger.Log.Info("ffmpeg 处理成功", logger.String("profile", profileName))
	return nil
}
