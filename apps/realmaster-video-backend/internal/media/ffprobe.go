package media

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"time"
)

// FFProbeStream 定义了 ffprobe JSON 输出中 'streams' 数组的元素结构
type FFProbeStream struct {
	CodecType string `json:"codec_type"`
	Width     int    `json:"width"`
	Height    int    `json:"height"`
}

// FFProbeFormat 定义了 ffprobe JSON 输出中 'format' 对象的结构
type FFProbeFormat struct {
	DurationStr string `json:"duration"`
}

// FFProbeOutput 是 ffprobe 命令完整 JSON 输出的顶层结构
type FFProbeOutput struct {
	Streams []FFProbeStream `json:"streams"`
	Format  FFProbeFormat   `json:"format"`
}

// Metadata 是我们希望从 ffprobe 中提取并使用的核心视频元数据
type Metadata struct {
	Duration        time.Duration
	DurationSeconds float64
	Width           int
	Height          int
}

// GetMetadata 使用 ffprobe 命令行工具来提取视频的元数据。
// 它需要系统中已安装 ffprobe。
func GetMetadata(ctx context.Context, filePath string) (*Metadata, error) {
	ffprobeData, err := Probe(ctx, filePath)
	if err != nil {
		return nil, fmt.Errorf("无法探测文件: %w", err)
	}

	// 提取并转换数据
	var videoStream *FFProbeStream
	for i, s := range ffprobeData.Streams {
		if s.CodecType == "video" {
			videoStream = &ffprobeData.Streams[i]
			break
		}
	}

	if videoStream == nil {
		return nil, fmt.Errorf("在 ffprobe 输出中未找到视频流")
	}

	durationFloat, err := strconv.ParseFloat(ffprobeData.Format.DurationStr, 64)
	if err != nil {
		return nil, fmt.Errorf("解析视频时长 '%s' 失败: %w", ffprobeData.Format.DurationStr, err)
	}

	metadata := &Metadata{
		Duration:        time.Duration(durationFloat * float64(time.Second)),
		DurationSeconds: durationFloat,
		Width:           videoStream.Width,
		Height:          videoStream.Height,
	}

	return metadata, nil
}

// HasAudioStream uses ffprobe to check if the file has at least one audio stream.
func HasAudioStream(ctx context.Context, filePath string) (bool, error) {
	probe, err := Probe(ctx, filePath)
	if err != nil {
		return false, fmt.Errorf("无法探测文件: %w", err)
	}

	for _, stream := range probe.Streams {
		if stream.CodecType == "audio" {
			return true, nil // Found an audio stream
		}
	}

	return false, nil // No audio stream found
}

// Probe runs ffprobe on a file and returns the parsed JSON output.
func Probe(ctx context.Context, filePath string) (*FFProbeOutput, error) {
	// 设置命令执行的超时
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 构建 ffprobe 命令
	cmd := exec.CommandContext(ctx, "ffprobe",
		"-v", "error",
		"-show_format",
		"-show_streams",
		"-of", "json",
		filePath,
	)

	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr

	// 执行命令
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("ffprobe 执行失败: %w, details: %s", err, stderr.String())
	}

	// 解析 JSON 输出
	var ffprobeData FFProbeOutput
	if err := json.Unmarshal(out.Bytes(), &ffprobeData); err != nil {
		return nil, fmt.Errorf("解析 ffprobe JSON 输出失败: %w", err)
	}

	return &ffprobeData, nil
}
