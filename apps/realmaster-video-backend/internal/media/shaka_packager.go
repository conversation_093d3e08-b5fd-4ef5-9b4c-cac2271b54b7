package media

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"

	"realmaster-video-backend/internal/platform/logger"

)

// PackageResult 包含打包操作的产物
type PackageResult struct {
	HLSManifestPath string
}

// Package 使用 Shaka Packager 将转码后的文件打包成 HLS 格式
func Package(ctx context.Context, transcodeResult *TranscodeResult) (*PackageResult, error) {
	args := []string{}

	// 如果存在音频文件，则添加音频流
	if transcodeResult.AudioOutputPath != "" {
		audioOutputStreamName := strings.TrimSuffix(filepath.Base(transcodeResult.AudioOutputPath), filepath.Ext(transcodeResult.AudioOutputPath))
		audioOutputFinalPath := filepath.Join(transcodeResult.PackagingDir, audioOutputStreamName+".mp4")
		args = append(args, fmt.Sprintf("stream=audio,in=%s,output=%s", transcodeResult.AudioOutputPath, audioOutputFinalPath))
	}

	// 添加所有视频流
	// stream=video,in=...,out=...
	for _, videoPath := range transcodeResult.VideoOutputs {
		videoOutputStreamName := strings.TrimSuffix(filepath.Base(videoPath), filepath.Ext(videoPath))
		videoOutputFinalPath := filepath.Join(transcodeResult.PackagingDir, videoOutputStreamName+".mp4")
		args = append(args, fmt.Sprintf("stream=video,in=%s,output=%s", videoPath, videoOutputFinalPath))
	}

	// 定义HLS输出清单文件
	hlsManifestPath := filepath.Join(transcodeResult.PackagingDir, transcodeResult.ManifestsBaseName+".m3u8")

	args = append(args, "--hls_master_playlist_output", hlsManifestPath)

	// 执行 packager 命令
	cmd := exec.CommandContext(ctx, "packager", args...)

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	logger.Log.Info("开始执行 Shaka Packager 打包...",
		logger.String("command", cmd.String()),
	)

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("shaka packager 执行失败: %v, stderr: %s", err, stderr.String())
	}

	logger.Log.Info("Shaka Packager 打包成功",
		logger.String("hls_output", hlsManifestPath),
	)

	result := &PackageResult{
		HLSManifestPath: hlsManifestPath,
	}

	return result, nil
}
