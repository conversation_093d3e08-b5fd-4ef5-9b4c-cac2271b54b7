package database

import (
	"context"

	"realmaster-video-backend/internal/platform/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateIndexes 使用gomongo创建索引
func CreateIndexes(ctx context.Context) {
	createCategoryIndexes(ctx)
	createAdvertiserIndexes(ctx)
	createVideoIndexes(ctx)
	createInteractionIndexes(ctx)
	createStateIndexes(ctx)
}

func createCategoryIndexes(ctx context.Context) {
	collection := GetCollection("realmaster_video", "video_categories")

	models := []mongo.IndexModel{
		{ // 名称唯一
			Keys:    bson.D{bson.E{Key: "nm", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{ // 排序字段索引，便于分页排序
			Keys: bson.D{bson.E{Key: "ord", Value: 1}},
		},
	}

	indexNames, err := collection.CreateIndexes(ctx, models)
	if err != nil {
		logger.Log.Warn("无法为分类创建索引（可能已存在）", logger.Error(err))
		return
	}

	logger.Log.Info("创建分类索引成功", logger.Any("indexNames", indexNames))
}

func createAdvertiserIndexes(ctx context.Context) {
	collection := GetCollection("realmaster_video", "video_advertisers")

	models := []mongo.IndexModel{
		{
			Keys:    bson.D{bson.E{Key: "nm", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{bson.E{Key: "ph", Value: 1}},
			Options: options.Index().SetUnique(true).SetSparse(true), // Sparse for optional unique fields
		},
		{
			Keys:    bson.D{bson.E{Key: "em", Value: 1}},
			Options: options.Index().SetUnique(true).SetSparse(true),
		},
		{ // 列表分页默认排序字段索引
			Keys: bson.D{bson.E{Key: "_ts", Value: -1}},
		},
	}

	indexNames, err := collection.CreateIndexes(ctx, models)
	if err != nil {
		logger.Log.Warn("无法为广告主创建索引（可能已存在）", logger.Error(err))
		return
	}

	logger.Log.Info("创建广告主索引成功", logger.Any("indexNames", indexNames))
}

func createVideoIndexes(ctx context.Context) {
	collection := GetCollection("realmaster_video", "video_videos")

	models := []mongo.IndexModel{
		{
			// Index for finding videos by client (advertiser)
			Keys: bson.D{bson.E{Key: "ClntId", Value: 1}},
		},
		{
			// Index for finding videos by category
			Keys: bson.D{bson.E{Key: "catId", Value: 1}},
		},
		{
			// Compound index for typical dashboard queries: filter by status, sort by publish date
			Keys: bson.D{
				bson.E{Key: "st", Value: 1},
				bson.E{Key: "pts", Value: -1},
			},
		},
	}

	indexNames, err := collection.CreateIndexes(ctx, models)
	if err != nil {
		logger.Log.Warn("无法为视频创建索引（可能已存在）", logger.Error(err))
		return
	}
	logger.Log.Info("创建视频索引成功", logger.Any("indexNames", indexNames))
}

func createInteractionIndexes(ctx context.Context) {
	collection := GetCollection("realmaster_video", "video_interactions")

	models := []mongo.IndexModel{
		{
			// Index for finding interactions by user
			Keys: bson.D{bson.E{Key: "uid", Value: 1}, bson.E{Key: "_ts", Value: -1}},
		},
		{
			// Index for finding interactions by video
			Keys: bson.D{bson.E{Key: "vid", Value: 1}, bson.E{Key: "_ts", Value: -1}},
		},
		{
			// Index for finding interactions by type
			Keys: bson.D{bson.E{Key: "tp", Value: 1}, bson.E{Key: "_ts", Value: -1}},
		},
		{
			// Compound index for video stats aggregation
			Keys: bson.D{bson.E{Key: "vid", Value: 1}, bson.E{Key: "tp", Value: 1}},
		},
	}

	indexNames, err := collection.CreateIndexes(ctx, models)
	if err != nil {
		logger.Log.Warn("无法为交互事件创建索引（可能已存在）", logger.Error(err))
		return
	}
	logger.Log.Info("创建交互事件索引成功", logger.Any("indexNames", indexNames))
}

func createStateIndexes(ctx context.Context) {
	collection := GetCollection("realmaster_video", "video_states")

	models := []mongo.IndexModel{
		{
			// Index for finding user favorites
			Keys: bson.D{bson.E{Key: "_id.uid", Value: 1}, bson.E{Key: "faved", Value: 1}, bson.E{Key: "_mt", Value: -1}},
		},
		{
			// Index for finding user states by video list
			Keys: bson.D{bson.E{Key: "_id.uid", Value: 1}, bson.E{Key: "_id.vid", Value: 1}},
		},
		{
			// Index for finding blocked videos
			Keys: bson.D{bson.E{Key: "_id.uid", Value: 1}, bson.E{Key: "blkd", Value: 1}},
		},
	}

	indexNames, err := collection.CreateIndexes(ctx, models)
	if err != nil {
		logger.Log.Warn("无法为状态创建索引（可能已存在）", logger.Error(err))
		return
	}
	logger.Log.Info("创建状态索引成功", logger.Any("indexNames", indexNames))
}
