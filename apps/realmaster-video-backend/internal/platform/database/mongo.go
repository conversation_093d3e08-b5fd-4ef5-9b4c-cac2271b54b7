package database

import (
	"fmt"

	"github.com/real-rm/gomongo"

	"realmaster-video-backend/internal/platform/logger"
)

// InitMongoDB 初始化gomongo数据库连接
// gomongo会自动从config.toml读取数据库配置
func InitMongoDB() error {
	err := gomongo.InitMongoDB()
	if err != nil {
		return fmt.Errorf("初始化gomongo失败: %w", err)
	}

	logger.Log.Info("gomongo初始化成功")
	return nil
}

// GetCollection 获取指定数据库和集合的实例
func GetCollection(dbName, collectionName string) *gomongo.MongoCollection {
	return gomongo.Coll(dbName, collectionName)
}
