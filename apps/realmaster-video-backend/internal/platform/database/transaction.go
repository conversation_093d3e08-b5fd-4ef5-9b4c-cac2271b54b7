package database

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/platform/logger"
)

// TransactionManager 处理事务的环境适配
type TransactionManager struct {
	dbName             string
	transactionSupport bool
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(dbName string, transactionSupport bool) *TransactionManager {
	return &TransactionManager{
		dbName:             dbName,
		transactionSupport: transactionSupport,
	}
}

// WithTransaction 根据环境配置决定是否使用事务
//
// 重大发现：gomongo完全支持事务！
// 1. gomongo.MongoCollection.GetClient() 返回底层 *mongo.Client
// 2. MongoDB驱动完全支持事务，包括单实例事务
// 3. MongoDB 8.0.9完全支持单实例事务
// 4. 可以通过gomongo实现真正的事务管理
//
// 重要提醒：使用事务时，repository层必须使用传入的SessionContext，
// 而不是原始的context，否则操作不会在事务中执行！
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	if !tm.transactionSupport {
		// 在不支持事务的环境中，按顺序执行操作
		logger.Log.Debug("事务不支持，按顺序执行操作",
			logger.String("dbName", tm.dbName))
		return fn(ctx)
	}

	// 实现真正的事务支持
	logger.Log.Debug("开始执行事务",
		logger.String("dbName", tm.dbName))

	// 获取MongoDB客户端 - 使用已存在的集合
	collection := GetCollection(tm.dbName, "video_videos") // 使用已知存在的集合
	if collection == nil {
		return fmt.Errorf("无法获取数据库连接: %s", tm.dbName)
	}

	client := collection.GetClient()
	if client == nil {
		return fmt.Errorf("无法获取MongoDB客户端: %s", tm.dbName)
	}

	// 创建会话并执行事务
	session, err := client.StartSession()
	if err != nil {
		logger.Log.Error("创建会话失败",
			logger.String("dbName", tm.dbName),
			logger.Error(err))
		return fmt.Errorf("创建会话失败: %w", err)
	}
	defer session.EndSession(ctx)

	// 使用WithTransaction执行事务
	// 关键修复：确保事务上下文被正确传递
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 传递SessionContext而不是原始context
		// 这确保所有数据库操作都在事务中执行
		return nil, fn(sessCtx)
	})

	if err != nil {
		logger.Log.Error("事务执行失败",
			logger.String("dbName", tm.dbName),
			logger.Error(err))
		return fmt.Errorf("事务执行失败: %w", err)
	}

	logger.Log.Debug("事务执行成功",
		logger.String("dbName", tm.dbName))
	return nil
}

// IsTransactionSupported 返回当前环境是否支持事务
func (tm *TransactionManager) IsTransactionSupported() bool {
	return tm.transactionSupport
}

// IsInTransaction 检查给定的context是否是事务上下文
// 这个函数可以帮助repository层确认是否在事务中执行
func IsInTransaction(ctx context.Context) bool {
	_, ok := ctx.(mongo.SessionContext)
	return ok
}

// GetSessionFromContext 从context中获取MongoDB会话
// 如果context不是SessionContext，返回nil
func GetSessionFromContext(ctx context.Context) mongo.Session {
	if sessCtx, ok := ctx.(mongo.SessionContext); ok {
		return sessCtx
	}
	return nil
}
