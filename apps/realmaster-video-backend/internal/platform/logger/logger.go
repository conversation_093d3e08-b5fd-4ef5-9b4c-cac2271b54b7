package logger

import (
	"fmt"

	"github.com/real-rm/golog"
)

// LoggerWrapper 包装golog以提供兼容的接口
type LoggerWrapper struct{}

var (
	// Log 是全局日志记录器实例，提供与zap兼容的接口
	Log *LoggerWrapper
)

// NewLogger 初始化golog并返回包装器
// golog会自动从config.toml读取[golog]配置，无需传入参数
func NewLogger() (*LoggerWrapper, error) {
	// golog会自动从config.toml读取配置，我们只需要初始化
	// 如果需要指定配置文件路径，可以设置环境变量 RMBASE_FILE_CFG
	err := golog.InitLog()
	if err != nil {
		// 如果初始化失败，可能是配置文件问题，我们提供一个友好的错误信息
		return nil, fmt.Errorf("golog初始化失败: %w (请确保config.toml存在且包含[golog]配置)", err)
	}

	// 创建包装器实例
	wrapper := &LoggerWrapper{}

	// 设置全局实例
	Log = wrapper

	return wrapper, nil
}

// Sync 同步日志缓冲区 - golog会自动处理
func Sync() error {
	// golog会自动处理同步，这里保持兼容性
	return nil
}

// 以下方法提供与zap.Logger兼容的接口

// Debug 记录调试级别日志
func (l *LoggerWrapper) Debug(msg string, fields ...interface{}) {
	convertedFields := convertFields(fields...)
	golog.Debug(msg, convertedFields...)
}

// Info 记录信息级别日志
func (l *LoggerWrapper) Info(msg string, fields ...interface{}) {
	convertedFields := convertFields(fields...)
	golog.Info(msg, convertedFields...)
}

// Warn 记录警告级别日志
func (l *LoggerWrapper) Warn(msg string, fields ...interface{}) {
	convertedFields := convertFields(fields...)
	golog.Warn(msg, convertedFields...)
}

// Error 记录错误级别日志
func (l *LoggerWrapper) Error(msg string, fields ...interface{}) {
	convertedFields := convertFields(fields...)
	golog.Error(msg, convertedFields...)
}

// Fatal 记录致命错误级别日志
func (l *LoggerWrapper) Fatal(msg string, fields ...interface{}) {
	convertedFields := convertFields(fields...)
	golog.Fatal(msg, convertedFields...)
}

// 以下是zap字段类型的兼容函数，用于转换现有代码中的zap字段

// ZapField 表示一个zap字段
type ZapField struct {
	Key   string
	Value interface{}
}

// convertFields 将ZapField切片转换为golog可用的键值对
func convertFields(fields ...interface{}) []interface{} {
	var result []interface{}
	for _, field := range fields {
		if zapField, ok := field.(ZapField); ok {
			result = append(result, zapField.Key, zapField.Value)
		} else {
			result = append(result, field)
		}
	}
	return result
}
