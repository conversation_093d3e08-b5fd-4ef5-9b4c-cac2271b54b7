package logger

// 这个文件提供zap兼容的函数，使现有代码无需大量修改即可使用golog

// String 创建字符串字段 - zap兼容
func String(key, value string) ZapField {
	return ZapField{Key: key, Value: value}
}

// Int 创建整数字段 - zap兼容
func Int(key string, value int) ZapField {
	return ZapField{Key: key, Value: value}
}

// Int64 创建64位整数字段 - zap兼容
func Int64(key string, value int64) ZapField {
	return ZapField{Key: key, Value: value}
}

// Error 创建错误字段 - zap兼容
func Error(err error) ZapField {
	if err == nil {
		return ZapField{Key: "error", Value: "<nil>"}
	}
	return ZapField{Key: "error", Value: err.Error()}
}

// Bool 创建布尔字段 - zap兼容
func Bool(key string, value bool) ZapField {
	return ZapField{Key: key, Value: value}
}

// Float64 创建浮点数字段 - zap兼容
func Float64(key string, value float64) ZapField {
	return ZapField{Key: key, Value: value}
}

// Duration 创建时间间隔字段 - zap兼容
func Duration(key string, value interface{}) ZapField {
	return ZapField{Key: key, Value: value}
}

// Any 创建任意类型字段 - zap兼容
func Any(key string, value interface{}) ZapField {
	return ZapField{Key: key, Value: value}
}
