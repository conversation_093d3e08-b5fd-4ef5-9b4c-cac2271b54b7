package server

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/real-rm/goauth/controller"
	"github.com/real-rm/goauth/middleware"

	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/category"
	"realmaster-video-backend/internal/domain/interaction"
	"realmaster-video-backend/internal/domain/main_property"
	"realmaster-video-backend/internal/domain/public"
	"realmaster-video-backend/internal/domain/video"
)

// RegisterCategoryRoutes 注册分类相关的路由
func RegisterCategoryRoutes(router gin.IRouter, handler *category.Handler) {
	// 分类管理路由
	categories := router.Group("/categories")
	{
		categories.GET("/", handler.ListCategories)
		categories.POST("/", handler.CreateCategory)
		categories.PUT("/:id", handler.UpdateCategory)
		categories.DELETE("/:id", handler.DeleteCategory)
	}
}

// RegisterAdvertiserRoutes 注册广告主相关的路由
func RegisterAdvertiserRoutes(router gin.IRouter, handler *advertiser.Handler) {
	advertisers := router.Group("/advertisers")
	{
		advertisers.GET("", handler.List)
		advertisers.GET("/:id", handler.GetByID)
		advertisers.POST("", handler.Create)
		advertisers.PATCH("/:id", handler.Update)
		advertisers.DELETE("/:id", handler.Delete)
	}
}

// RegisterMainPropertyRoutes 注册房源相关的路由
func RegisterMainPropertyRoutes(router gin.IRouter, propertyHandler *main_property.Handler) {
	api := router.Group("/properties")
	{
		api.POST("/search", propertyHandler.SearchByKeyword)
		api.POST("/batch-get", propertyHandler.GetByIDs)
		api.GET("/:id", propertyHandler.GetByID)
	}
}

// RegisterVideoRoutes registers routes for the video domain.
func RegisterVideoRoutes(router gin.IRouter, handler *video.Handler) {
	// 管理后台 API
	adminVideoRoutes := router.Group("/videos")
	{
		// Stats endpoint should be before the one with path parameter (`/:id`)
		adminVideoRoutes.GET("/stats", handler.GetVideoStats)
		adminVideoRoutes.GET("", handler.FindVideos)
		adminVideoRoutes.GET("/", handler.FindVideos)
		adminVideoRoutes.POST("/", handler.CreateDraft)
		adminVideoRoutes.DELETE("/:id", handler.DeleteVideo)
		adminVideoRoutes.PATCH("/:id", handler.UpdateVideo)
		adminVideoRoutes.GET("/:id", handler.GetByID)
		adminVideoRoutes.POST("/:id/publish", handler.PublishVideo)
		adminVideoRoutes.PATCH("/:id/stats", handler.UpdateVideoStats)

		// 单独的缩略图上传路由（用于大文件分块上传场景）
		adminVideoRoutes.POST("/upload-thumbnail", handler.UploadThumbnail)
	}

	// 注意：移除了临时文件服务路由，现在通过goupload生成的URL直接访问

	// 分块上传路由
	chunkedUpload := adminVideoRoutes.Group("/chunked-upload")
	{
		chunkedUpload.POST("/initiate", handler.InitiateChunkedUpload)
		chunkedUpload.POST("/:uploadId/chunk/:chunkNumber", handler.UploadChunk)
		chunkedUpload.POST("/complete", handler.CompleteChunkedUpload)
	}
}

// 注意：移除了内部文件上传路由，现在完全使用goupload

// RegisterPublicRoutes 注册公共API路由
func RegisterPublicRoutes(router gin.IRouter, handler *public.Handler) {
	// Feed接口
	router.GET("/feed", handler.GetFeed)

	// 用户状态接口
	router.POST("/states/batch", handler.GetUserStates)
	router.GET("/states", handler.GetUserStatesQuery)

	// 用户收藏接口
	router.GET("/favorites", handler.GetUserFavorites)
}

// RegisterInteractionRoutes 注册交互事件路由
func RegisterInteractionRoutes(router gin.IRouter, handler *interaction.Handler) {
	// 交互事件接口
	router.POST("/interactions", handler.CreateInteraction)
	router.GET("/interactions/user", handler.GetUserInteractions)
	router.GET("/interactions/video/:videoId", handler.GetVideoInteractions)
}

// RegisterSystemRoutes 注册系统级路由
func RegisterSystemRoutes(router gin.IRouter) {
	// 注意：静态文件服务现在完全由nginx提供
	// - 草稿文件: nginx /draft/videos/, /draft/thumbnails/
	// - 最终文件: nginx /media/videos/, /media/thumbnails/, /media/avatars/
	// - Go后端只处理API逻辑，不再提供静态文件服务

	// 这里可以添加其他系统级路由，如健康检查等
	// router.GET("/health", healthCheckHandler)
}

// RegisterAuthRoutes 注册认证路由
func RegisterAuthRoutes(router gin.IRouter) {
	// 使用 goauth 的认证路由
	controller.Auth(router.Group("/auth"))
}

// RegisterDevRoutes 注册开发环境路由
func RegisterDevRoutes(router gin.IRouter) {
	devGroup := router.Group("/dev")
	{
		devGroup.GET("/jwt", func(c *gin.Context) {
			// 支持通过查询参数选择用户
			userParam := c.Query("user")

			var userID string
			var roles []string

			switch userParam {
			case "admin":
				userID = "dev_admin_001"
				roles = []string{"user", "admin"}
			case "user":
				userID = "dev_user_002"
				roles = []string{"user"}
			case "realtor":
				userID = "dev_realtor_003"
				roles = []string{"user", "realtor"}
			default:
				userID = "dev_user_123"
				roles = []string{"user", "admin"}
			}

			// 使用 goauth 的方式生成 JWT
			now := time.Now()
			expiresAt := now.Add(24 * time.Hour).Unix()

			// 创建 JWT claims
			claims := jwt.MapClaims{
				"sub":   userID,
				"roles": roles,
				"exp":   expiresAt,
				"iat":   now.Unix(),
			}

			token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
			jwtToken, err := token.SignedString([]byte(middleware.GetSecretKey()))
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "生成JWT失败"})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"jwt":        jwtToken,
				"expires_in": 24 * 3600, // 24小时，以秒为单位
				"user_id":    userID,
				"roles":      roles,
			})
		})
	}
}
