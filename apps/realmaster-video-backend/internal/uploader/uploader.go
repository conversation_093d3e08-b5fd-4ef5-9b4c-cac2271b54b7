package uploader

import (
	"context"
)

// UploadResult 包含单个文件上传后的信息
type UploadResult struct {
	// E.g., "my_video_1080p.mp4"
	FileName string
	// E.g., "http://files.example.com/videos/video123/my_video_1080p.mp4"
	URL string
	// E.g., "/videos/video123/my_video_1080p.mp4"
	Path string
}

// Uploader 定义了上传器的通用接口
// 这允许我们轻松地替换不同的实现 (e.g., HTTP, S3, GCS)
type Uploader interface {
	// UploadDirectory 遍历一个本地目录，并将其中所有文件上传到目标服务器的指定子目录中
	// sourcePath: 本地要上传的文件夹路径 (e.g., "/tmp/..._packaged")
	// targetSubDir: 在远程服务器上创建的子目录，通常是视频ID，用于隔离文件
	UploadDirectory(ctx context.Context, sourcePath string, targetSubDir string) ([]UploadResult, error)

	// UploadFile 上传单个文件
	// sourceFilePath: 本地要上传的单个文件的路径
	// targetSubDir: 远程子目录
	UploadFile(ctx context.Context, sourceFilePath string, targetSubDir string) (*UploadResult, error)
}
