#!/bin/bash

# RealMaster 开发环境启动脚本
# 基于实际goupload配置启动nginx静态文件服务

echo "🚀 启动RealMaster开发环境..."

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "❌ nginx未安装，请先安装nginx"
    echo "Ubuntu/Debian: sudo apt install nginx"
    echo "macOS: brew install nginx"
    exit 1
fi

# 检查配置文件
if [ ! -f "nginx.conf" ]; then
    echo "❌ 找不到nginx.conf配置文件"
    exit 1
fi

# 创建goupload存储目录（基于config.toml配置）
echo "📁 检查并创建存储目录..."

# 草稿阶段目录
sudo mkdir -p /var/www/draft/rm_video_drafts
sudo mkdir -p /var/www/draft/rm_thumbnail_drafts

# 最终媒体目录
sudo mkdir -p /var/www/media/rm_videos
sudo mkdir -p /var/www/media/rm_thumbnails
sudo mkdir -p /var/www/media/rm_avatars

# 设置权限（开发环境）
sudo chmod -R 755 /var/www/draft/
sudo chmod -R 755 /var/www/media/

echo "✅ 存储目录创建完成"

# 停止可能运行的nginx进程
echo "🛑 停止现有nginx进程..."
sudo pkill -f "nginx.*nginx.conf" 2>/dev/null || true

# 启动nginx
echo "🌐 启动nginx (端口3000)..."
sudo nginx -c "$(pwd)/nginx.conf" -g "daemon off;" &

NGINX_PID=$!

echo "✅ Nginx已启动!"
echo ""
echo "📂 静态文件服务路径:"
echo "   草稿视频: http://localhost:3000/draft/videos/"
echo "   草稿缩略图: http://localhost:3000/draft/thumbnails/"
echo "   最终视频: http://localhost:3000/media/videos/"
echo "   最终缩略图: http://localhost:3000/media/thumbnails/"
echo "   客户头像: http://localhost:3000/media/avatars/"
echo ""
echo "🔧 配置文件: $(pwd)/nginx.conf"
echo "🗂️  存储目录:"
echo "   /var/www/draft/ (草稿文件)"
echo "   /var/www/media/ (最终文件)"
echo ""
echo "💡 使用说明:"
echo "   - Go后端API: http://localhost:8080"
echo "   - 前端通过nginx访问媒体文件"
echo "   - 按 Ctrl+C 停止服务"
echo ""

# 等待中断信号
trap "echo '🛑 停止nginx...'; sudo kill $NGINX_PID 2>/dev/null; exit 0" INT

wait $NGINX_PID
